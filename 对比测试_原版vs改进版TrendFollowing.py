# -*- coding: utf-8 -*-
"""
对比测试: 原版 vs 改进版 TrendFollowing策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import logging
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s]: %(message)s'
)

logger = logging.getLogger(__name__)

def compare_strategies():
    """对比原版和改进版TrendFollowing策略"""
    
    print("🔄 TrendFollowing策略对比测试")
    print("=" * 60)
    print("对比策略:")
    print("  1. TrendFollowing (原版)")
    print("  2. ImprovedTrendFollowingStrategy (改进版)")
    print("=" * 60)
    
    try:
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-10'  # 扩展测试期
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 测试策略列表
        strategies_to_test = [
            {
                'name': 'TrendFollowing',
                'class': STRATEGIES.get('TrendFollowing'),
                'params': {
                    'sma_short': 10,
                    'sma_long': 30,
                    'rsi_threshold': 45,
                    'risk_per_trade_pct': 0.02,
                    'atr_sl_multiple': 1.5,
                    'atr_tp_multiple': 3.0,
                    'min_signal_interval_minutes': 30
                }
            },
            {
                'name': 'ImprovedTrendFollowingStrategy',
                'class': STRATEGIES.get('ImprovedTrendFollowingStrategy'),
                'params': {
                    'sma_short': 12,
                    'sma_long': 26,
                    'rsi_overbought': 75,
                    'rsi_oversold': 25,
                    'adx_threshold': 30,
                    'adx_rising_periods': 3,
                    'min_momentum_threshold': 0.002,
                    'volume_threshold': 1.5,
                    'risk_per_trade_pct': 0.008,
                    'initial_sl_atr_multiple': 2.5,
                    'trailing_sl_atr_multiple': 1.8,
                    'tp_level_1': 2.0,
                    'tp_level_2': 4.0,
                    'tp_level_3': 6.0,
                    'max_consecutive_losses': 3,
                    'min_signal_interval_minutes': 120,
                    'max_holding_hours': 48
                }
            }
        ]
        
        results_comparison = []
        
        for strategy_info in strategies_to_test:
            strategy_name = strategy_info['name']
            strategy_class = strategy_info['class']
            strategy_params = strategy_info['params']
            
            if not strategy_class:
                print(f"❌ 策略 {strategy_name} 不可用")
                continue
            
            print(f"\n[测试] {strategy_name}")
            print("-" * 40)
            
            start_time = time.time()
            
            # 创建回测引擎
            backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
            
            # 运行回测
            results = backtester.run_backtest(config.start_date, config.end_date)
            
            execution_time = time.time() - start_time
            
            if results:
                # 提取关键指标
                strategy_result = {
                    'strategy_name': strategy_name,
                    'total_return_pct': results.get('总收益率', 0) * 100,
                    'total_trades': results.get('总交易次数', 0),
                    'winning_trades': results.get('盈利次数', 0),
                    'losing_trades': results.get('亏损次数', 0),
                    'win_rate': results.get('胜率', 0) * 100,
                    'profit_loss_ratio': results.get('盈亏比', 0),
                    'sharpe_ratio': results.get('夏普比率', 0),
                    'max_drawdown_pct': results.get('最大回撤率', 0) * 100,
                    'annual_return_pct': results.get('年化收益率', 0) * 100,
                    'execution_time': execution_time
                }
                
                results_comparison.append(strategy_result)
                
                print(f"✅ 完成 - 收益率: {strategy_result['total_return_pct']:.2f}%")
                print(f"   交易次数: {strategy_result['total_trades']}")
                print(f"   胜率: {strategy_result['win_rate']:.1f}%")
                print(f"   夏普比率: {strategy_result['sharpe_ratio']:.2f}")
                print(f"   最大回撤: {strategy_result['max_drawdown_pct']:.2f}%")
                
            else:
                print(f"❌ 回测失败")
        
        # 生成对比报告
        if len(results_comparison) >= 2:
            print(f"\n📊 策略对比报告")
            print("=" * 80)
            
            # 创建对比表格
            df_comparison = pd.DataFrame(results_comparison)
            
            # 显示详细对比
            print(f"{'指标':<20} {'原版TrendFollowing':<20} {'改进版':<20} {'改进幅度':<15}")
            print("-" * 80)
            
            metrics_to_compare = [
                ('总收益率(%)', 'total_return_pct'),
                ('年化收益率(%)', 'annual_return_pct'),
                ('总交易次数', 'total_trades'),
                ('胜率(%)', 'win_rate'),
                ('盈亏比', 'profit_loss_ratio'),
                ('夏普比率', 'sharpe_ratio'),
                ('最大回撤(%)', 'max_drawdown_pct'),
                ('执行时间(秒)', 'execution_time')
            ]
            
            original = results_comparison[0]
            improved = results_comparison[1]
            
            for metric_name, metric_key in metrics_to_compare:
                orig_value = original[metric_key]
                impr_value = improved[metric_key]
                
                # 计算改进幅度
                if orig_value != 0:
                    improvement = ((impr_value - orig_value) / abs(orig_value)) * 100
                else:
                    improvement = 0 if impr_value == 0 else float('inf')
                
                # 格式化显示
                if isinstance(orig_value, float):
                    orig_str = f"{orig_value:.2f}"
                    impr_str = f"{impr_value:.2f}"
                else:
                    orig_str = str(orig_value)
                    impr_str = str(impr_value)
                
                if improvement == float('inf'):
                    impr_str_display = "∞"
                else:
                    impr_str_display = f"{improvement:+.1f}%"
                
                print(f"{metric_name:<20} {orig_str:<20} {impr_str:<20} {impr_str_display:<15}")
            
            # 总结
            print(f"\n🎯 改进效果总结:")
            print("-" * 40)
            
            # 关键改进指标
            return_improvement = ((improved['total_return_pct'] - original['total_return_pct']) / 
                                abs(original['total_return_pct'])) * 100 if original['total_return_pct'] != 0 else 0
            
            winrate_improvement = improved['win_rate'] - original['win_rate']
            sharpe_improvement = improved['sharpe_ratio'] - original['sharpe_ratio']
            
            print(f"📈 收益率改进: {return_improvement:+.1f}%")
            print(f"🎯 胜率提升: {winrate_improvement:+.1f}个百分点")
            print(f"📊 夏普比率改进: {sharpe_improvement:+.2f}")
            
            # 判断改进效果
            if (improved['total_return_pct'] > original['total_return_pct'] and 
                improved['win_rate'] > original['win_rate'] and
                improved['sharpe_ratio'] > original['sharpe_ratio']):
                print(f"\n🎉 改进版策略在所有关键指标上都优于原版！")
            elif improved['total_return_pct'] > original['total_return_pct']:
                print(f"\n✅ 改进版策略在收益率上优于原版")
            else:
                print(f"\n⚠️ 改进版策略需要进一步优化")
        
        return results_comparison
        
    except Exception as e:
        logger.error(f"对比测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    try:
        results = compare_strategies()
        
        if results:
            print(f"\n💾 对比测试完成！")
        else:
            print(f"\n❌ 对比测试失败")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
