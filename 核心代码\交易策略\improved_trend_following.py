# -*- coding: utf-8 -*-
"""
改进版趋势跟踪策略 - ImprovedTrendFollowingStrategy
基于原版TrendFollowing的问题分析，实施多重改进
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class ImprovedTrendFollowingStrategy:
    """
    改进版趋势跟踪策略
    
    主要改进:
    1. 多重信号确认机制
    2. 动态止损止盈
    3. 智能仓位管理
    4. 市场环境识别
    """
    
    def __init__(self, backtester=None, symbols=None, params=None):
        # 兼容回测引擎的初始化
        if params is None:
            params = {}

        # 基础参数
        self.strategy_name = "ImprovedTrendFollowingStrategy"
        self.backtester = backtester
        self.symbols = symbols or ['BTCUSDT']
        
        # 技术指标参数
        self.sma_short = params.get('sma_short', 12)
        self.sma_long = params.get('sma_long', 26)
        self.rsi_period = params.get('rsi_period', 14)
        self.rsi_overbought = params.get('rsi_overbought', 75)
        self.rsi_oversold = params.get('rsi_oversold', 25)
        self.atr_period = params.get('atr_period', 14)
        self.adx_period = params.get('adx_period', 14)
        
        # 信号确认参数
        self.adx_threshold = params.get('adx_threshold', 30)
        self.adx_rising_periods = params.get('adx_rising_periods', 3)
        self.min_momentum_threshold = params.get('min_momentum_threshold', 0.002)
        self.volume_threshold = params.get('volume_threshold', 1.5)
        self.signal_confirmation_bars = params.get('signal_confirmation_bars', 2)
        
        # 风险管理参数
        self.risk_per_trade_pct = params.get('risk_per_trade_pct', 0.008)
        self.initial_sl_atr_multiple = params.get('initial_sl_atr_multiple', 2.5)
        self.trailing_sl_atr_multiple = params.get('trailing_sl_atr_multiple', 1.8)
        self.tp_level_1 = params.get('tp_level_1', 2.0)
        self.tp_level_2 = params.get('tp_level_2', 4.0)
        self.tp_level_3 = params.get('tp_level_3', 6.0)
        
        # 仓位管理参数
        self.max_position_size = params.get('max_position_size', 0.15)
        self.max_consecutive_losses = params.get('max_consecutive_losses', 3)
        self.loss_reduction_factor = params.get('loss_reduction_factor', 0.5)
        
        # 时间控制
        self.min_signal_interval_minutes = params.get('min_signal_interval_minutes', 120)
        self.max_holding_hours = params.get('max_holding_hours', 48)
        
        # 状态跟踪
        self.last_signal_time = {}
        self.consecutive_losses = 0
        self.position_entry_time = {}
        self.position_partial_exits = {}
        
        logger.info(f"{self.strategy_name}: 改进版趋势跟踪策略初始化完成")
        logger.info(f"ADX阈值: {self.adx_threshold}, 确认周期: {self.adx_rising_periods}")
        logger.info(f"动态止损: {self.initial_sl_atr_multiple}-{self.trailing_sl_atr_multiple}倍ATR")
        logger.info(f"分级止盈: {self.tp_level_1}/{self.tp_level_2}/{self.tp_level_3}倍ATR")
    
    def calculate_market_regime(self, data: pd.DataFrame, current_time: pd.Timestamp) -> str:
        """识别市场环境"""
        try:
            current_data = data.loc[current_time]
            adx = current_data.get('ADX_14', 0)
            
            # 计算波动率分位数
            lookback_data = data.loc[:current_time].tail(60)
            if len(lookback_data) >= 20:
                volatility = lookback_data['ATR_14'].rolling(20).std().iloc[-1]
                vol_percentile = (volatility > lookback_data['ATR_14'].rolling(60).quantile(0.8).iloc[-1])
            else:
                vol_percentile = False
            
            # 市场环境分类
            if adx > 35:
                return 'strong_trend'
            elif adx > 25:
                return 'moderate_trend'
            elif adx > 20:
                return 'weak_trend'
            else:
                return 'sideways'
                
        except Exception as e:
            logger.warning(f"市场环境识别失败: {e}")
            return 'unknown'
    
    def check_signal_quality(self, data: pd.DataFrame, current_time: pd.Timestamp, signal_type: str) -> bool:
        """多重信号质量检查"""
        try:
            current_data = data.loc[current_time]
            
            # 1. ADX趋势强度检查
            adx = current_data.get('ADX_14', 0)
            if adx < self.adx_threshold:
                return False
            
            # 2. ADX上升趋势检查
            if len(data.loc[:current_time]) >= self.adx_rising_periods:
                recent_adx = data.loc[:current_time]['ADX_14'].tail(self.adx_rising_periods)
                if not (recent_adx.iloc[-1] > recent_adx.iloc[0]):
                    return False
            
            # 3. 价格动量检查
            if len(data.loc[:current_time]) >= 5:
                recent_prices = data.loc[:current_time]['CLOSE'].tail(5)
                momentum = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0]
                if signal_type == 'buy' and momentum < self.min_momentum_threshold:
                    return False
                elif signal_type == 'sell' and momentum > -self.min_momentum_threshold:
                    return False
            
            # 4. 成交量确认
            volume = current_data.get('VOLUME', 0)
            if len(data.loc[:current_time]) >= 20:
                avg_volume = data.loc[:current_time]['VOLUME'].tail(20).mean()
                if volume < avg_volume * self.volume_threshold:
                    return False
            
            # 5. 波动率检查
            atr = current_data.get('ATR_14', 0)
            close_price = current_data.get('CLOSE', 0)
            if close_price > 0:
                volatility = atr / close_price
                if volatility < 0.01 or volatility > 0.05:  # 1%-5%波动率范围
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"信号质量检查失败: {e}")
            return False
    
    def calculate_position_size(self, data: pd.DataFrame, current_time: pd.Timestamp, 
                              signal_type: str, current_price: float) -> float:
        """智能仓位计算"""
        try:
            # 基础风险
            base_risk = self.risk_per_trade_pct
            
            # 连续亏损调整
            if self.consecutive_losses >= self.max_consecutive_losses:
                base_risk *= self.loss_reduction_factor
            
            # 市场环境调整
            market_regime = self.calculate_market_regime(data, current_time)
            regime_multipliers = {
                'strong_trend': 1.5,
                'moderate_trend': 1.0,
                'weak_trend': 0.7,
                'sideways': 0.3,
                'unknown': 0.5
            }
            base_risk *= regime_multipliers.get(market_regime, 1.0)
            
            # 波动率调整
            current_data = data.loc[current_time]
            atr = current_data.get('ATR_14', 0)
            if atr > 0 and current_price > 0:
                volatility = atr / current_price
                # 高波动率降低仓位，低波动率提高仓位
                vol_adjustment = max(0.5, min(1.5, 0.02 / volatility))
                base_risk *= vol_adjustment
            
            # 限制最大仓位
            final_risk = min(base_risk, self.max_position_size)
            
            return final_risk
            
        except Exception as e:
            logger.warning(f"仓位计算失败: {e}")
            return self.risk_per_trade_pct * 0.5
    
    def generate_signals(self, data: pd.DataFrame, current_time: pd.Timestamp, 
                        portfolio_value: float, positions: Dict) -> List[Dict]:
        """生成交易信号"""
        signals = []
        
        try:
            if current_time not in data.index:
                return signals
            
            current_data = data.loc[current_time]
            symbol = 'BTCUSDT'  # 主要交易标的
            
            # 检查信号间隔
            if symbol in self.last_signal_time:
                time_diff = (current_time - self.last_signal_time[symbol]).total_seconds() / 60
                if time_diff < self.min_signal_interval_minutes:
                    return signals
            
            # 获取技术指标
            sma_short = current_data.get('SMA_20', 0)  # 使用配置的SMA
            sma_long = current_data.get('SMA_60', 0)
            rsi = current_data.get('RSI_14', 50)
            adx = current_data.get('ADX_14', 0)
            atr = current_data.get('ATR_14', 0)
            close_price = current_data.get('CLOSE', 0)
            
            if close_price <= 0 or atr <= 0:
                return signals
            
            # 检查当前持仓
            current_position = positions.get(symbol, {})
            has_position = current_position.get('shares', 0) > 1e-9
            
            # 时间止损检查
            if has_position and symbol in self.position_entry_time:
                holding_hours = (current_time - self.position_entry_time[symbol]).total_seconds() / 3600
                if holding_hours > self.max_holding_hours:
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'price': close_price,
                        'size': current_position.get('shares', 0),
                        'timestamp': current_time,
                        'signal_type': 'time_stop',
                        'reason': f'持仓超过{self.max_holding_hours}小时'
                    })
                    return signals
            
            # 生成买入信号
            if not has_position and sma_short > sma_long and rsi < self.rsi_overbought:
                # 多重信号确认
                if self.check_signal_quality(data, current_time, 'buy'):
                    position_size = self.calculate_position_size(data, current_time, 'buy', close_price)
                    
                    if position_size > 0:
                        # 计算止损止盈
                        stop_loss = close_price - (atr * self.initial_sl_atr_multiple)
                        take_profit_1 = close_price + (atr * self.tp_level_1)
                        
                        signals.append({
                            'symbol': symbol,
                            'action': 'buy',
                            'price': close_price,
                            'size': position_size,
                            'timestamp': current_time,
                            'signal_type': 'improved_trend_entry',
                            'stop_loss': stop_loss,
                            'take_profit': take_profit_1,
                            'reason': f'改进趋势信号: SMA金叉, ADX={adx:.1f}, RSI={rsi:.1f}'
                        })
                        
                        self.last_signal_time[symbol] = current_time
                        self.position_entry_time[symbol] = current_time
                        self.position_partial_exits[symbol] = 0
            
            # 生成卖出信号
            elif has_position:
                should_exit = False
                exit_reason = ""
                
                # 趋势反转信号
                if sma_short < sma_long and rsi > self.rsi_oversold:
                    should_exit = True
                    exit_reason = "趋势反转"
                
                # ADX下降信号
                elif adx < 20:
                    should_exit = True
                    exit_reason = "趋势强度减弱"
                
                if should_exit:
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'price': close_price,
                        'size': current_position.get('shares', 0),
                        'timestamp': current_time,
                        'signal_type': 'improved_trend_exit',
                        'reason': exit_reason
                    })
                    
                    self.last_signal_time[symbol] = current_time
                    if symbol in self.position_entry_time:
                        del self.position_entry_time[symbol]
                    if symbol in self.position_partial_exits:
                        del self.position_partial_exits[symbol]
            
        except Exception as e:
            logger.error(f"生成信号时出错: {e}")
        
        return signals
    
    def update_performance(self, trade_result: Dict):
        """更新策略性能跟踪"""
        try:
            if trade_result.get('action') == 'sell' and 'realized_pnl' in trade_result:
                pnl = trade_result['realized_pnl']
                if pnl < 0:
                    self.consecutive_losses += 1
                else:
                    self.consecutive_losses = 0
                    
        except Exception as e:
            logger.warning(f"性能更新失败: {e}")

    def on_bar(self, current_bars_all_symbols: pd.DataFrame) -> List[Dict]:
        """回测引擎兼容接口"""
        try:
            if current_bars_all_symbols.empty:
                return []

            # 获取当前时间
            current_time = current_bars_all_symbols.index[0]

            # 获取组合价值和持仓信息
            if hasattr(self.backtester, 'portfolio'):
                portfolio_value = self.backtester.portfolio.get_total_value()
                positions = self.backtester.portfolio.positions
            else:
                portfolio_value = 100000  # 默认值
                positions = {}

            # 获取历史数据
            if hasattr(self.backtester, 'all_historical_data'):
                historical_data = self.backtester.all_historical_data.loc['BTCUSDT']
                # 只使用到当前时间的数据
                available_data = historical_data.loc[:current_time]
            else:
                available_data = current_bars_all_symbols

            # 生成信号
            signals = self.generate_signals(available_data, current_time, portfolio_value, positions)

            return signals

        except Exception as e:
            logger.error(f"on_bar方法执行失败: {e}")
            return []

# 注册策略
def get_strategy_class():
    return ImprovedTrendFollowingStrategy
