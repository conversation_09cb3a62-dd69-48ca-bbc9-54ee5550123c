# -*- coding: utf-8 -*-
import logging
from typing import Dict, Any, List, Optional
import pandas as pd

logger = logging.getLogger(__name__)

class Portfolio:
    """
    投资组合管理器 (V3 - 简化和修复版)
    """
    def __init__(self, config: Any):
        self.config_dict = config.__dict__ if hasattr(config, '__dict__') else config
        self.initial_capital: float = float(self.config_dict.get('initial_cash', 100000.0))
        self.current_cash: float = self.initial_capital
        self.positions: Dict[str, Dict[str, Any]] = {}
        self.trade_log: List[Dict] = []
        self.transaction_cost_pct: float = float(self.config_dict.get('transaction_cost_pct', 0.001))
        logger.info(f"组合管理器(V3)初始化: 初始资金={self.initial_capital:.2f}, 交易成本={self.transaction_cost_pct:.4%}")

    def get_position_size(self, symbol: str) -> float:
        return self.positions.get(symbol, {}).get('shares', 0.0)

    def get_portfolio_value(self, current_prices_snapshot: Optional[pd.Series] = None) -> Dict:
        positions_value = 0.0
        if self.positions and current_prices_snapshot is not None:
            for symbol, pos_details in self.positions.items():
                shares = pos_details.get('shares', 0.0)
                if abs(shares) > 1e-9:
                    current_price = current_prices_snapshot.get(symbol, pos_details.get('avg_price', 0.0))
                    positions_value += shares * current_price
        total_value = self.current_cash + positions_value
        return {'total': total_value, 'cash': self.current_cash, 'positions_value': positions_value}

    def execute_trade(self, signal: Dict):
        symbol, action, price, size, timestamp = signal['symbol'], signal['action'].lower(), signal['price'], signal['size'], signal['timestamp']
        trade_value, commission, realized_pnl = price * size, price * size * self.transaction_cost_pct, 0.0

        if action == 'buy':
            if self.current_cash < trade_value + commission:
                logger.warning(f"[{timestamp}] 现金不足买入 {symbol}，跳过交易。")
                return
            self.current_cash -= (trade_value + commission)
            self.positions[symbol] = {'shares': size, 'avg_price': price, 'stop_loss': signal.get('stop_loss_price'), 'take_profit': signal.get('take_profit_price'), 'entry_timestamp': timestamp}
            log_action = 'buy_to_open'
        elif action == 'sell':
            current_shares = self.get_position_size(symbol)
            if size > current_shares: size = current_shares
            if size <= 1e-9: return
            avg_price = self.positions[symbol]['avg_price']
            realized_pnl = (price - avg_price) * size - commission
            self.current_cash += (price * size - commission)
            del self.positions[symbol]
            log_action = 'sell_to_close'
        else: return

        self.trade_log.append({'timestamp': timestamp, 'symbol': symbol, 'action': log_action, 'price': price, 'size': size, 'commission': commission, 'realized_pnl': realized_pnl, 'signal_type': signal.get('signal_type', 'strategy')})
        logger.info(f"[{timestamp}] 执行交易: {log_action} {size:.4f} {symbol} @{price:.4f}. PnL: {realized_pnl:.2f}. 现金: {self.current_cash:.2f}")

    def monitor_and_execute_sl_tp(self, current_prices: pd.Series, timestamp: pd.Timestamp):
        if not self.positions or current_prices.empty: return
        for symbol, pos_details in list(self.positions.items()):
            shares, stop_loss, take_profit = pos_details.get('shares', 0.0), pos_details.get('stop_loss'), pos_details.get('take_profit')
            if shares <= 1e-9: continue
            current_price = current_prices.get(symbol)
            if pd.isna(current_price) or current_price <= 0: continue
            exit_reason = None
            if stop_loss and current_price <= stop_loss: exit_reason = 'stop_loss'
            elif take_profit and current_price >= take_profit: exit_reason = 'take_profit'
            if exit_reason:
                logger.info(f"[{timestamp}] {symbol} 触发平仓: {exit_reason}! 当前价: {current_price:.4f}, SL: {stop_loss}, TP: {take_profit}")
                self.execute_trade({'symbol': symbol, 'action': 'sell', 'price': current_price, 'size': shares, 'timestamp': timestamp, 'signal_type': f'exit_{exit_reason}'})