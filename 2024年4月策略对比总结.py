# -*- coding: utf-8 -*-
"""
2024年4月四大策略回测对比总结报告
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_april_2024_comparison():
    """创建2024年4月策略对比"""
    
    # 2024年4月回测结果
    strategies_results = {
        'AlphaXInspiredStrategy': {
            '总收益率': 5.44,
            '最大回撤率': 4.56,
            '胜率': 40.00,
            '交易次数': 25,
            '盈亏比': 150.00,
            '年化收益率': 65.28,
            '夏普比率': 245.00,
            '评级': 'A+',
            '特点': '表现优异，盈利稳定'
        },
        'TrendFollowingStrategy': {
            '总收益率': -52.56,
            '最大回撤率': 52.81,
            '胜率': 35.21,
            '交易次数': 852,
            '盈亏比': 132.19,
            '年化收益率': -99.99,
            '夏普比率': -3171.47,
            '评级': 'F',
            '特点': '严重亏损，过度交易'
        },
        'MeanReversionStrategy': {
            '总收益率': -33.60,
            '最大回撤率': 34.06,
            '胜率': 47.79,
            '交易次数': 544,
            '盈亏比': 85.74,
            '年化收益率': -99.32,
            '夏普比率': -1990.99,
            '评级': 'D',
            '特点': '胜率较高但整体亏损'
        },
        'OptimizedAlphaXStrategy': {
            '总收益率': 0.00,
            '最大回撤率': 0.00,
            '胜率': 0.00,
            '交易次数': 0,
            '盈亏比': 0.00,
            '年化收益率': 0.00,
            '夏普比率': 0.00,
            '评级': 'A',
            '特点': '极度保守，完全避险'
        }
    }
    
    print("=== 2024年4月四大策略回测对比总结报告 ===")
    print("测试期间：2024年4月1日 至 2024年4月30日 (30天)")
    print("测试标的：BTCUSDT")
    print("初始资金：100,000 USDT")
    print("数据来源：币安官方分钟K线数据")
    print("=" * 70)
    
    # 市场环境分析
    print("\n【2024年4月市场环境分析】")
    print("• BTC价格区间：约59,000-67,000 USDT")
    print("• 市场特征：高波动性，多次大幅震荡")
    print("• 主要事件：比特币减半前期，市场情绪波动")
    print("• 波动幅度：约13.5%的价格波动区间")
    print("• 趋势特征：震荡为主，缺乏明确单边趋势")
    
    # 策略表现排名
    print("\n【策略表现排名】")
    df = pd.DataFrame(strategies_results).T
    
    print("\n按总收益率排名：")
    for i, (strategy, data) in enumerate(df.sort_values('总收益率', ascending=False).iterrows(), 1):
        print(f"  {i}. {strategy}: {data['总收益率']:+.2f}% (评级: {data['评级']})")
    
    print("\n详细对比表：")
    print(f"{'策略名称':<25} {'收益率':<8} {'回撤率':<8} {'胜率':<8} {'交易次数':<8} {'评级':<4}")
    print("-" * 75)
    for strategy, data in strategies_results.items():
        print(f"{strategy:<25} {data['总收益率']:>+6.2f}% {data['最大回撤率']:>6.2f}% "
              f"{data['胜率']:>6.2f}% {data['交易次数']:>6d}   {data['评级']}")
    
    # 关键发现
    print("\n【关键发现】")
    print("\n1. AlphaXInspiredStrategy 表现最佳：")
    print("   ✅ 唯一盈利策略：+5.44%")
    print("   ✅ 风险控制良好：最大回撤仅4.56%")
    print("   ✅ 交易频率适中：25次交易")
    print("   ✅ 优秀的风险调整收益：夏普比率245%")
    
    print("\n2. TrendFollowingStrategy 表现最差：")
    print("   ❌ 严重亏损：-52.56%")
    print("   ❌ 过度交易：852次交易")
    print("   ❌ 高回撤风险：52.81%")
    print("   ❌ 不适合震荡市场")
    
    print("\n3. MeanReversionStrategy 中等表现：")
    print("   ⚠️  胜率最高：47.79%")
    print("   ❌ 整体亏损：-33.60%")
    print("   ⚠️  交易频繁：544次")
    print("   ❌ 盈亏比不佳：85.74%")
    
    print("\n4. OptimizedAlphaXStrategy 极度保守：")
    print("   ✅ 完全避险：0%收益，0%回撤")
    print("   ✅ 零交易：完全避免了市场风险")
    print("   ⚠️  过于保守：错过了盈利机会")
    print("   💡 适合极端风险厌恶投资者")
    
    # 市场适应性分析
    print("\n【市场适应性分析】")
    
    market_analysis = {
        "高波动震荡市": {
            "最适合": "AlphaXInspiredStrategy",
            "原因": "平衡了趋势跟踪和风险控制",
            "表现": "+5.44%收益，4.56%回撤"
        },
        "趋势跟踪": {
            "最不适合": "TrendFollowingStrategy", 
            "原因": "在震荡市中频繁止损",
            "表现": "-52.56%严重亏损"
        },
        "均值回归": {
            "部分适合": "MeanReversionStrategy",
            "原因": "胜率高但盈亏比差",
            "表现": "47.79%胜率但-33.60%亏损"
        },
        "风险规避": {
            "最保守": "OptimizedAlphaXStrategy",
            "原因": "参数过于严格，完全避险",
            "表现": "0交易，0风险，0收益"
        }
    }
    
    for condition, info in market_analysis.items():
        print(f"\n{condition}：")
        for key, value in info.items():
            print(f"  {key}：{value}")
    
    # 投资建议
    print("\n【投资建议】")
    
    recommendations = [
        "🏆 推荐策略：AlphaXInspiredStrategy",
        "   - 在2024年4月表现最佳，适合当前市场环境",
        "   - 良好的风险调整收益，适合大多数投资者",
        "",
        "⚠️  避免策略：TrendFollowingStrategy", 
        "   - 在震荡市场中表现极差",
        "   - 需要在明确趋势市场中使用",
        "",
        "🔧 优化建议：",
        "   - MeanReversionStrategy需要改进盈亏比",
        "   - OptimizedAlphaXStrategy可适当放宽参数",
        "   - TrendFollowingStrategy需要添加市场环境过滤",
        "",
        "📊 组合建议：",
        "   - 主要配置：AlphaXInspiredStrategy (70%)",
        "   - 辅助配置：OptimizedAlphaXStrategy (30%)",
        "   - 根据市场环境动态调整比例"
    ]
    
    for rec in recommendations:
        print(rec)
    
    # 风险提示
    print("\n【重要风险提示】")
    warnings = [
        "⚠️  单月回测结果不代表长期表现",
        "⚠️  2024年4月为特殊市场环境（减半前期）",
        "⚠️  需要在不同市场环境下验证策略稳定性",
        "⚠️  实盘交易存在滑点、延迟等额外成本",
        "⚠️  建议从小资金开始实盘验证",
        "⚠️  定期监控和调整策略参数",
        "⚠️  分散投资，不要过度依赖单一策略"
    ]
    
    for warning in warnings:
        print(f"  {warning}")
    
    return strategies_results

def create_april_comparison_chart(data):
    """创建2024年4月对比图表"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('2024年4月四大策略回测对比分析', fontsize=16, fontweight='bold')
    
    strategies = list(data.keys())
    colors = ['#00aa44', '#ff4444', '#ff8800', '#0066cc']  # 绿、红、橙、蓝
    
    # 1. 收益率对比
    ax1 = axes[0, 0]
    returns = [data[s]['总收益率'] for s in strategies]
    bars1 = ax1.bar(range(len(strategies)), returns, color=colors)
    ax1.set_title('总收益率对比 (%)')
    ax1.set_ylabel('收益率 (%)')
    ax1.set_xticks(range(len(strategies)))
    ax1.set_xticklabels([s.replace('Strategy', '') for s in strategies], rotation=45)
    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.7)
    for i, v in enumerate(returns):
        ax1.text(i, v + 1 if v >= 0 else v - 2, f'{v:+.1f}%', 
                ha='center', va='bottom' if v >= 0 else 'top', fontweight='bold')
    
    # 2. 风险对比（回撤率）
    ax2 = axes[0, 1]
    drawdowns = [data[s]['最大回撤率'] for s in strategies]
    bars2 = ax2.bar(range(len(strategies)), drawdowns, color=colors)
    ax2.set_title('最大回撤率对比 (%)')
    ax2.set_ylabel('回撤率 (%)')
    ax2.set_xticks(range(len(strategies)))
    ax2.set_xticklabels([s.replace('Strategy', '') for s in strategies], rotation=45)
    for i, v in enumerate(drawdowns):
        ax2.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    
    # 3. 交易频率对比
    ax3 = axes[0, 2]
    trade_counts = [data[s]['交易次数'] for s in strategies]
    bars3 = ax3.bar(range(len(strategies)), trade_counts, color=colors)
    ax3.set_title('交易次数对比')
    ax3.set_ylabel('交易次数')
    ax3.set_xticks(range(len(strategies)))
    ax3.set_xticklabels([s.replace('Strategy', '') for s in strategies], rotation=45)
    for i, v in enumerate(trade_counts):
        ax3.text(i, v + 20, f'{int(v)}', ha='center', va='bottom')
    
    # 4. 胜率对比
    ax4 = axes[1, 0]
    win_rates = [data[s]['胜率'] for s in strategies]
    bars4 = ax4.bar(range(len(strategies)), win_rates, color=colors)
    ax4.set_title('胜率对比 (%)')
    ax4.set_ylabel('胜率 (%)')
    ax4.set_xticks(range(len(strategies)))
    ax4.set_xticklabels([s.replace('Strategy', '') for s in strategies], rotation=45)
    ax4.set_ylim(0, 60)
    for i, v in enumerate(win_rates):
        ax4.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    
    # 5. 风险调整收益（夏普比率）
    ax5 = axes[1, 1]
    sharpe_ratios = [data[s]['夏普比率'] for s in strategies]
    # 处理极值
    sharpe_display = []
    for sr in sharpe_ratios:
        if sr > 1000:
            sharpe_display.append(1000)
        elif sr < -1000:
            sharpe_display.append(-1000)
        else:
            sharpe_display.append(sr)
    
    bars5 = ax5.bar(range(len(strategies)), sharpe_display, color=colors)
    ax5.set_title('夏普比率对比')
    ax5.set_ylabel('夏普比率')
    ax5.set_xticks(range(len(strategies)))
    ax5.set_xticklabels([s.replace('Strategy', '') for s in strategies], rotation=45)
    ax5.axhline(y=0, color='black', linestyle='--', alpha=0.7)
    for i, (v, orig) in enumerate(zip(sharpe_display, sharpe_ratios)):
        if orig > 1000:
            text = f'{orig:.0f}+'
        elif orig < -1000:
            text = f'{orig:.0f}'
        else:
            text = f'{orig:.0f}'
        ax5.text(i, v + 50 if v >= 0 else v - 50, text, 
                ha='center', va='bottom' if v >= 0 else 'top')
    
    # 6. 综合评级
    ax6 = axes[1, 2]
    ratings = [data[s]['评级'] for s in strategies]
    rating_scores = {'A+': 5, 'A': 4, 'B': 3, 'C': 2, 'D': 1, 'F': 0}
    scores = [rating_scores[r] for r in ratings]
    bars6 = ax6.bar(range(len(strategies)), scores, color=colors)
    ax6.set_title('策略评级对比')
    ax6.set_ylabel('评级分数')
    ax6.set_ylim(0, 6)
    ax6.set_yticks([0, 1, 2, 3, 4, 5])
    ax6.set_yticklabels(['F', 'D', 'C', 'B', 'A', 'A+'])
    ax6.set_xticks(range(len(strategies)))
    ax6.set_xticklabels([s.replace('Strategy', '') for s in strategies], rotation=45)
    for i, (score, rating) in enumerate(zip(scores, ratings)):
        ax6.text(i, score + 0.1, rating, ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('2024年4月策略对比图表.png', dpi=300, bbox_inches='tight')
    print(f"\n图表已保存为: 2024年4月策略对比图表.png")
    plt.show()

if __name__ == '__main__':
    print("2024年4月四大策略回测对比分析")
    print("=" * 70)
    
    # 创建对比分析
    results = create_april_2024_comparison()
    
    # 创建对比图表
    create_april_comparison_chart(results)
    
    print("\n" + "=" * 70)
    print("🎉 2024年4月策略对比分析完成！")
    print("✅ AlphaXInspiredStrategy 表现最佳：+5.44%收益")
    print("❌ TrendFollowingStrategy 表现最差：-52.56%亏损")
    print("🔧 OptimizedAlphaXStrategy 过于保守：0交易")
    print("⚠️  MeanReversionStrategy 需要优化：胜率高但整体亏损")
    
    print("\n📈 投资建议：")
    print("💡 主要使用 AlphaXInspiredStrategy")
    print("💡 辅助配置 OptimizedAlphaXStrategy 作为避险")
    print("💡 避免在震荡市使用 TrendFollowingStrategy")
    print("💡 继续优化 MeanReversionStrategy 的盈亏比")
