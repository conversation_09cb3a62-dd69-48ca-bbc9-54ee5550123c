#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
深入测试交易信号生成
"""

import pandas as pd
import numpy as np
from 配置.系统配置 import Config
from 核心代码.市场数据.数据获取器 import MarketData
from 核心代码.因子计算.因子库 import calculate_factors
from 核心代码.交易策略.策略库 import STRATEGIES

class MockEngine:
    """模拟交易引擎，用于测试策略"""
    
    def __init__(self):
        self.current_dt = pd.Timestamp.now()
        self.positions = {}
        
    def get_position_size(self, symbol):
        return self.positions.get(symbol, 0.0)
    
    def get_portfolio_value(self, data):
        return {'total': 100000.0}  # 模拟10万资金
    
    @property
    def risk_manager(self):
        return MockRiskManager()

class MockRiskManager:
    """模拟风险管理器"""
    
    def calculate_trade_size(self, portfolio_value, price, risk_pct, stop_loss_price):
        if price <= 0 or stop_loss_price <= 0:
            return 0.0
        
        risk_amount = portfolio_value * risk_pct
        price_diff = abs(price - stop_loss_price)
        
        if price_diff <= 0:
            return 0.0
            
        size = risk_amount / price_diff
        return min(size, portfolio_value * 0.1 / price)  # 最大10%仓位

def test_signal_generation():
    """测试信号生成的详细过程"""
    print("="*60)
    print("深入测试交易信号生成")
    print("="*60)
    
    # 1. 准备数据
    config = Config()
    market_data = MarketData(config)
    
    # 加载更多数据以确保有足够的历史数据计算技术指标
    df = market_data.get_market_data("BTCUSDT", "2025-04-01", "2025-04-10", source='local')
    
    if df is None or df.empty:
        print("❌ 无法获取测试数据")
        return False
    
    print(f"✅ 加载数据: {len(df)} 条记录")
    
    # 2. 计算因子
    factors_df = calculate_factors(df, config.factor_config)
    if factors_df is None or factors_df.empty:
        print("❌ 因子计算失败")
        return False
    
    # 合并数据
    combined_df = pd.concat([df, factors_df], axis=1)
    print(f"✅ 合并数据: {len(combined_df)} 条记录，{len(combined_df.columns)} 列")
    
    # 3. 测试每个策略
    mock_engine = MockEngine()
    
    for strategy_config in config.strategies:
        strategy_name = strategy_config['name']
        strategy_params = strategy_config['params']
        
        print(f"\n{'='*40}")
        print(f"测试策略: {strategy_name}")
        print(f"{'='*40}")
        
        if strategy_name not in STRATEGIES:
            print(f"❌ 策略不存在")
            continue
        
        try:
            # 初始化策略
            strategy_class = STRATEGIES[strategy_name]
            strategy = strategy_class(mock_engine, ['BTCUSDT'], strategy_params)
            
            print(f"✅ 策略初始化成功")
            print(f"   参数: {strategy_params}")
            
            # 检查策略需要的关键因子
            print(f"\n检查策略所需因子:")
            if hasattr(strategy, 'sma_short_key'):
                sma_short = strategy.sma_short_key
                sma_long = strategy.sma_long_key if hasattr(strategy, 'sma_long_key') else None
                print(f"   SMA短期: {sma_short} ({'✅' if sma_short in combined_df.columns else '❌'})")
                if sma_long:
                    print(f"   SMA长期: {sma_long} ({'✅' if sma_long in combined_df.columns else '❌'})")
            
            if hasattr(strategy, 'rsi_key'):
                rsi_key = strategy.rsi_key
                print(f"   RSI: {rsi_key} ({'✅' if rsi_key in combined_df.columns else '❌'})")
            
            if hasattr(strategy, 'atr_key'):
                atr_key = strategy.atr_key
                print(f"   ATR: {atr_key} ({'✅' if atr_key in combined_df.columns else '❌'})")
            
            if hasattr(strategy, 'adx_key'):
                adx_key = strategy.adx_key
                print(f"   ADX: {adx_key} ({'✅' if adx_key in combined_df.columns else '❌'})")
            
            # 分析最新数据
            latest_data = combined_df.iloc[-1]
            print(f"\n最新数据分析 ({latest_data.name}):")
            close_price = latest_data['CLOSE']
            print(f"   价格: {close_price:.2f if not pd.isna(close_price) else 'N/A'}")
            
            if hasattr(strategy, 'sma_short_key') and strategy.sma_short_key in combined_df.columns:
                sma_short_val = latest_data.get(strategy.sma_short_key)
                print(f"   {strategy.sma_short_key}: {sma_short_val:.2f if not pd.isna(sma_short_val) else 'N/A'}")
                
                if hasattr(strategy, 'sma_long_key') and strategy.sma_long_key in combined_df.columns:
                    sma_long_val = latest_data.get(strategy.sma_long_key)
                    print(f"   {strategy.sma_long_key}: {sma_long_val:.2f if not pd.isna(sma_long_val) else 'N/A'}")
                    
                    if not pd.isna(sma_short_val) and not pd.isna(sma_long_val):
                        trend = "上升趋势" if sma_short_val > sma_long_val else "下降趋势"
                        print(f"   趋势: {trend}")
            
            if hasattr(strategy, 'rsi_key') and strategy.rsi_key in combined_df.columns:
                rsi_val = latest_data.get(strategy.rsi_key)
                print(f"   {strategy.rsi_key}: {rsi_val:.2f if not pd.isna(rsi_val) else 'N/A'}")
                
                if hasattr(strategy, 'rsi_oversold') and not pd.isna(rsi_val):
                    oversold_status = "超卖" if rsi_val < strategy.rsi_oversold else "正常"
                    print(f"   RSI状态: {oversold_status} (阈值: {strategy.rsi_oversold})")
            
            if hasattr(strategy, 'adx_key') and strategy.adx_key in combined_df.columns:
                adx_val = latest_data.get(strategy.adx_key)
                print(f"   {strategy.adx_key}: {adx_val:.2f if not pd.isna(adx_val) else 'N/A'}")
                
                if hasattr(strategy, 'adx_threshold') and not pd.isna(adx_val):
                    trend_strength = "强趋势" if adx_val > strategy.adx_threshold else "弱趋势"
                    print(f"   趋势强度: {trend_strength} (阈值: {strategy.adx_threshold})")
            
            # 测试信号生成
            print(f"\n测试信号生成:")
            
            # 准备数据格式（策略期望的格式）
            test_data = pd.DataFrame([latest_data]).T
            test_data.index = ['BTCUSDT']
            
            mock_engine.current_dt = latest_data.name
            
            try:
                signals = strategy.on_bar(test_data)
                
                if signals and len(signals) > 0:
                    print(f"✅ 生成 {len(signals)} 个交易信号:")
                    for i, signal in enumerate(signals):
                        print(f"   信号 {i+1}: {signal['action']} {signal['size']:.4f} @ {signal['price']:.2f}")
                        if 'stop_loss_price' in signal and signal['stop_loss_price']:
                            print(f"           止损: {signal['stop_loss_price']:.2f}")
                        if 'take_profit_price' in signal and signal['take_profit_price']:
                            print(f"           止盈: {signal['take_profit_price']:.2f}")
                else:
                    print("⚠️  未生成交易信号")
                    
                    # 分析为什么没有信号
                    print("   分析原因:")
                    
                    # 检查AlphaXInspiredStrategy的具体条件
                    if strategy_name == 'AlphaXInspiredStrategy':
                        if hasattr(strategy, '_is_strong_uptrend'):
                            uptrend = strategy._is_strong_uptrend(latest_data)
                            print(f"   - 强上升趋势: {'✅' if uptrend else '❌'}")
                        
                        if hasattr(strategy, '_is_buy_trigger'):
                            buy_trigger = strategy._is_buy_trigger(latest_data)
                            print(f"   - 买入触发: {'✅' if buy_trigger else '❌'}")
                    
                    # 检查MeanReversion的具体条件
                    elif strategy_name == 'MeanReversion':
                        price = latest_data.get('CLOSE')
                        bb_lower_key = getattr(strategy, 'bb_lower_key', 'BBands_Default_BB_Lower')
                        bb_lower = latest_data.get(bb_lower_key)
                        rsi_key = getattr(strategy, 'rsi_key', 'RSI_14')
                        rsi = latest_data.get(rsi_key)
                        
                        if not pd.isna(price) and not pd.isna(bb_lower):
                            below_bb = price < bb_lower
                            print(f"   - 价格低于布林下轨: {'✅' if below_bb else '❌'} ({price:.2f} vs {bb_lower:.2f})")
                        
                        if not pd.isna(rsi) and hasattr(strategy, 'rsi_oversold'):
                            oversold = rsi < strategy.rsi_oversold
                            print(f"   - RSI超卖: {'✅' if oversold else '❌'} ({rsi:.2f} vs {strategy.rsi_oversold})")
                
            except Exception as e:
                print(f"❌ 信号生成出错: {e}")
                import traceback
                traceback.print_exc()
        
        except Exception as e:
            print(f"❌ 策略测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    return True

if __name__ == "__main__":
    test_signal_generation()
