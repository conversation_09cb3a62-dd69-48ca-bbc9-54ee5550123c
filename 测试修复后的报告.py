# -*- coding: utf-8 -*-
"""
测试修复后的报告显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s]: %(message)s'
)

logger = logging.getLogger(__name__)

def test_fixed_report():
    """测试修复后的报告显示"""
    
    print("🔧 测试修复后的报告显示")
    print("=" * 60)
    
    try:
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-05'  # 短期测试
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 测试已知会产生交易的策略
        strategy_name = 'TrendFollowing'
        strategy_class = STRATEGIES.get(strategy_name)

        if not strategy_class:
            print(f"❌ 策略 {strategy_name} 不可用")
            return

        # 宽松的策略参数
        strategy_params = {
            'sma_short': 10,
            'sma_long': 30,
            'rsi_threshold': 45,
            'risk_per_trade_pct': 0.02,
            'atr_sl_multiple': 1.5,
            'atr_tp_multiple': 3.0,
            'min_signal_interval_minutes': 30
        }
        
        print(f"测试策略: {strategy_name}")
        print(f"测试时间: {config.start_date} 到 {config.end_date}")
        print(f"策略参数: {strategy_params}")
        
        # 创建回测引擎
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
        
        # 运行回测
        results = backtester.run_backtest(config.start_date, config.end_date)
        
        # 检查结果
        print(f"\n📊 修复后的回测结果:")
        if results:
            print("✅ 结果字典不为空")
            print(f"结果字典键: {list(results.keys())}")
            
            # 显示关键指标
            print(f"\n📈 关键指标:")
            key_metrics = [
                ('初始资金', '初始资金'),
                ('最终权益', '最终权益'),
                ('总收益', '总收益'),
                ('总收益率', '总收益率'),
                ('年化收益率', '年化收益率'),
                ('年化波动率', '年化波动率'),
                ('夏普比率', '夏普比率'),
                ('索提诺比率', '索提诺比率'),
                ('最大回撤', '最大回撤'),
                ('最大回撤率', '最大回撤率'),
                ('Calmar比率', 'Calmar比率'),
                ('总交易次数', '总交易次数'),
                ('盈利次数', '盈利次数'),
                ('亏损次数', '亏损次数'),
                ('胜率', '胜率'),
                ('盈亏比', '盈亏比')
            ]
            
            for display_name, key in key_metrics:
                value = results.get(key, 'N/A')
                if isinstance(value, float):
                    if '率' in key or '比' in key:
                        print(f"  {display_name}: {value:.4f} ({value*100:.2f}%)")
                    else:
                        print(f"  {display_name}: {value:.4f}")
                else:
                    print(f"  {display_name}: {value}")
        else:
            print("❌ 结果为空")
        
        # 检查交易日志
        trade_log = backtester.portfolio.trade_log
        print(f"\n📋 交易日志:")
        print(f"总交易记录数: {len(trade_log)}")
        
        if trade_log:
            df = pd.DataFrame(trade_log)
            print(f"交易类型统计:")
            action_counts = df['action'].value_counts()
            for action, count in action_counts.items():
                print(f"  {action}: {count}次")
        
        # 检查权益曲线
        equity_curve = backtester.equity_curve
        print(f"\n📈 权益曲线:")
        print(f"权益点数: {len(equity_curve)}")
        if equity_curve:
            print(f"起始权益: {equity_curve[0][1]:.2f}")
            print(f"最终权益: {equity_curve[-1][1]:.2f}")
        
        return results
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    try:
        results = test_fixed_report()
        
        if results:
            print(f"\n🎉 测试成功！报告指标已修复")
            print(f"现在图表应该显示正确的指标值")
        else:
            print(f"\n❌ 测试失败")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
