2025-06-09 04:54:37,578 [WARNING] [核心代码.市场数据.数据获取器]: 未配置有效的 Binance API Key/Secret，Binance 数据源将不可用。
2025-06-09 04:54:37,578 [WARNING] [核心代码.市场数据.数据获取器]: 未配置有效的 Tushare Token，Tushare 数据源将不可用。
2025-06-09 04:54:37,578 [INFO] [核心代码.市场数据.数据获取器]: 请求数据: BTCUSDT, 2025-04-01 to 2025-04-29, Freq: 1m, Source: local
2025-06-09 04:54:37,578 [INFO] [核心代码.市场数据.数据获取器]: 从本地加载 BTCUSDT 在 2025-04-01 至 2025-04-29 范围的分钟数据，路径: 数据/BTCUSDT
2025-06-09 04:54:37,579 [DEBUG] [核心代码.市场数据.数据获取器]: 读取本地数据文件: 数据/BTCUSDT\BTCUSDT-1m-2025-04.csv
2025-06-09 04:54:37,628 [DEBUG] [核心代码.市场数据.数据获取器]: 成功读取 数据/BTCUSDT\BTCUSDT-1m-2025-04.csv，包含 40321 条数据。
2025-06-09 04:54:37,633 [INFO] [核心代码.市场数据.数据获取器]: load_local_minute_data: BTCUSDT 筛选后数据范围从 2025-04-01 00:00:00 到 2025-04-29 00:00:00，共 40321 条。
2025-06-09 04:54:37,633 [INFO] [核心代码.市场数据.数据获取器]: 为 BTCUSDT 从本地加载并筛选了 40321 条数据。
2025-06-09 04:54:37,633 [INFO] [核心代码.市场数据.数据获取器]: 从本地加载了 BTCUSDT 的 40321 条分钟数据。
2025-06-09 04:54:37,642 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: SMA_20 (函数: SMA, 参数: {'window': 20})
2025-06-09 04:54:37,643 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: SMA_60 (函数: SMA, 参数: {'window': 60})
2025-06-09 04:54:37,643 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: EMA_10 (函数: EMA, 参数: {'window': 10})
2025-06-09 04:54:37,643 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: EMA_20 (函数: EMA, 参数: {'window': 20})
2025-06-09 04:54:37,644 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: RSI_14 (函数: RSI, 参数: {'window': 14})
2025-06-09 04:54:37,645 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: ATR_14 (函数: ATR, 参数: {'window': 14})
2025-06-09 04:54:37,645 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: MACD_Default (函数: MACD, 参数: {})
2025-06-09 04:54:37,646 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: BBands_Default (函数: BBands, 参数: {})
2025-06-09 04:54:37,646 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Volatility_20 (函数: Volatility, 参数: {'window': 20})
2025-06-09 04:54:37,647 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Momentum_10 (函数: Momentum, 参数: {'window': 10})
2025-06-09 04:54:37,647 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: CCI_20 (函数: CCI, 参数: {'window': 20})
2025-06-09 04:54:37,647 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Donchian_20 (函数: Donchian, 参数: {'window': 20})
2025-06-09 04:54:37,647 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Keltner_Default (函数: Keltner, 参数: {})
2025-06-09 04:54:37,647 [DEBUG] [核心代码.因子计算.因子库]: 开始计算衍生机器学习特征...
2025-06-09 04:54:37,650 [DEBUG] [核心代码.因子计算.因子库]: 衍生特征计算完成。
2025-06-09 04:54:37,661 [WARNING] [核心代码.市场数据.数据获取器]: 未配置有效的 Binance API Key/Secret，Binance 数据源将不可用。
2025-06-09 04:54:37,661 [WARNING] [核心代码.市场数据.数据获取器]: 未配置有效的 Tushare Token，Tushare 数据源将不可用。
2025-06-09 04:54:37,661 [INFO] [核心代码.市场数据.数据获取器]: 请求数据: BTCUSDT, 2025-04-01 to 2025-04-29, Freq: 1m, Source: local
2025-06-09 04:54:37,661 [INFO] [核心代码.市场数据.数据获取器]: 从本地加载 BTCUSDT 在 2025-04-01 至 2025-04-29 范围的分钟数据，路径: 数据/BTCUSDT
2025-06-09 04:54:37,661 [DEBUG] [核心代码.市场数据.数据获取器]: 读取本地数据文件: 数据/BTCUSDT\BTCUSDT-1m-2025-04.csv
2025-06-09 04:54:37,692 [DEBUG] [核心代码.市场数据.数据获取器]: 成功读取 数据/BTCUSDT\BTCUSDT-1m-2025-04.csv，包含 40321 条数据。
2025-06-09 04:54:37,695 [INFO] [核心代码.市场数据.数据获取器]: load_local_minute_data: BTCUSDT 筛选后数据范围从 2025-04-01 00:00:00 到 2025-04-29 00:00:00，共 40321 条。
2025-06-09 04:54:37,695 [INFO] [核心代码.市场数据.数据获取器]: 为 BTCUSDT 从本地加载并筛选了 40321 条数据。
2025-06-09 04:54:37,696 [INFO] [核心代码.市场数据.数据获取器]: 从本地加载了 BTCUSDT 的 40321 条分钟数据。
2025-06-09 04:54:37,697 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: SMA_20 (函数: SMA, 参数: {'window': 20})
2025-06-09 04:54:37,697 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: SMA_60 (函数: SMA, 参数: {'window': 60})
2025-06-09 04:54:37,698 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: EMA_10 (函数: EMA, 参数: {'window': 10})
2025-06-09 04:54:37,698 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: EMA_20 (函数: EMA, 参数: {'window': 20})
2025-06-09 04:54:37,698 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: RSI_14 (函数: RSI, 参数: {'window': 14})
2025-06-09 04:54:37,700 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: ATR_14 (函数: ATR, 参数: {'window': 14})
2025-06-09 04:54:37,701 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: MACD_Default (函数: MACD, 参数: {})
2025-06-09 04:54:37,701 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: BBands_Default (函数: BBands, 参数: {})
2025-06-09 04:54:37,702 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Volatility_20 (函数: Volatility, 参数: {'window': 20})
2025-06-09 04:54:37,702 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Momentum_10 (函数: Momentum, 参数: {'window': 10})
2025-06-09 04:54:37,702 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: CCI_20 (函数: CCI, 参数: {'window': 20})
2025-06-09 04:54:37,702 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Donchian_20 (函数: Donchian, 参数: {'window': 20})
2025-06-09 04:54:37,702 [DEBUG] [核心代码.因子计算.因子库]: 计算因子: Keltner_Default (函数: Keltner, 参数: {})
2025-06-09 04:54:37,702 [DEBUG] [核心代码.因子计算.因子库]: 开始计算衍生机器学习特征...
2025-06-09 04:54:37,705 [DEBUG] [核心代码.因子计算.因子库]: 衍生特征计算完成。
