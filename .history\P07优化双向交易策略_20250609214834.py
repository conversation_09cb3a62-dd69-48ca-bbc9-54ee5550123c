# -*- coding: utf-8 -*-
"""
P07优化双向交易策略
深度优化信号质量和参数设置，追求稳定盈利
采用多重确认机制 + 动态参数 + 严格风控
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class OptimizedBidirectionalStrategy:
    """P07优化双向交易策略 - 深度优化版本"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 深度优化的策略参数
        self.strategy_params = {
            # 技术指标参数 (优化后)
            'ema_fast': 8,                 # 快速EMA (更敏感)
            'ema_slow': 21,                # 慢速EMA
            'ema_trend': 55,               # 趋势EMA
            'rsi_period': 10,              # RSI周期 (更敏感)
            'macd_signal': 6,              # MACD信号线 (更敏感)
            'atr_period': 14,              # ATR周期
            
            # 多重确认阈值 (严格化)
            'trend_confirmation': True,     # 趋势确认
            'volume_confirmation': True,    # 成交量确认
            'momentum_confirmation': True,  # 动量确认
            'volatility_filter': True,     # 波动率过滤
            
            # 做多信号阈值 (优化)
            'long_rsi_min': 40,            # 做多RSI最小值
            'long_rsi_max': 70,            # 做多RSI最大值
            'long_momentum_min': 0.005,    # 做多最小动量 (提高)
            'long_volume_min': 1.2,        # 做多最小成交量比
            'long_trend_strength': 0.002,  # 做多趋势强度
            
            # 做空信号阈值 (优化)
            'short_rsi_min': 30,           # 做空RSI最小值
            'short_rsi_max': 60,           # 做空RSI最大值
            'short_momentum_max': -0.005,  # 做空最大动量 (提高)
            'short_volume_min': 1.2,       # 做空最小成交量比
            'short_trend_strength': 0.002, # 做空趋势强度
            
            # 信号质量控制
            'min_signal_strength': 1.5,    # 最小信号强度 (提高)
            'signal_confirmation_bars': 3, # 信号确认K线数
            'max_signals_per_day': 5,      # 每日最大信号数 (减少)
            
            # 仓位管理 (优化)
            'base_position': 0.15,         # 基础仓位 (降低)
            'max_position': 0.3,           # 最大仓位 (降低)
            'position_scaling': True,      # 仓位缩放
            'risk_per_trade': 0.015,       # 每笔风险 (降低)
            
            # 动态风控 (新增)
            'dynamic_stop_loss': True,     # 动态止损
            'stop_loss_base': 0.02,        # 基础止损2%
            'take_profit_base': 0.06,      # 基础止盈6%
            'profit_protection': 0.5,      # 盈利保护50%
            'trailing_stop': True,         # 移动止损
            
            # 交易控制 (严格化)
            'min_signal_gap': 120,         # 最小信号间隔2小时 (增加)
            'max_holding_hours': 8,        # 最大持仓8小时 (减少)
            'force_close_weak_signal': True, # 弱信号强制平仓
            'market_hours_only': False,    # 24小时交易
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_advanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算高级技术指标"""
        try:
            df = data.copy()
            
            # 多重EMA系统
            df['EMA_Fast'] = df['CLOSE'].ewm(span=self.strategy_params['ema_fast']).mean()
            df['EMA_Slow'] = df['CLOSE'].ewm(span=self.strategy_params['ema_slow']).mean()
            df['EMA_Trend'] = df['CLOSE'].ewm(span=self.strategy_params['ema_trend']).mean()
            
            # 优化的MACD
            df['MACD'] = df['EMA_Fast'] - df['EMA_Slow']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['macd_signal']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # 敏感RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # ATR (真实波动范围)
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(self.strategy_params['atr_period']).mean()
            
            # 多重动量指标
            df['Momentum_3'] = df['CLOSE'].pct_change(3)
            df['Momentum_10'] = df['CLOSE'].pct_change(10)
            df['Momentum_30'] = df['CLOSE'].pct_change(30)
            
            # 成交量分析
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            df['Volume_Trend'] = df['Volume_MA'].pct_change(5)
            
            # 波动率指标
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            df['Price_Range'] = (df['HIGH'] - df['LOW']) / df['CLOSE']
            
            # 趋势强度
            df['Trend_Strength'] = abs(df['EMA_Fast'] - df['EMA_Slow']) / df['CLOSE']
            df['Long_Trend'] = (df['CLOSE'] - df['EMA_Trend']) / df['EMA_Trend']
            
            # 市场结构
            df['Higher_High'] = (df['HIGH'] > df['HIGH'].shift(1)) & (df['HIGH'].shift(1) > df['HIGH'].shift(2))
            df['Lower_Low'] = (df['LOW'] < df['LOW'].shift(1)) & (df['LOW'].shift(1) < df['LOW'].shift(2))
            
            return df
            
        except Exception as e:
            logger.warning(f"计算高级指标失败: {e}")
            return data
    
    def generate_high_quality_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成高质量交易信号"""
        try:
            # 初始化信号
            df['Long_Signal'] = 0
            df['Short_Signal'] = 0
            df['Signal_Quality'] = 0
            
            # 做多信号 - 多重确认
            long_base_conditions = (
                # 趋势确认
                (df['EMA_Fast'] > df['EMA_Slow']) &
                (df['CLOSE'] > df['EMA_Fast']) &
                (df['CLOSE'] > df['EMA_Trend']) &
                
                # MACD确认
                (df['MACD'] > df['MACD_Signal']) &
                (df['MACD_Histogram'] > 0) &
                (df['MACD_Histogram'] > df['MACD_Histogram'].shift(1)) &
                
                # RSI确认
                (df['RSI'] >= self.strategy_params['long_rsi_min']) &
                (df['RSI'] <= self.strategy_params['long_rsi_max']) &
                
                # 动量确认
                (df['Momentum_10'] > self.strategy_params['long_momentum_min']) &
                (df['Momentum_3'] > 0) &
                
                # 成交量确认
                (df['Volume_Ratio'] > self.strategy_params['long_volume_min']) &
                (df['Volume_Trend'] > 0) &
                
                # 趋势强度确认
                (df['Trend_Strength'] > self.strategy_params['long_trend_strength']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.01) & (df['Volatility'] < 0.08) &
                
                # 市场结构确认
                (df['Higher_High'] | (df['CLOSE'] > df['CLOSE'].shift(5)))
            )
            
            # 做空信号 - 多重确认
            short_base_conditions = (
                # 趋势确认
                (df['EMA_Fast'] < df['EMA_Slow']) &
                (df['CLOSE'] < df['EMA_Fast']) &
                (df['CLOSE'] < df['EMA_Trend']) &
                
                # MACD确认
                (df['MACD'] < df['MACD_Signal']) &
                (df['MACD_Histogram'] < 0) &
                (df['MACD_Histogram'] < df['MACD_Histogram'].shift(1)) &
                
                # RSI确认
                (df['RSI'] >= self.strategy_params['short_rsi_min']) &
                (df['RSI'] <= self.strategy_params['short_rsi_max']) &
                
                # 动量确认
                (df['Momentum_10'] < self.strategy_params['short_momentum_max']) &
                (df['Momentum_3'] < 0) &
                
                # 成交量确认
                (df['Volume_Ratio'] > self.strategy_params['short_volume_min']) &
                (df['Volume_Trend'] > 0) &
                
                # 趋势强度确认
                (df['Trend_Strength'] > self.strategy_params['short_trend_strength']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.01) & (df['Volatility'] < 0.08) &
                
                # 市场结构确认
                (df['Lower_Low'] | (df['CLOSE'] < df['CLOSE'].shift(5)))
            )
            
            # 信号确认机制
            confirm_bars = self.strategy_params['signal_confirmation_bars']
            
            # 做多信号确认
            for i in range(confirm_bars, len(df)):
                if all(long_base_conditions.iloc[i-j] for j in range(confirm_bars)):
                    df.iloc[i, df.columns.get_loc('Long_Signal')] = 1
            
            # 做空信号确认
            for i in range(confirm_bars, len(df)):
                if all(short_base_conditions.iloc[i-j] for j in range(confirm_bars)):
                    df.iloc[i, df.columns.get_loc('Short_Signal')] = 1
            
            # 计算信号质量分数
            df['Signal_Quality'] = (
                # 趋势强度权重
                df['Trend_Strength'] * 100 * 0.25 +
                
                # MACD强度权重
                abs(df['MACD_Histogram']) * 1000 * 0.25 +
                
                # 动量强度权重
                abs(df['Momentum_10']) * 100 * 0.2 +
                
                # 成交量强度权重
                (df['Volume_Ratio'] - 1) * 0.2 +
                
                # RSI位置权重
                (50 - abs(df['RSI'] - 50)) / 50 * 0.1
            )
            
            return df
            
        except Exception as e:
            logger.error(f"生成高质量信号失败: {e}")
            return df

    def simulate_optimized_strategy(self, data: pd.DataFrame) -> dict:
        """模拟优化双向交易策略"""
        try:
            print("🔄 模拟P07优化双向交易策略...")

            # 计算高级技术指标
            data = self.calculate_advanced_indicators(data)
            data = self.generate_high_quality_signals(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            long_position = 0
            short_position = 0
            trades = []
            equity_curve = [initial_capital]

            # 交易控制变量
            last_trade_time = None
            long_entry_time = None
            short_entry_time = None
            long_entry_price = 0
            short_entry_price = 0
            daily_signals = {}

            # 动态风控变量
            max_equity = initial_capital
            consecutive_losses = 0

            for i in range(100, len(data)):  # 从第100个数据点开始，确保指标稳定
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']

                # 计算当前权益
                long_value = long_position * current_price if long_position > 0 else 0
                short_value = short_position * (2 * short_entry_price - current_price) if short_position > 0 else 0
                current_equity = cash + long_value + short_value
                equity_curve.append(current_equity)

                # 更新最大权益
                max_equity = max(max_equity, current_equity)

                # 检查数据有效性
                if pd.isna(current_price):
                    continue

                # 每日信号限制
                current_date = current_time.date()
                if current_date not in daily_signals:
                    daily_signals[current_date] = 0

                if daily_signals[current_date] >= self.strategy_params['max_signals_per_day']:
                    continue

                # 检查信号间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_signal_gap']:
                        continue

                # 获取信号和质量
                long_signal = current_data.get('Long_Signal', 0)
                short_signal = current_data.get('Short_Signal', 0)
                signal_quality = current_data.get('Signal_Quality', 0)

                # 信号质量过滤
                if signal_quality < self.strategy_params['min_signal_strength']:
                    continue

                # 连续亏损保护
                if consecutive_losses >= 3:
                    signal_quality *= 0.5  # 降低信号强度

                # 检查做多信号
                if long_signal == 1 and signal_quality >= self.strategy_params['min_signal_strength']:
                    # 平空头仓位
                    if short_position > 0:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': '反向信号平仓'
                        })

                        if short_pnl < 0:
                            consecutive_losses += 1
                        else:
                            consecutive_losses = 0

                        short_position = 0
                        short_entry_time = None

                    # 开多头仓位
                    if long_position == 0:
                        # 动态仓位计算
                        base_pos = self.strategy_params['base_position']
                        quality_multiplier = min(signal_quality / 2, 2.0)
                        position_size = base_pos * quality_multiplier
                        position_size = min(position_size, self.strategy_params['max_position'])

                        # ATR风险调整
                        atr = current_data.get('ATR', current_price * 0.02)
                        risk_adjusted_size = min(position_size,
                                               self.strategy_params['risk_per_trade'] / (atr / current_price))

                        position_value = cash * risk_adjusted_size
                        shares = position_value / current_price

                        if shares > 0:
                            long_position = shares
                            cash -= shares * current_price * 1.0005
                            long_entry_time = current_time
                            long_entry_price = current_price

                            # 动态止损止盈
                            volatility = current_data.get('Volatility', 0.02)
                            stop_loss_pct = self.strategy_params['stop_loss_base'] * (1 + volatility * 5)
                            take_profit_pct = self.strategy_params['take_profit_base'] * (1 + volatility * 2)

                            trades.append({
                                'time': current_time,
                                'action': 'buy_long',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'position_size': risk_adjusted_size,
                                'stop_loss': current_price * (1 - stop_loss_pct),
                                'take_profit': current_price * (1 + take_profit_pct),
                                'atr': atr
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 检查做空信号
                elif short_signal == 1 and signal_quality >= self.strategy_params['min_signal_strength']:
                    # 平多头仓位
                    if long_position > 0:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': '反向信号平仓'
                        })

                        if long_pnl < 0:
                            consecutive_losses += 1
                        else:
                            consecutive_losses = 0

                        long_position = 0
                        long_entry_time = None

                    # 开空头仓位
                    if short_position == 0:
                        # 动态仓位计算
                        base_pos = self.strategy_params['base_position']
                        quality_multiplier = min(signal_quality / 2, 2.0)
                        position_size = base_pos * quality_multiplier
                        position_size = min(position_size, self.strategy_params['max_position'])

                        # ATR风险调整
                        atr = current_data.get('ATR', current_price * 0.02)
                        risk_adjusted_size = min(position_size,
                                               self.strategy_params['risk_per_trade'] / (atr / current_price))

                        position_value = cash * risk_adjusted_size
                        shares = position_value / current_price

                        if shares > 0:
                            short_position = shares
                            cash -= shares * current_price * 1.0005
                            short_entry_time = current_time
                            short_entry_price = current_price

                            # 动态止损止盈
                            volatility = current_data.get('Volatility', 0.02)
                            stop_loss_pct = self.strategy_params['stop_loss_base'] * (1 + volatility * 5)
                            take_profit_pct = self.strategy_params['take_profit_base'] * (1 + volatility * 2)

                            trades.append({
                                'time': current_time,
                                'action': 'sell_short',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'position_size': risk_adjusted_size,
                                'stop_loss': current_price * (1 + stop_loss_pct),
                                'take_profit': current_price * (1 - take_profit_pct),
                                'atr': atr
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 优化的多头出场逻辑
                if long_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'buy_long'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']
                    atr = last_trade.get('atr', current_price * 0.02)

                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price > long_entry_price * 1.03:  # 盈利3%以上
                            profit_protection = long_entry_price * (1 + self.strategy_params['profit_protection'] * 0.03)
                            stop_loss = max(stop_loss, profit_protection)

                    # 动态止损调整
                    if self.strategy_params['dynamic_stop_loss']:
                        current_volatility = current_data.get('Volatility', 0.02)
                        if current_volatility > 0.05:  # 高波动时收紧止损
                            stop_loss = max(stop_loss, current_price * 0.985)

                    holding_hours = (current_time - long_entry_time).total_seconds() / 3600

                    should_exit_long = False
                    exit_reason = ""

                    if current_price <= stop_loss:
                        should_exit_long = True
                        exit_reason = "多头止损"
                    elif current_price >= take_profit:
                        should_exit_long = True
                        exit_reason = "多头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit_long = True
                        exit_reason = "多头超时"
                    elif (self.strategy_params['force_close_weak_signal'] and
                          current_data.get('Signal_Quality', 0) < 0.5 and
                          holding_hours > 2):
                        should_exit_long = True
                        exit_reason = "信号减弱"

                    if should_exit_long:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'pnl_pct': long_pnl / (long_entry_price * long_position) * 100,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        if long_pnl < 0:
                            consecutive_losses += 1
                        else:
                            consecutive_losses = 0

                        long_position = 0
                        long_entry_time = None
                        last_trade_time = current_time

                # 优化的空头出场逻辑
                if short_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'sell_short'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']
                    atr = last_trade.get('atr', current_price * 0.02)

                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price < short_entry_price * 0.97:  # 盈利3%以上
                            profit_protection = short_entry_price * (1 - self.strategy_params['profit_protection'] * 0.03)
                            stop_loss = min(stop_loss, profit_protection)

                    # 动态止损调整
                    if self.strategy_params['dynamic_stop_loss']:
                        current_volatility = current_data.get('Volatility', 0.02)
                        if current_volatility > 0.05:  # 高波动时收紧止损
                            stop_loss = min(stop_loss, current_price * 1.015)

                    holding_hours = (current_time - short_entry_time).total_seconds() / 3600

                    should_exit_short = False
                    exit_reason = ""

                    if current_price >= stop_loss:
                        should_exit_short = True
                        exit_reason = "空头止损"
                    elif current_price <= take_profit:
                        should_exit_short = True
                        exit_reason = "空头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit_short = True
                        exit_reason = "空头超时"
                    elif (self.strategy_params['force_close_weak_signal'] and
                          current_data.get('Signal_Quality', 0) < 0.5 and
                          holding_hours > 2):
                        should_exit_short = True
                        exit_reason = "信号减弱"

                    if should_exit_short:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'pnl_pct': short_pnl / (short_entry_price * short_position) * 100,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        if short_pnl < 0:
                            consecutive_losses += 1
                        else:
                            consecutive_losses = 0

                        short_position = 0
                        short_entry_time = None
                        last_trade_time = current_time

            # 期末平仓
            final_price = data['CLOSE'].iloc[-1]

            if long_position > 0:
                long_pnl = (final_price - long_entry_price) * long_position
                cash += long_position * final_price * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell_long',
                    'price': final_price,
                    'shares': long_position,
                    'pnl': long_pnl,
                    'reason': '期末平仓'
                })

            if short_position > 0:
                short_pnl = short_position * (short_entry_price - final_price)
                cash += short_position * short_entry_price + short_pnl * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'cover_short',
                    'price': final_price,
                    'shares': short_position,
                    'pnl': short_pnl,
                    'reason': '期末平仓'
                })

            final_equity = cash

            # 计算统计指标
            entry_trades = [t for t in trades if t['action'] in ['buy_long', 'sell_short']]
            exit_trades = [t for t in trades if t.get('pnl') is not None]

            total_trades = len(entry_trades)
            long_trades = len([t for t in trades if t['action'] == 'buy_long'])
            short_trades = len([t for t in trades if t['action'] == 'sell_short'])

            profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in exit_trades if t.get('pnl', 0) < 0])

            win_rate = profitable_trades / (profitable_trades + losing_trades) if (profitable_trades + losing_trades) > 0 else 0

            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in exit_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in exit_trades if t.get('pnl', 0) < 0]

            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital

            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            print(f"✅ P07优化双向交易策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   总交易次数: {total_trades} (多头:{long_trades}, 空头:{short_trades})")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")

            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'long_trades': long_trades,
                'short_trades': short_trades,
                'winning_trades': profitable_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }

        except Exception as e:
            logger.error(f"P07优化双向交易策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None
