# -*- coding: utf-8 -*-
"""
增强版AlphaX策略 V3.0 - 高级功能版本
主要特性：
1. 趋势跟踪：在强上升趋势中增加仓位
2. 动态止损：根据波动率调整止损距离  
3. 分批建仓：避免一次性满仓
4. 智能风险管理：动态调整仓位大小
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class EnhancedAlphaXStrategyV3:
    """增强版AlphaX策略 V3.0"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'EnhancedAlphaXStrategyV3'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 基础技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.adx_threshold = float(all_params.get('adx_threshold', 25.0))
        self.rsi_oversold = float(all_params.get('rsi_oversold', 35.0))
        self.risk_per_trade_pct = float(all_params.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(all_params.get('atr_sl_multiple', 2.0))
        self.atr_tp_multiple = float(all_params.get('atr_tp_multiple', 4.0))
        self.min_signal_interval_minutes = int(all_params.get('min_signal_interval_minutes', 120))
        
        # 新增：趋势跟踪参数
        self.trend_strength_threshold = float(all_params.get('trend_strength_threshold', 40.0))  # 强趋势ADX阈值
        self.trend_multiplier = float(all_params.get('trend_multiplier', 1.5))  # 强趋势时的仓位倍数
        self.momentum_threshold = float(all_params.get('momentum_threshold', 0.02))  # 动量阈值
        
        # 新增：动态止损参数
        self.volatility_lookback = int(all_params.get('volatility_lookback', 20))  # 波动率回看期
        self.min_sl_multiple = float(all_params.get('min_sl_multiple', 1.5))  # 最小止损倍数
        self.max_sl_multiple = float(all_params.get('max_sl_multiple', 3.0))  # 最大止损倍数
        self.trailing_stop_enabled = bool(all_params.get('trailing_stop_enabled', True))  # 启用跟踪止损
        
        # 新增：分批建仓参数
        self.batch_count = int(all_params.get('batch_count', 3))  # 分批次数
        self.batch_interval_minutes = int(all_params.get('batch_interval_minutes', 30))  # 分批间隔
        self.max_position_pct = float(all_params.get('max_position_pct', 0.03))  # 最大总仓位比例
        self.pyramid_enabled = bool(all_params.get('pyramid_enabled', True))  # 启用金字塔加仓
        
        # 新增：智能风险管理参数
        self.volatility_adjustment = bool(all_params.get('volatility_adjustment', True))  # 波动率调整
        self.correlation_check = bool(all_params.get('correlation_check', False))  # 相关性检查
        self.max_daily_trades = int(all_params.get('max_daily_trades', 5))  # 每日最大交易次数
        
        # 状态跟踪
        self.last_signal_time: Dict[str, pd.Timestamp] = {}
        self.position_batches: Dict[str, List[Dict]] = {}  # 记录分批建仓信息
        self.dynamic_sl_history: Dict[str, List[float]] = {}  # 动态止损历史
        self.daily_trade_count: Dict[str, int] = {}  # 每日交易计数
        self.trend_strength_history: Dict[str, List[float]] = {}  # 趋势强度历史
        
        logger.info(f"{self.strategy_name}: 增强版V3.0初始化完成")
        logger.info(f"趋势跟踪: {self.trend_multiplier}x倍数, 阈值={self.trend_strength_threshold}")
        logger.info(f"分批建仓: {self.batch_count}批, 间隔={self.batch_interval_minutes}分钟")
        logger.info(f"动态止损: {self.min_sl_multiple}-{self.max_sl_multiple}倍ATR")

    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            # 更新每日交易计数
            self._update_daily_trade_count(symbol, current_time)
            
            # 检查每日交易限制
            if not self._check_daily_trade_limit(symbol, current_time):
                continue
            
            # 获取当前持仓
            current_position = abs(self.engine.get_position_size(symbol)) if self.engine else 0
            
            if current_position > 1e-9:
                # 已有持仓，检查加仓和止损更新
                add_signals = self._handle_existing_position(symbol, data, current_time)
                signals.extend(add_signals)
            else:
                # 无持仓，检查开仓
                entry_signals = self._handle_new_position(symbol, data, current_time)
                signals.extend(entry_signals)
        
        return signals

    def _update_daily_trade_count(self, symbol: str, current_time: datetime):
        """更新每日交易计数"""
        current_date = current_time.date()
        key = f"{symbol}_{current_date}"
        
        # 清理旧的计数
        keys_to_remove = [k for k in self.daily_trade_count.keys() 
                         if not k.startswith(f"{symbol}_{current_date}")]
        for k in keys_to_remove:
            del self.daily_trade_count[k]

    def _check_daily_trade_limit(self, symbol: str, current_time: datetime) -> bool:
        """检查每日交易限制"""
        current_date = current_time.date()
        key = f"{symbol}_{current_date}"
        
        current_count = self.daily_trade_count.get(key, 0)
        return current_count < self.max_daily_trades

    def _handle_existing_position(self, symbol: str, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """处理已有持仓的情况"""
        signals = []
        
        # 更新趋势强度历史
        self._update_trend_strength_history(symbol, data)
        
        # 检查是否可以加仓
        if self.pyramid_enabled and self._can_add_position(symbol, data, current_time):
            add_signal = self._generate_add_position_signal(symbol, data, current_time)
            if add_signal:
                signals.append(add_signal)
        
        # 更新动态止损
        if self.trailing_stop_enabled:
            self._update_dynamic_stop_loss(symbol, data)
        
        return signals

    def _handle_new_position(self, symbol: str, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """处理新开仓的情况"""
        signals = []
        
        # 检查开仓条件
        if self._should_enter_position(symbol, data, current_time):
            entry_signal = self._generate_entry_signal(symbol, data, current_time)
            if entry_signal:
                signals.append(entry_signal)
        
        return signals

    def _should_enter_position(self, symbol: str, data: Dict[str, Any], current_time: datetime) -> bool:
        """检查是否应该开仓"""
        # 检查信号间隔
        if symbol in self.last_signal_time:
            time_diff = (current_time - self.last_signal_time[symbol]).total_seconds() / 60
            if time_diff < self.min_signal_interval_minutes:
                return False
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'SMA_20', 'SMA_60', 'ADX_14', 'RSI_14', 'ATR_14']
        if not all(key in data for key in required_keys):
            return False
        
        # 检查基本技术条件
        return self._is_strong_uptrend(data) and self._is_buy_trigger(data)

    def _is_strong_uptrend(self, data: Dict[str, Any]) -> bool:
        """检查是否为强上升趋势"""
        try:
            sma_short = data.get(self.sma_short_key)
            sma_long = data.get(self.sma_long_key)
            adx = data.get('ADX_14')
            
            if any(pd.isna(x) or x is None for x in [sma_short, sma_long, adx]):
                return False
            
            return sma_short > sma_long and adx > self.adx_threshold
            
        except Exception as e:
            logger.warning(f"检查强趋势时出错: {e}")
            return False

    def _is_very_strong_uptrend(self, data: Dict[str, Any]) -> bool:
        """检查是否为非常强的上升趋势（用于加仓判断）"""
        try:
            sma_short = data.get(self.sma_short_key)
            sma_long = data.get(self.sma_long_key)
            adx = data.get('ADX_14')
            price = data.get('CLOSE')
            
            if any(pd.isna(x) or x is None for x in [sma_short, sma_long, adx, price]):
                return False
            
            # 更严格的趋势条件
            momentum = (price - sma_short) / sma_short if sma_short > 0 else 0
            
            return (sma_short > sma_long * 1.02 and  # 短期均线明显高于长期均线
                    adx > self.trend_strength_threshold and  # ADX超过强趋势阈值
                    price > sma_short and  # 价格在短期均线之上
                    momentum > self.momentum_threshold)  # 有足够的向上动量
            
        except Exception as e:
            logger.warning(f"检查超强趋势时出错: {e}")
            return False

    def _is_buy_trigger(self, data: Dict[str, Any]) -> bool:
        """买入触发条件"""
        try:
            rsi = data.get('RSI_14')
            if pd.isna(rsi) or rsi is None:
                return False
            
            return rsi < self.rsi_oversold
            
        except Exception as e:
            logger.warning(f"检查买入触发条件时出错: {e}")
            return False

    def _generate_entry_signal(self, symbol: str, data: Dict[str, Any], current_time: datetime) -> Optional[Dict[str, Any]]:
        """生成首次开仓信号（分批建仓第一批）"""
        try:
            # 计算动态止损距离
            dynamic_sl_multiple = self._calculate_dynamic_stop_loss_multiple(data)

            # 计算基础仓位大小
            base_position_size = self._calculate_base_position_size(symbol, data, dynamic_sl_multiple)
            if base_position_size is None or base_position_size <= 1e-9:
                return None

            # 检查是否为超强趋势，决定是否增加仓位
            is_very_strong = self._is_very_strong_uptrend(data)
            position_multiplier = self.trend_multiplier if is_very_strong else 1.0

            # 分批建仓：第一批使用基础仓位的1/batch_count
            first_batch_size = (base_position_size * position_multiplier) / self.batch_count

            # 创建信号
            signal = self._create_entry_signal(symbol, data, first_batch_size, dynamic_sl_multiple, current_time)

            if signal:
                # 记录分批建仓信息
                if symbol not in self.position_batches:
                    self.position_batches[symbol] = []

                batch_info = {
                    'batch_number': 1,
                    'size': first_batch_size,
                    'price': data.get('CLOSE'),
                    'timestamp': current_time,
                    'is_strong_trend': is_very_strong,
                    'sl_multiple': dynamic_sl_multiple
                }
                self.position_batches[symbol].append(batch_info)

                self.last_signal_time[symbol] = current_time

                # 更新每日交易计数
                current_date = current_time.date()
                key = f"{symbol}_{current_date}"
                self.daily_trade_count[key] = self.daily_trade_count.get(key, 0) + 1

                trend_info = "超强趋势" if is_very_strong else "普通趋势"
                logger.info(f"[{current_time}] {self.strategy_name}: {symbol} 首次开仓(1/{self.batch_count}批), "
                          f"仓位={first_batch_size:.4f}, {trend_info}, 动态止损倍数={dynamic_sl_multiple:.2f}")

            return signal

        except Exception as e:
            logger.warning(f"生成入场信号时出错: {e}")
            return None

    def _can_add_position(self, symbol: str, data: Dict[str, Any], current_time: datetime) -> bool:
        """检查是否可以加仓"""
        try:
            if symbol not in self.position_batches:
                return False

            batches = self.position_batches[symbol]
            if len(batches) >= self.batch_count:
                return False

            # 检查加仓间隔
            last_batch_time = batches[-1]['timestamp']
            time_diff = (current_time - last_batch_time).total_seconds() / 60
            if time_diff < self.batch_interval_minutes:
                return False

            # 检查是否仍在强趋势中
            return self._is_very_strong_uptrend(data)

        except Exception as e:
            logger.warning(f"检查加仓条件时出错: {e}")
            return False

    def _generate_add_position_signal(self, symbol: str, data: Dict[str, Any], current_time: datetime) -> Optional[Dict[str, Any]]:
        """生成加仓信号"""
        try:
            if symbol not in self.position_batches:
                return None

            batches = self.position_batches[symbol]
            next_batch_number = len(batches) + 1

            if next_batch_number > self.batch_count:
                return None

            # 计算加仓大小（与第一批相同）
            first_batch = batches[0]
            add_size = first_batch['size']

            # 使用当前的动态止损
            dynamic_sl_multiple = self._calculate_dynamic_stop_loss_multiple(data)

            # 创建加仓信号
            signal = self._create_entry_signal(symbol, data, add_size, dynamic_sl_multiple, current_time)

            if signal:
                # 记录加仓信息
                batch_info = {
                    'batch_number': next_batch_number,
                    'size': add_size,
                    'price': data.get('CLOSE'),
                    'timestamp': current_time,
                    'is_strong_trend': True,  # 能加仓说明趋势很强
                    'sl_multiple': dynamic_sl_multiple
                }
                self.position_batches[symbol].append(batch_info)

                # 更新每日交易计数
                current_date = current_time.date()
                key = f"{symbol}_{current_date}"
                self.daily_trade_count[key] = self.daily_trade_count.get(key, 0) + 1

                logger.info(f"[{current_time}] {self.strategy_name}: {symbol} 加仓({next_batch_number}/{self.batch_count}批), "
                          f"仓位={add_size:.4f}, 动态止损倍数={dynamic_sl_multiple:.2f}")

            return signal

        except Exception as e:
            logger.warning(f"生成加仓信号时出错: {e}")
            return None

    def _calculate_dynamic_stop_loss_multiple(self, data: Dict[str, Any]) -> float:
        """计算动态止损倍数（基于波动率和市场条件）"""
        try:
            # 获取ATR值作为波动率指标
            current_atr = data.get('ATR_14')
            if pd.isna(current_atr) or current_atr is None or current_atr <= 0:
                return self.atr_sl_multiple

            # 基于RSI调整止损距离
            rsi = data.get('RSI_14')
            if pd.isna(rsi) or rsi is None:
                return self.atr_sl_multiple

            # 基于ADX调整止损距离
            adx = data.get('ADX_14')
            if pd.isna(adx) or adx is None:
                adx = self.adx_threshold

            # 动态调整逻辑：
            # 1. RSI越低（超卖越严重），止损距离越大（给更多空间）
            # 2. ADX越高（趋势越强），止损距离可以适当放宽
            # 3. 波动率调整

            base_multiplier = self.atr_sl_multiple

            # RSI调整
            if rsi < 20:  # 极度超卖
                rsi_adjustment = 1.3
            elif rsi < 30:  # 超卖
                rsi_adjustment = 1.1
            elif rsi > 70:  # 超买
                rsi_adjustment = 0.8
            else:  # 正常范围
                rsi_adjustment = 1.0

            # ADX调整（趋势越强，可以给更多空间）
            if adx > self.trend_strength_threshold:  # 超强趋势
                adx_adjustment = 1.2
            elif adx > self.adx_threshold * 1.5:  # 强趋势
                adx_adjustment = 1.1
            else:  # 普通趋势
                adx_adjustment = 1.0

            # 综合调整
            final_multiplier = base_multiplier * rsi_adjustment * adx_adjustment

            # 确保在合理范围内
            return max(self.min_sl_multiple, min(self.max_sl_multiple, final_multiplier))

        except Exception as e:
            logger.warning(f"计算动态止损倍数时出错: {e}")
            return self.atr_sl_multiple

    def _calculate_base_position_size(self, symbol: str, data: Dict[str, Any], sl_multiple: float) -> Optional[float]:
        """计算基础仓位大小"""
        try:
            price = data.get('CLOSE')
            atr = data.get('ATR_14')

            if any(pd.isna(x) or x is None or x <= 0 for x in [price, atr]):
                return None

            # 模拟组合价值（实际应从引擎获取）
            portfolio_value = 100000  # 假设组合价值
            stop_loss_price = price - atr * sl_multiple

            # 计算风险金额
            max_risk_amount = portfolio_value * self.max_position_pct
            normal_risk_amount = portfolio_value * self.risk_per_trade_pct

            # 选择较小的风险金额
            risk_amount = min(max_risk_amount, normal_risk_amount)

            # 计算仓位大小
            risk_per_share = abs(price - stop_loss_price)
            if risk_per_share <= 0:
                return None

            size_abs = risk_amount / risk_per_share

            # 波动率调整
            if self.volatility_adjustment:
                volatility_factor = self._calculate_volatility_factor(data)
                size_abs *= volatility_factor

            return size_abs

        except Exception as e:
            logger.warning(f"计算基础仓位大小时出错: {e}")
            return None

    def _calculate_volatility_factor(self, data: Dict[str, Any]) -> float:
        """计算波动率调整因子"""
        try:
            atr = data.get('ATR_14')
            price = data.get('CLOSE')

            if any(pd.isna(x) or x is None or x <= 0 for x in [atr, price]):
                return 1.0

            # 计算ATR相对于价格的比例
            atr_ratio = atr / price

            # 根据波动率调整仓位
            # 高波动率时减少仓位，低波动率时增加仓位
            if atr_ratio > 0.05:  # 高波动率
                return 0.7
            elif atr_ratio > 0.03:  # 中等波动率
                return 0.85
            elif atr_ratio < 0.01:  # 低波动率
                return 1.2
            else:  # 正常波动率
                return 1.0

        except Exception as e:
            logger.warning(f"计算波动率因子时出错: {e}")
            return 1.0

    def _create_entry_signal(self, symbol: str, data: Dict[str, Any], size_abs: float,
                           sl_multiple: float, current_time: datetime) -> Optional[Dict[str, Any]]:
        """创建入场信号"""
        try:
            price = data.get('CLOSE')
            atr = data.get('ATR_14')

            if any(pd.isna(x) or x is None or x <= 0 for x in [price, atr]):
                return None

            # 使用动态止损倍数
            stop_loss_price = price - atr * sl_multiple
            take_profit_price = price + atr * self.atr_tp_multiple

            signal = {
                'symbol': symbol,
                'action': 'buy',
                'size': size_abs,
                'price': price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'timestamp': current_time,
                'strategy': self.strategy_name,
                'signal_type': 'entry_enhanced_alphax_v3',
                'reason': f'增强版开仓: RSI={data.get("RSI_14", 0):.2f}, ADX={data.get("ADX_14", 0):.2f}, 动态止损={sl_multiple:.2f}x'
            }

            return signal

        except Exception as e:
            logger.warning(f"创建入场信号时出错: {e}")
            return None

    def _update_trend_strength_history(self, symbol: str, data: Dict[str, Any]):
        """更新趋势强度历史"""
        try:
            adx = data.get('ADX_14')
            if pd.isna(adx) or adx is None:
                return

            if symbol not in self.trend_strength_history:
                self.trend_strength_history[symbol] = []

            self.trend_strength_history[symbol].append(adx)

            # 只保留最近的数据
            if len(self.trend_strength_history[symbol]) > self.volatility_lookback:
                self.trend_strength_history[symbol] = self.trend_strength_history[symbol][-self.volatility_lookback:]

        except Exception as e:
            logger.warning(f"更新趋势强度历史时出错: {e}")

    def _update_dynamic_stop_loss(self, symbol: str, data: Dict[str, Any]):
        """更新动态止损（持仓期间调整止损）"""
        try:
            price = data.get('CLOSE')
            atr = data.get('ATR_14')

            if any(pd.isna(x) or x is None or x <= 0 for x in [price, atr]):
                return

            # 计算新的动态止损价格
            new_sl_multiple = self._calculate_dynamic_stop_loss_multiple(data)
            new_stop_loss = price - atr * new_sl_multiple

            # 记录止损历史
            if symbol not in self.dynamic_sl_history:
                self.dynamic_sl_history[symbol] = []

            self.dynamic_sl_history[symbol].append(new_stop_loss)

            # 只保留最近的止损历史
            if len(self.dynamic_sl_history[symbol]) > 10:
                self.dynamic_sl_history[symbol] = self.dynamic_sl_history[symbol][-10:]

            # 这里可以添加实际更新止损订单的逻辑
            # 注意：只能向有利方向调整止损（对多头来说，只能上调止损）

        except Exception as e:
            logger.warning(f"更新动态止损时出错: {e}")

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息（用于监控和调试）"""
        info = {
            'strategy_name': self.strategy_name,
            'version': '3.0 - 增强版',
            'features': [
                '趋势跟踪：强趋势时增加仓位',
                '动态止损：基于波动率和RSI调整止损距离',
                '分批建仓：避免一次性满仓',
                '智能风险管理：波动率调整仓位',
                '每日交易限制：防止过度交易'
            ],
            'parameters': {
                'trend_strength_threshold': self.trend_strength_threshold,
                'trend_multiplier': self.trend_multiplier,
                'batch_count': self.batch_count,
                'batch_interval_minutes': self.batch_interval_minutes,
                'max_position_pct': self.max_position_pct,
                'min_sl_multiple': self.min_sl_multiple,
                'max_sl_multiple': self.max_sl_multiple,
                'max_daily_trades': self.max_daily_trades
            },
            'current_status': {
                'active_symbols': list(self.position_batches.keys()),
                'batch_counts': {symbol: len(batches) for symbol, batches in self.position_batches.items()},
                'last_signals': {symbol: time.isoformat() for symbol, time in self.last_signal_time.items()},
                'daily_trades': dict(self.daily_trade_count)
            }
        }
        return info

    def reset_position_tracking(self, symbol: str):
        """重置仓位跟踪（当仓位清零时调用）"""
        if symbol in self.position_batches:
            del self.position_batches[symbol]
        if symbol in self.dynamic_sl_history:
            del self.dynamic_sl_history[symbol]
        if symbol in self.trend_strength_history:
            del self.trend_strength_history[symbol]
        logger.info(f"{self.strategy_name}: 重置 {symbol} 的仓位跟踪信息")
