# -*- coding: utf-8 -*-
"""
策略库管理器 - 新版策略库管理系统
解决策略命名混乱、分类不清、使用不便的问题
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
import logging
import json
import os

logger = logging.getLogger(__name__)

class StrategyLibraryManager:
    """策略库管理器"""
    
    def __init__(self):
        self.strategy_catalog = self._load_strategy_catalog()
        self.strategy_mapping = self._create_strategy_mapping()
        self.performance_cache = {}
        
    def _load_strategy_catalog(self) -> Dict[str, Any]:
        """加载策略目录"""
        return {
            # 基础策略 (B类)
            "基础策略": {
                "代码": "B",
                "描述": "简单易懂的基础策略",
                "适用": "新手用户",
                "策略": [
                    {
                        "id": "B01",
                        "原名": "MeanReversionStrategy",
                        "简名": "均值回归",
                        "全名": "B01_均值回归",
                        "描述": "基于布林带和RSI的均值回归策略",
                        "标签": ["基础", "均值回归", "布林带", "RSI", "新手友好"],
                        "难度": "初级",
                        "风险": "低",
                        "收益潜力": "低",
                        "稳定性": "中"
                    },
                    {
                        "id": "B02", 
                        "原名": "TrendFollowingStrategy",
                        "简名": "趋势跟踪",
                        "全名": "B02_趋势跟踪",
                        "描述": "基于移动平均和ADX的趋势跟踪策略",
                        "标签": ["基础", "趋势跟踪", "移动平均", "ADX", "新手友好"],
                        "难度": "初级",
                        "风险": "低", 
                        "收益潜力": "低",
                        "稳定性": "中"
                    }
                ]
            },
            
            # 优化策略 (O类)
            "优化策略": {
                "代码": "O",
                "描述": "经过参数优化的策略",
                "适用": "有经验用户",
                "策略": [
                    {
                        "id": "O01",
                        "原名": "OptimizedMeanReversionStrategy", 
                        "简名": "优化均值回归",
                        "全名": "O01_优化均值回归",
                        "描述": "参数优化的均值回归策略",
                        "标签": ["优化", "均值回归", "参数优化", "风险控制"],
                        "难度": "中级",
                        "风险": "低",
                        "收益潜力": "中",
                        "稳定性": "中"
                    },
                    {
                        "id": "O02",
                        "原名": "BalancedMeanReversionStrategy",
                        "简名": "平衡均值回归", 
                        "全名": "O02_平衡均值回归",
                        "描述": "平衡风险收益的均值回归策略",
                        "标签": ["优化", "均值回归", "平衡", "稳健"],
                        "难度": "中级",
                        "风险": "低",
                        "收益潜力": "中",
                        "稳定性": "高"
                    },
                    {
                        "id": "O03",
                        "原名": "ImprovedTrendFollowingStrategy",
                        "简名": "改进趋势跟踪",
                        "全名": "O03_改进趋势跟踪", 
                        "描述": "信号过滤和动态止损的改进趋势策略",
                        "标签": ["优化", "趋势跟踪", "信号过滤", "动态止损"],
                        "难度": "中级",
                        "风险": "中",
                        "收益潜力": "中",
                        "稳定性": "中"
                    }
                ]
            },
            
            # AI策略 (A类)
            "AI策略": {
                "代码": "A",
                "描述": "使用机器学习的智能策略",
                "适用": "高级用户",
                "策略": [
                    {
                        "id": "A01",
                        "原名": "OptimizedTrendStrategyAI",
                        "简名": "AI趋势预测",
                        "全名": "A01_AI趋势预测",
                        "描述": "使用机器学习预测趋势的策略",
                        "标签": ["AI", "机器学习", "趋势预测", "高级"],
                        "难度": "高级",
                        "风险": "中",
                        "收益潜力": "中",
                        "稳定性": "中"
                    },
                    {
                        "id": "A02",
                        "原名": "AIOptimizedAlphaXStrategy",
                        "简名": "AI优化阿尔法",
                        "全名": "A02_AI优化阿尔法",
                        "描述": "AI优化的阿尔法策略",
                        "标签": ["AI", "随机森林", "特征工程", "高级"],
                        "难度": "专家",
                        "风险": "中",
                        "收益潜力": "高",
                        "稳定性": "中"
                    },
                    {
                        "id": "A03",
                        "原名": "MLEnhancedTrendFollowingStrategy",
                        "简名": "ML增强趋势",
                        "全名": "A03_ML增强趋势",
                        "描述": "机器学习增强的趋势跟踪策略",
                        "标签": ["AI", "机器学习", "趋势跟踪", "信号质量"],
                        "难度": "专家",
                        "风险": "中",
                        "收益潜力": "中",
                        "稳定性": "低"
                    }
                ]
            },
            
            # 高级策略 (H类)
            "高级策略": {
                "代码": "H",
                "描述": "复杂的高级策略",
                "适用": "专业用户",
                "策略": [
                    {
                        "id": "H01",
                        "原名": "AlphaXInspiredStrategy",
                        "简名": "阿尔法X",
                        "全名": "H01_阿尔法X",
                        "描述": "分批建仓和动态止损的高级策略",
                        "标签": ["高级", "分批建仓", "动态止损", "趋势跟踪"],
                        "难度": "高级",
                        "风险": "中",
                        "收益潜力": "高",
                        "稳定性": "中"
                    },
                    {
                        "id": "H02",
                        "原名": "MultiTimeframeTrendFollowingStrategy",
                        "简名": "多时间框架",
                        "全名": "H02_多时间框架",
                        "描述": "多时间框架趋势确认策略",
                        "标签": ["高级", "多时间框架", "趋势确认", "复杂"],
                        "难度": "专家",
                        "风险": "中",
                        "收益潜力": "中",
                        "稳定性": "中"
                    }
                ]
            },
            
            # 保守策略 (C类)
            "保守策略": {
                "代码": "C",
                "描述": "低风险保守策略",
                "适用": "风险厌恶用户",
                "策略": [
                    {
                        "id": "C01",
                        "原名": "UltraConservativeStrategy",
                        "简名": "超保守",
                        "全名": "C01_超保守",
                        "描述": "极低风险的保守策略",
                        "标签": ["保守", "低风险", "稳健", "防守型"],
                        "难度": "初级",
                        "风险": "低",
                        "收益潜力": "低",
                        "稳定性": "高"
                    }
                ]
            },
            
            # 特殊策略 (S类)
            "特殊策略": {
                "代码": "S",
                "描述": "特定目标的策略",
                "适用": "特定需求用户",
                "策略": [
                    {
                        "id": "S01",
                        "原名": "FinalProfitableStrategy",
                        "简名": "最终盈利",
                        "全名": "S01_最终盈利",
                        "描述": "专门优化盈利能力的策略",
                        "标签": ["特殊", "盈利导向", "综合策略"],
                        "难度": "特殊",
                        "风险": "中",
                        "收益潜力": "中",
                        "稳定性": "中"
                    },
                    {
                        "id": "S02",
                        "原名": "MonthlyProfitableStrategy",
                        "简名": "月月盈利",
                        "全名": "S02_月月盈利",
                        "描述": "专门针对月度盈利优化的策略",
                        "标签": ["特殊", "月度目标", "稳定盈利"],
                        "难度": "特殊",
                        "风险": "中",
                        "收益潜力": "中",
                        "稳定性": "高"
                    }
                ]
            }
        }
    
    def _create_strategy_mapping(self) -> Dict[str, Dict[str, str]]:
        """创建策略映射表"""
        mapping = {}
        
        for category_info in self.strategy_catalog.values():
            for strategy in category_info['策略']:
                original = strategy['原名']
                mapping[original] = strategy
                # 同时支持简名和全名查找
                mapping[strategy['简名']] = strategy
                mapping[strategy['全名']] = strategy
                mapping[strategy['id']] = strategy
        
        return mapping
    
    def get_strategy_info(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """获取策略信息"""
        return self.strategy_mapping.get(strategy_name)
    
    def list_strategies(self, category: Optional[str] = None, 
                       difficulty: Optional[str] = None,
                       risk_level: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出策略"""
        strategies = []
        
        for category_name, category_info in self.strategy_catalog.items():
            if category and category != category_name:
                continue
                
            for strategy in category_info['策略']:
                if difficulty and strategy['难度'] != difficulty:
                    continue
                if risk_level and strategy['风险'] != risk_level:
                    continue
                
                strategies.append({
                    **strategy,
                    'category': category_name
                })
        
        return strategies
    
    def search_strategies(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索策略"""
        results = []
        keyword_lower = keyword.lower()
        
        for category_name, category_info in self.strategy_catalog.items():
            for strategy in category_info['策略']:
                # 搜索名称、描述、标签
                searchable_text = (
                    strategy['简名'] + ' ' +
                    strategy['描述'] + ' ' +
                    ' '.join(strategy['标签'])
                ).lower()
                
                if keyword_lower in searchable_text:
                    results.append({
                        **strategy,
                        'category': category_name
                    })
        
        return results
    
    def recommend_strategies(self, user_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推荐策略"""
        experience = user_profile.get('experience', '新手')
        risk_tolerance = user_profile.get('risk_tolerance', '低')
        goal = user_profile.get('goal', '稳健')
        
        recommendations = []
        
        # 根据经验水平过滤
        if experience == '新手':
            difficulty_filter = ['初级']
        elif experience == '有经验':
            difficulty_filter = ['初级', '中级']
        elif experience == '专家':
            difficulty_filter = ['中级', '高级', '专家']
        else:
            difficulty_filter = None
        
        # 根据风险偏好过滤
        if risk_tolerance == '低':
            risk_filter = ['低']
        elif risk_tolerance == '中':
            risk_filter = ['低', '中']
        else:
            risk_filter = ['低', '中', '高']
        
        # 获取符合条件的策略
        for category_name, category_info in self.strategy_catalog.items():
            for strategy in category_info['策略']:
                # 检查难度
                if difficulty_filter and strategy['难度'] not in difficulty_filter:
                    continue
                
                # 检查风险
                if strategy['风险'] not in risk_filter:
                    continue
                
                # 根据目标加权
                score = 0
                if goal == '稳健' and strategy['稳定性'] == '高':
                    score += 3
                elif goal == '收益' and strategy['收益潜力'] == '高':
                    score += 3
                elif goal == '技术' and 'AI' in strategy['标签']:
                    score += 3
                
                recommendations.append({
                    **strategy,
                    'category': category_name,
                    'score': score
                })
        
        # 按分数排序
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:5]
    
    def get_strategy_comparison(self, strategy_names: List[str]) -> pd.DataFrame:
        """策略对比"""
        comparison_data = []
        
        for name in strategy_names:
            info = self.get_strategy_info(name)
            if info:
                comparison_data.append({
                    '策略名称': info['简名'],
                    '策略ID': info['id'],
                    '难度': info['难度'],
                    '风险等级': info['风险'],
                    '收益潜力': info['收益潜力'],
                    '稳定性': info['稳定性'],
                    '主要标签': ', '.join(info['标签'][:3])
                })
        
        return pd.DataFrame(comparison_data)
    
    def print_strategy_catalog(self):
        """打印策略目录"""
        print("📚 量化交易策略库目录 (重构版)")
        print("=" * 80)
        
        for category_name, category_info in self.strategy_catalog.items():
            print(f"\n🔸 {category_name} ({category_info['代码']}类)")
            print(f"   描述: {category_info['描述']}")
            print(f"   适用: {category_info['适用']}")
            print("   " + "-" * 50)
            
            for strategy in category_info['策略']:
                print(f"   📌 {strategy['简名']} ({strategy['id']})")
                print(f"      描述: {strategy['描述']}")
                print(f"      难度: {strategy['难度']} | 风险: {strategy['风险']} | 收益潜力: {strategy['收益潜力']}")
                print(f"      标签: {', '.join(strategy['标签'])}")
                print()
    
    def get_original_strategy_name(self, new_name: str) -> Optional[str]:
        """获取原始策略名称（用于向后兼容）"""
        info = self.get_strategy_info(new_name)
        return info['原名'] if info else None

# 创建全局实例
strategy_manager = StrategyLibraryManager()

def get_strategy_manager() -> StrategyLibraryManager:
    """获取策略管理器实例"""
    return strategy_manager

if __name__ == "__main__":
    # 演示用法
    manager = StrategyLibraryManager()
    
    # 打印目录
    manager.print_strategy_catalog()
    
    # 搜索示例
    print("\n🔍 搜索 'AI' 相关策略:")
    ai_strategies = manager.search_strategies('AI')
    for strategy in ai_strategies:
        print(f"  - {strategy['简名']} ({strategy['id']})")
    
    # 推荐示例
    print("\n🎯 新手用户推荐:")
    user_profile = {'experience': '新手', 'risk_tolerance': '低', 'goal': '稳健'}
    recommendations = manager.recommend_strategies(user_profile)
    for rec in recommendations:
        print(f"  - {rec['简名']} (分数: {rec['score']})")
    
    # 对比示例
    print("\n📊 策略对比:")
    comparison = manager.get_strategy_comparison(['B01', 'O01', 'A01'])
    print(comparison.to_string(index=False))
