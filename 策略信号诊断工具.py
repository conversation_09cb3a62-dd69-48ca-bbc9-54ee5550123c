# -*- coding: utf-8 -*-
"""
策略信号诊断工具
分析为什么P01阿尔法X策略无法产生交易信号
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class SignalDiagnosticTool:
    """策略信号诊断工具"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
    
    def load_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载数据"""
        try:
            print(f"📊 加载数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到数据")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = data.copy()
        
        # 移动平均线
        df['SMA_10'] = df['CLOSE'].rolling(10).mean()
        df['SMA_20'] = df['CLOSE'].rolling(20).mean()
        df['SMA_50'] = df['CLOSE'].rolling(50).mean()
        
        # RSI
        delta = df['CLOSE'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # ATR
        high_low = df['HIGH'] - df['LOW']
        high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
        low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['ATR'] = true_range.rolling(14).mean()
        
        # ADX (简化版)
        df['ADX'] = df['CLOSE'].rolling(14).std() / df['CLOSE'].rolling(14).mean() * 100
        
        # 成交量
        df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
        
        # 动量
        df['Momentum'] = df['CLOSE'].pct_change(5)
        
        return df
    
    def analyze_signal_conditions(self, data: pd.DataFrame):
        """分析信号生成条件"""
        print("\n🔍 信号条件分析")
        print("=" * 60)
        
        # 计算指标
        data = self.calculate_indicators(data)
        
        # 从第50个数据点开始分析
        analysis_data = data.iloc[50:].copy()
        total_points = len(analysis_data)
        
        print(f"分析数据点数: {total_points}")
        print()
        
        # 分析各种条件的满足情况
        conditions = {}
        
        # 1. 趋势条件
        conditions['SMA_10 > SMA_20'] = (analysis_data['SMA_10'] > analysis_data['SMA_20']).sum()
        conditions['SMA_20 > SMA_50'] = (analysis_data['SMA_20'] > analysis_data['SMA_50']).sum()
        conditions['Price > SMA_10'] = (analysis_data['CLOSE'] > analysis_data['SMA_10']).sum()
        
        # 2. RSI条件
        conditions['RSI < 70'] = (analysis_data['RSI'] < 70).sum()
        conditions['RSI < 60'] = (analysis_data['RSI'] < 60).sum()
        conditions['RSI < 50'] = (analysis_data['RSI'] < 50).sum()
        conditions['RSI > 30'] = (analysis_data['RSI'] > 30).sum()
        conditions['RSI > 40'] = (analysis_data['RSI'] > 40).sum()
        
        # 3. ADX条件
        conditions['ADX > 15'] = (analysis_data['ADX'] > 15).sum()
        conditions['ADX > 20'] = (analysis_data['ADX'] > 20).sum()
        conditions['ADX > 25'] = (analysis_data['ADX'] > 25).sum()
        conditions['ADX > 30'] = (analysis_data['ADX'] > 30).sum()
        
        # 4. 成交量条件
        volume_condition = analysis_data['VOLUME'] > analysis_data['Volume_MA'] * 1.2
        conditions['Volume > 1.2x MA'] = volume_condition.sum()
        
        # 5. 动量条件
        conditions['Momentum > 0'] = (analysis_data['Momentum'] > 0).sum()
        conditions['Momentum > 0.005'] = (analysis_data['Momentum'] > 0.005).sum()
        conditions['Momentum > 0.01'] = (analysis_data['Momentum'] > 0.01).sum()
        
        # 打印条件满足情况
        print("📋 各条件满足情况:")
        print("-" * 50)
        for condition, count in conditions.items():
            percentage = count / total_points * 100
            print(f"{condition:<20}: {count:>6}/{total_points} ({percentage:>5.1f}%)")
        
        print()
        
        # 分析组合条件
        print("🔗 组合条件分析:")
        print("-" * 50)
        
        # 基础趋势组合
        basic_trend = (
            (analysis_data['SMA_10'] > analysis_data['SMA_20']) &
            (analysis_data['CLOSE'] > analysis_data['SMA_10'])
        )
        print(f"基础趋势条件: {basic_trend.sum()}/{total_points} ({basic_trend.sum()/total_points*100:.1f}%)")
        
        # 宽松条件组合
        loose_combo = (
            basic_trend &
            (analysis_data['RSI'] < 70) &
            (analysis_data['ADX'] > 15)
        )
        print(f"宽松组合条件: {loose_combo.sum()}/{total_points} ({loose_combo.sum()/total_points*100:.1f}%)")
        
        # 中等条件组合
        medium_combo = (
            basic_trend &
            (analysis_data['RSI'] < 60) &
            (analysis_data['ADX'] > 20) &
            (analysis_data['VOLUME'] > analysis_data['Volume_MA'])
        )
        print(f"中等组合条件: {medium_combo.sum()}/{total_points} ({medium_combo.sum()/total_points*100:.1f}%)")
        
        # 严格条件组合
        strict_combo = (
            basic_trend &
            (analysis_data['RSI'] < 50) &
            (analysis_data['ADX'] > 25) &
            (analysis_data['VOLUME'] > analysis_data['Volume_MA'] * 1.2) &
            (analysis_data['Momentum'] > 0.005)
        )
        print(f"严格组合条件: {strict_combo.sum()}/{total_points} ({strict_combo.sum()/total_points*100:.1f}%)")
        
        print()
        
        # 推荐参数
        print("💡 推荐参数设置:")
        print("-" * 50)
        
        if loose_combo.sum() > total_points * 0.05:  # 至少5%的时间满足
            print("✅ 建议使用宽松条件组合:")
            print("   - ADX阈值: 15")
            print("   - RSI上限: 70")
            print("   - 成交量倍数: 1.0")
        elif medium_combo.sum() > total_points * 0.02:  # 至少2%的时间满足
            print("⚠️ 建议使用中等条件组合:")
            print("   - ADX阈值: 20")
            print("   - RSI上限: 60")
            print("   - 成交量倍数: 1.0")
        else:
            print("❌ 当前市场条件下很难产生信号，建议:")
            print("   - 进一步放宽条件")
            print("   - 考虑不同的策略逻辑")
            print("   - 分析市场特性")
        
        # 市场特性分析
        print(f"\n📈 市场特性分析:")
        print("-" * 50)
        
        price_change = (analysis_data['CLOSE'].iloc[-1] - analysis_data['CLOSE'].iloc[0]) / analysis_data['CLOSE'].iloc[0] * 100
        volatility = analysis_data['CLOSE'].pct_change().std() * 100
        avg_volume = analysis_data['VOLUME'].mean()
        
        print(f"期间价格变化: {price_change:+.2f}%")
        print(f"平均波动率: {volatility:.2f}%")
        print(f"平均成交量: {avg_volume:,.0f}")
        
        if price_change > 10:
            print("📊 市场特征: 强上涨趋势")
            print("💡 建议: 使用趋势跟踪策略")
        elif price_change < -10:
            print("📊 市场特征: 强下跌趋势")
            print("💡 建议: 避免做多，考虑做空策略")
        else:
            print("📊 市场特征: 震荡市场")
            print("💡 建议: 使用均值回归策略")
        
        return analysis_data

def main():
    """主函数"""
    print("🔍 启动策略信号诊断工具...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    diagnostic = SignalDiagnosticTool()
    
    # 分析2024年4月数据
    try:
        data = diagnostic.load_data('2024-04-01', '2024-04-30')
        analysis_result = diagnostic.analyze_signal_conditions(data)
        
        print(f"\n🎯 诊断完成!")
        print("基于以上分析，可以调整策略参数以产生更多交易信号")
        
    except Exception as e:
        logger.error(f"诊断失败: {e}")

if __name__ == "__main__":
    main()
