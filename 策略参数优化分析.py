# -*- coding: utf-8 -*-
"""
策略参数优化分析 - 达到客户收益风险比目标
目标：年化收益≥15%、夏普比率≥2、最大回撤≤15%
"""
import sys
import os

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def analyze_current_performance():
    """分析当前策略表现"""
    print("=== 当前策略表现分析 ===")
    print("测试期间：2024年4月 (30天)")
    print()
    
    current_results = {
        'AlphaXInspiredStrategy': {
            '月收益率': 5.44,
            '年化收益率': 65.28,  # 这个计算可能有问题
            '最大回撤': 4.56,
            '夏普比率': 245.0,
            '交易次数': 25
        }
    }
    
    # 重新计算年化收益率
    monthly_return = 5.44 / 100  # 5.44%
    # 正确的年化收益率计算：(1 + 月收益率)^12 - 1
    annual_return = (1 + monthly_return) ** 12 - 1
    
    print(f"当前AlphaXInspiredStrategy表现：")
    print(f"• 月收益率：{monthly_return*100:.2f}%")
    print(f"• 年化收益率（正确计算）：{annual_return*100:.2f}%")
    print(f"• 最大回撤：{current_results['AlphaXInspiredStrategy']['最大回撤']:.2f}%")
    print(f"• 夏普比率：{current_results['AlphaXInspiredStrategy']['夏普比率']:.2f}")
    print()
    
    # 客户目标对比
    print("=== 客户目标对比 ===")
    targets = {
        '年化收益率': {'目标': 15, '当前': annual_return*100, '单位': '%'},
        '夏普比率': {'目标': 2, '当前': 245.0, '单位': ''},
        '最大回撤': {'目标': 15, '当前': 4.56, '单位': '%'}
    }
    
    for metric, data in targets.items():
        current = data['当前']
        target = data['目标']
        unit = data['单位']
        
        if metric == '最大回撤':
            status = "✅ 达标" if current <= target else "❌ 超标"
        else:
            status = "✅ 达标" if current >= target else "❌ 未达标"
            
        print(f"• {metric}: {current:.2f}{unit} (目标: {target}{unit}) {status}")
    
    print()
    print("=== 问题分析 ===")
    if annual_return * 100 >= 15:
        print("✅ 年化收益率已达标")
    else:
        print("❌ 年化收益率未达标，需要提升策略盈利能力")
    
    print("✅ 夏普比率远超目标")
    print("✅ 最大回撤控制良好")
    print()
    
    return annual_return * 100

def create_optimized_strategy_v2():
    """创建优化策略V2版本"""
    
    strategy_code = '''# -*- coding: utf-8 -*-
"""
增强版AlphaX策略V2 - 针对客户收益风险比目标优化
目标：年化收益≥15%、夏普比率≥2、最大回撤≤15%
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta

class EnhancedAlphaXStrategyV2:
    """增强版AlphaX策略V2"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'EnhancedAlphaXStrategyV2'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 优化后的参数 - 提高盈利能力
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_10')  # 缩短至10期
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_30')   # 缩短至30期
        self.adx_threshold = all_params.get('adx_threshold', 20)        # 降低趋势要求
        self.rsi_oversold = all_params.get('rsi_oversold', 30)         # 放宽超卖条件
        self.rsi_overbought = all_params.get('rsi_overbought', 70)     # 添加超买条件
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.02)  # 提高单笔风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)         # 适中止损
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 4.0)         # 适中止盈
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 60)  # 缩短信号间隔
        
        # 新增多空策略
        self.enable_short = all_params.get('enable_short', True)       # 启用做空
        self.volume_threshold = all_params.get('volume_threshold', 1.1) # 降低成交量要求
        self.max_daily_trades = all_params.get('max_daily_trades', 4)   # 增加每日交易次数
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        print(f"策略 EnhancedAlphaXStrategyV2 初始化...")
        print(f"EnhancedAlphaXStrategyV2: 优化参数 - RSI范围=[{self.rsi_oversold}, {self.rsi_overbought}], "
              f"ADX阈值={self.adx_threshold}, 风险={self.risk_per_trade_pct*100}%, 多空={self.enable_short}")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查成交量确认"""
        current_volume = data.get('VOLUME', 0)
        avg_volume = data.get('VOLUME_SMA_20', current_volume)
        
        if avg_volume <= 0:
            return True
        
        volume_ratio = current_volume / avg_volume
        return volume_ratio >= self.volume_threshold
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 支持多空"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.sma_short_key, self.sma_long_key, 'ADX_14', 'RSI_14', 'ATR_14']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        if not self.check_volume_confirmation(data):
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key)
        adx = data.get('ADX_14')
        rsi = data.get('RSI_14')
        atr = data.get('ATR_14')
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, sma_short, sma_long, adx, rsi, atr]):
            return signals
        
        # 计算仓位大小
        portfolio_value = 100000  # 假设组合价值
        risk_amount = portfolio_value * self.risk_per_trade_pct
        position_size = risk_amount / (atr * self.atr_sl_multiple)
        
        # 做多条件
        long_conditions = [
            sma_short > sma_long,  # 短期均线在长期均线之上
            adx >= self.adx_threshold,  # 趋势强度足够
            rsi <= self.rsi_oversold,  # RSI超卖
            price > sma_short * 0.998  # 价格接近短期均线
        ]
        
        if all(long_conditions):
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'EnhancedAlphaXStrategyV2',
                'signal_type': 'long_entry',
                'reason': f'做多信号: RSI={rsi:.2f}, ADX={adx:.2f}, 趋势向上'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] EnhancedAlphaXStrategyV2: 做多信号 BTCUSDT, "
                  f"RSI={rsi:.2f}, ADX={adx:.2f}, 数量={position_size:.4f}")
        
        # 做空条件（如果启用）
        elif self.enable_short:
            short_conditions = [
                sma_short < sma_long,  # 短期均线在长期均线之下
                adx >= self.adx_threshold,  # 趋势强度足够
                rsi >= self.rsi_overbought,  # RSI超买
                price < sma_short * 1.002  # 价格接近短期均线
            ]
            
            if all(short_conditions):
                stop_loss = price + (atr * self.atr_sl_multiple)
                take_profit = price - (atr * self.atr_tp_multiple)
                
                signal = {
                    'symbol': 'BTCUSDT',
                    'action': 'sell',
                    'size': position_size,
                    'price': price,
                    'stop_loss_price': stop_loss,
                    'take_profit_price': take_profit,
                    'timestamp': current_time,
                    'strategy': 'EnhancedAlphaXStrategyV2',
                    'signal_type': 'short_entry',
                    'reason': f'做空信号: RSI={rsi:.2f}, ADX={adx:.2f}, 趋势向下'
                }
                
                signals.append(signal)
                self.last_signal_time = current_time
                self.daily_trade_count += 1
                
                print(f"[{current_time}] EnhancedAlphaXStrategyV2: 做空信号 BTCUSDT, "
                      f"RSI={rsi:.2f}, ADX={adx:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'EnhancedAlphaXStrategyV2',
            'version': '2.0',
            'description': '增强版AlphaX策略，优化收益风险比，支持多空交易',
            'parameters': {
                'sma_short_key': self.sma_short_key,
                'sma_long_key': self.sma_long_key,
                'adx_threshold': self.adx_threshold,
                'rsi_oversold': self.rsi_oversold,
                'rsi_overbought': self.rsi_overbought,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'enable_short': self.enable_short,
                'min_signal_interval_minutes': self.min_signal_interval_minutes
            },
            'targets': {
                'annual_return': '≥15%',
                'sharpe_ratio': '≥2',
                'max_drawdown': '≤15%'
            }
        }
'''
    
    # 保存策略文件
    strategy_file_path = os.path.join(CORE_CODE_PATH, '交易策略', 'enhanced_alphax_strategy_v2.py')
    
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.write(strategy_code)
    
    print(f"增强策略V2已保存到: {strategy_file_path}")
    return strategy_file_path

def update_strategy_library_v2():
    """更新策略库，添加V2策略"""
    
    strategy_lib_path = os.path.join(CORE_CODE_PATH, '交易策略', '策略库.py')
    
    # 读取现有策略库文件
    with open(strategy_lib_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加V2策略导入
    import_line = "from .enhanced_alphax_strategy_v2 import EnhancedAlphaXStrategyV2"
    
    if import_line not in content:
        # 在文件末尾添加
        additional_content = f'''

# 增强策略V2导入
{import_line}

# 更新策略字典
STRATEGIES['EnhancedAlphaXStrategyV2'] = EnhancedAlphaXStrategyV2
'''
        content += additional_content
        
        # 写回文件
        with open(strategy_lib_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"策略库已更新: {strategy_lib_path}")

if __name__ == '__main__':
    print("策略参数优化分析 - 达到客户收益风险比目标")
    print("=" * 60)
    
    # 分析当前表现
    current_annual_return = analyze_current_performance()
    
    print("=== 优化策略 ===")
    print("基于分析结果，创建增强版策略V2：")
    print("• 缩短均线周期：SMA_10/SMA_30（提高信号敏感度）")
    print("• 降低ADX阈值：20（增加交易机会）")
    print("• 放宽RSI条件：30/70（扩大交易范围）")
    print("• 增加单笔风险：2%（提高收益潜力）")
    print("• 启用做空交易（双向获利）")
    print("• 缩短信号间隔：60分钟（增加交易频率）")
    print("• 增加每日交易：4次（提高资金利用率）")
    print()
    
    # 创建优化策略
    create_optimized_strategy_v2()
    update_strategy_library_v2()
    
    print("\n=== 下一步测试 ===")
    print("运行以下命令测试优化策略：")
    print("./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2024-04-01 --end_date 2024-04-30 --strategy EnhancedAlphaXStrategyV2")
    
    print("\n=== 预期改进 ===")
    print("• 通过多空交易提高收益率")
    print("• 通过更频繁交易增加盈利机会") 
    print("• 保持良好的风险控制")
    print("• 目标达到年化收益≥15%")
