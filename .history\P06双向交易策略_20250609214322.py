# -*- coding: utf-8 -*-
"""
P06双向交易策略
模拟大机构的双向交易能力：上涨做多，下跌做空
这是关键突破：让我们能在任何市场环境下都有盈利机会
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class BidirectionalTradingStrategy:
    """P06双向交易策略 - 模拟大机构的做多做空能力"""

    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"

        # 双向交易策略参数
        self.strategy_params = {
            # 技术指标参数
            'ma_short': 12,                # 短期均线
            'ma_long': 26,                 # 长期均线
            'rsi_period': 14,              # RSI周期
            'macd_signal': 9,              # MACD信号线

            # 做多信号阈值
            'long_rsi_min': 35,            # 做多RSI最小值
            'long_rsi_max': 65,            # 做多RSI最大值
            'long_momentum_min': 0.002,    # 做多最小动量

            # 做空信号阈值
            'short_rsi_min': 35,           # 做空RSI最小值
            'short_rsi_max': 65,           # 做空RSI最大值
            'short_momentum_max': -0.002,  # 做空最大动量(负值)

            # 仓位管理
            'position_size': 0.25,         # 单向仓位25%
            'max_total_exposure': 0.5,     # 最大总敞口50%
            'allow_hedging': True,         # 允许对冲(同时持有多空)

            # 风险控制
            'stop_loss_long': 0.025,       # 做多止损2.5%
            'take_profit_long': 0.05,      # 做多止盈5%
            'stop_loss_short': 0.025,      # 做空止损2.5%
            'take_profit_short': 0.05,     # 做空止盈5%

            # 交易控制
            'min_signal_gap': 30,          # 最小信号间隔30分钟
            'max_holding_hours': 12,       # 最大持仓12小时
            'force_close_opposite': True,  # 反向信号时强制平仓
        }

    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")

            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)

            all_data = []
            current_date = start_dt

            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month

                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)

                if os.path.exists(filepath):
                    print(f"  加载: {filename}")

                    df = pd.read_csv(filepath)

                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])

                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)

                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH',
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)

                    df = df[(df.index >= start_date) & (df.index <= end_date)]

                    if not df.empty:
                        all_data.append(df)

                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)

            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")

            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]

            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data

        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise

    def calculate_bidirectional_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算双向交易指标"""
        try:
            df = data.copy()

            # 移动平均线
            df['MA_Short'] = df['CLOSE'].ewm(span=self.strategy_params['ma_short']).mean()
            df['MA_Long'] = df['CLOSE'].ewm(span=self.strategy_params['ma_long']).mean()

            # MACD指标
            df['MACD'] = df['MA_Short'] - df['MA_Long']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['macd_signal']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']

            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # 价格动量
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)

            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']

            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()

            # 趋势强度
            df['Trend_Strength'] = abs(df['MACD']) / df['CLOSE']

            return df

        except Exception as e:
            logger.warning(f"计算双向交易指标失败: {e}")
            return data

    def generate_bidirectional_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成双向交易信号"""
        try:
            # 初始化信号
            df['Long_Signal'] = 0    # 做多信号
            df['Short_Signal'] = 0   # 做空信号
            df['Signal_Strength'] = 0

            # 做多信号条件
            long_conditions = (
                (df['MACD'] > df['MACD_Signal']) &  # MACD金叉
                (df['MACD_Histogram'] > 0) &        # 柱状图为正
                (df['MA_Short'] > df['MA_Long']) &  # 短期均线在长期均线上方
                (df['RSI'] >= self.strategy_params['long_rsi_min']) &
                (df['RSI'] <= self.strategy_params['long_rsi_max']) &
                (df['Momentum_5'] > self.strategy_params['long_momentum_min']) &
                (df['Volume_Ratio'] > 0.8)          # 成交量支持
            )

            # 做空信号条件
            short_conditions = (
                (df['MACD'] < df['MACD_Signal']) &  # MACD死叉
                (df['MACD_Histogram'] < 0) &        # 柱状图为负
                (df['MA_Short'] < df['MA_Long']) &  # 短期均线在长期均线下方
                (df['RSI'] >= self.strategy_params['short_rsi_min']) &
                (df['RSI'] <= self.strategy_params['short_rsi_max']) &
                (df['Momentum_5'] < self.strategy_params['short_momentum_max']) &
                (df['Volume_Ratio'] > 0.8)          # 成交量支持
            )

            # 设置信号
            df.loc[long_conditions, 'Long_Signal'] = 1
            df.loc[short_conditions, 'Short_Signal'] = 1

            # 计算信号强度
            df['Signal_Strength'] = (
                abs(df['MACD_Histogram']) * 0.4 +
                df['Trend_Strength'] * 100 * 0.3 +
                abs(df['Momentum_5']) * 100 * 0.2 +
                (df['Volume_Ratio'] - 1) * 0.1
            )

            return df

        except Exception as e:
            logger.error(f"生成双向交易信号失败: {e}")
            return df