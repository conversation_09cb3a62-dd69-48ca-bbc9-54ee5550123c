# -*- coding: utf-8 -*-
"""
策略全面对比测试 - 2024年4月BTCUSDT数据
测试所有可用策略的性能表现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import json
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] [%(name)s]: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('策略对比测试_2024年4月.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def get_all_strategies():
    """获取所有可用策略"""
    try:
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 过滤掉None值和不可用的策略
        available_strategies = {}
        for name, strategy_class in STRATEGIES.items():
            if strategy_class is not None:
                available_strategies[name] = strategy_class
        
        return available_strategies
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        return {}

def get_strategy_default_params(strategy_name):
    """获取策略的默认参数"""
    
    # 基础参数（适用于大多数策略）
    base_params = {
        'risk_per_trade_pct': 0.01,
        'atr_sl_multiple': 2.0,
        'atr_tp_multiple': 4.0,
        'min_signal_interval_minutes': 120
    }
    
    # 策略特定参数
    strategy_specific_params = {
        'MeanReversion': {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'bb_period': 20,
            'bb_std': 2.0
        },
        'TrendFollowing': {
            'sma_short': 20,
            'sma_long': 50,
            'rsi_threshold': 50
        },
        'OptimizedTrendStrategyAI': {
            'prediction_threshold': 0.6,
            'confidence_threshold': 0.7
        },
        'AlphaXInspiredStrategy': {
            'adx_threshold': 25.0,
            'rsi_oversold': 35.0
        },
        'EnhancedAlphaXStrategyV3': {
            'trend_multiplier': 1.5,
            'batch_count': 3,
            'batch_interval_minutes': 30,
            'max_position_pct': 0.03,
            'min_sl_multiple': 1.5,
            'max_sl_multiple': 3.0,
            'trend_strength_threshold': 40.0
        },
        'OptimizedAlphaXStrategy': {
            'adx_threshold': 25.0,
            'rsi_oversold': 30.0
        },
        'EnhancedMeanReversionStrategy': {
            'rsi_oversold': 25,
            'rsi_overbought': 75,
            'bb_period': 20,
            'volatility_threshold': 0.02
        },
        'AggressiveMeanReversionStrategy': {
            'rsi_oversold': 20,
            'rsi_overbought': 80,
            'bb_std': 2.5
        },
        'ProfitableMeanReversionStrategy': {
            'rsi_oversold': 25,
            'bb_period': 15,
            'profit_target': 0.02
        },
        'FixedMeanReversionStrategy': {
            'rsi_oversold': 30,
            'bb_period': 20,
            'fixed_stop_loss': 0.015
        }
    }
    
    # 合并参数
    params = base_params.copy()
    if strategy_name in strategy_specific_params:
        params.update(strategy_specific_params[strategy_name])
    
    return params

def run_single_strategy_backtest(strategy_name, strategy_class):
    """运行单个策略的回测"""
    
    logger.info(f"开始测试策略: {strategy_name}")
    
    try:
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config

        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-30'
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 获取策略参数
        strategy_params = get_strategy_default_params(strategy_name)
        
        logger.info(f"{strategy_name}: 开始回测 (2025-04-01 到 2025-04-30)")

        start_time = time.time()

        # 创建回测引擎
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)

        # 执行回测
        results = backtester.run_backtest(config.start_date, config.end_date)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        if results:
            # 计算关键指标
            initial_value = config['initial_cash']
            final_value = results.get('final_portfolio_value', initial_value)
            total_return = (final_value - initial_value) / initial_value * 100
            
            strategy_result = {
                'strategy_name': strategy_name,
                'initial_cash': initial_value,
                'final_value': final_value,
                'total_return_pct': total_return,
                'total_trades': results.get('total_trades', 0),
                'winning_trades': results.get('winning_trades', 0),
                'losing_trades': results.get('losing_trades', 0),
                'win_rate': results.get('win_rate', 0),
                'sharpe_ratio': results.get('sharpe_ratio', 0),
                'max_drawdown': results.get('max_drawdown', 0),
                'execution_time': execution_time,
                'status': 'success',
                'error': None,
                'parameters': strategy_params
            }
            
            logger.info(f"{strategy_name}: 回测完成 - 收益率: {total_return:.2f}%, 交易次数: {strategy_result['total_trades']}")
            
        else:
            strategy_result = {
                'strategy_name': strategy_name,
                'status': 'failed',
                'error': 'No results returned',
                'execution_time': execution_time,
                'parameters': strategy_params
            }
            logger.warning(f"{strategy_name}: 回测失败 - 未返回结果")
        
        return strategy_result
        
    except Exception as e:
        logger.error(f"{strategy_name}: 回测出错 - {e}")
        return {
            'strategy_name': strategy_name,
            'status': 'error',
            'error': str(e),
            'execution_time': 0,
            'parameters': strategy_params
        }

def run_comprehensive_strategy_comparison():
    """运行全面的策略对比测试"""
    
    print("🚀 开始策略全面对比测试")
    print("=" * 80)
    print("测试数据: 2025年4月BTCUSDT (2025-04-01 到 2025-04-30)")
    print("初始资金: 100,000 USDT")
    print("交易成本: 0.05%")
    print("=" * 80)
    
    # 获取所有策略
    strategies = get_all_strategies()
    
    if not strategies:
        print("❌ 未找到可用策略")
        return
    
    print(f"📊 发现 {len(strategies)} 个可用策略:")
    for i, strategy_name in enumerate(strategies.keys(), 1):
        print(f"  {i:2d}. {strategy_name}")
    
    print("\n⏳ 开始执行回测...")
    
    # 存储所有结果
    all_results = []
    successful_results = []
    
    # 逐个测试策略
    for i, (strategy_name, strategy_class) in enumerate(strategies.items(), 1):
        print(f"\n[{i}/{len(strategies)}] 测试策略: {strategy_name}")
        print("-" * 60)
        
        result = run_single_strategy_backtest(strategy_name, strategy_class)
        all_results.append(result)
        
        if result['status'] == 'success':
            successful_results.append(result)
            print(f"✅ 成功 - 收益率: {result['total_return_pct']:.2f}%")
        else:
            print(f"❌ 失败 - {result.get('error', '未知错误')}")
    
    # 生成对比报告
    generate_comparison_report(all_results, successful_results)
    
    return all_results

def generate_comparison_report(all_results, successful_results):
    """生成策略对比报告"""
    
    print("\n" + "=" * 80)
    print("📊 策略对比测试报告")
    print("=" * 80)
    
    # 基本统计
    total_strategies = len(all_results)
    successful_strategies = len(successful_results)
    failed_strategies = total_strategies - successful_strategies
    
    print(f"总策略数量: {total_strategies}")
    print(f"成功执行: {successful_strategies}")
    print(f"执行失败: {failed_strategies}")
    print(f"成功率: {successful_strategies/total_strategies*100:.1f}%")
    
    if not successful_results:
        print("\n❌ 没有成功的策略结果可供分析")
        return
    
    # 按收益率排序
    successful_results.sort(key=lambda x: x['total_return_pct'], reverse=True)
    
    print(f"\n🏆 策略性能排行榜 (按收益率排序):")
    print("-" * 80)
    print(f"{'排名':<4} {'策略名称':<30} {'收益率':<10} {'交易次数':<8} {'胜率':<8} {'夏普比率':<10}")
    print("-" * 80)
    
    for i, result in enumerate(successful_results, 1):
        print(f"{i:<4} {result['strategy_name']:<30} {result['total_return_pct']:>7.2f}% "
              f"{result['total_trades']:>6} {result['win_rate']:>6.1f}% {result['sharpe_ratio']:>8.3f}")
    
    # 最佳策略详细信息
    if successful_results:
        best_strategy = successful_results[0]
        print(f"\n🥇 最佳策略: {best_strategy['strategy_name']}")
        print("-" * 40)
        print(f"总收益率: {best_strategy['total_return_pct']:.2f}%")
        print(f"最终价值: {best_strategy['final_value']:,.2f} USDT")
        print(f"总交易次数: {best_strategy['total_trades']}")
        print(f"胜率: {best_strategy['win_rate']:.1f}%")
        print(f"夏普比率: {best_strategy['sharpe_ratio']:.3f}")
        print(f"最大回撤: {best_strategy['max_drawdown']:.2f}%")
    
    # 保存详细结果到文件
    save_results_to_file(all_results, successful_results)

def save_results_to_file(all_results, successful_results):
    """保存结果到文件"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    results_file = f"策略对比结果_2024年4月_{timestamp}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
    
    # 保存CSV格式的汇总
    if successful_results:
        df = pd.DataFrame(successful_results)
        csv_file = f"策略对比汇总_2024年4月_{timestamp}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"\n💾 结果已保存:")
        print(f"详细结果: {results_file}")
        print(f"CSV汇总: {csv_file}")

if __name__ == "__main__":
    try:
        results = run_comprehensive_strategy_comparison()
        print("\n🎉 策略对比测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        print(f"\n❌ 测试失败: {e}")
