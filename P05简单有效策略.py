# -*- coding: utf-8 -*-
"""
P05简单有效策略
回归基础，使用最简单但有效的交易逻辑
专注于捕捉明确的趋势机会，确保有交易信号产生
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class SimpleEffectiveStrategy:
    """P05简单有效策略 - 回归基础的简单交易逻辑"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 简化的策略参数
        self.strategy_params = {
            # 基础技术指标
            'ma_period': 20,               # 移动平均周期
            'rsi_period': 14,              # RSI周期
            'price_change_threshold': 0.003, # 价格变化阈值0.3%
            
            # 简单的入场条件
            'rsi_buy_threshold': 45,       # RSI买入阈值
            'rsi_sell_threshold': 55,      # RSI卖出阈值
            'ma_trend_confirm': True,      # 均线趋势确认
            
            # 仓位管理
            'position_size': 0.3,          # 固定仓位30%
            'max_positions': 1,            # 最大持仓数
            
            # 风险控制
            'stop_loss': 0.03,             # 止损3%
            'take_profit': 0.06,           # 止盈6%
            'max_holding_minutes': 480,    # 最大持仓8小时
            
            # 交易频率
            'min_trade_interval': 30,      # 最小交易间隔30分钟
            'force_trade': True,           # 强制产生交易
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_simple_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算简单技术指标"""
        try:
            df = data.copy()
            
            # 移动平均线
            df['MA'] = df['CLOSE'].rolling(self.strategy_params['ma_period']).mean()
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 价格变化
            df['Price_Change'] = df['CLOSE'].pct_change()
            df['Price_Change_5'] = df['CLOSE'].pct_change(5)
            
            # 趋势方向
            df['Trend'] = np.where(df['CLOSE'] > df['MA'], 1, -1)
            
            return df
            
        except Exception as e:
            logger.warning(f"计算简单指标失败: {e}")
            return data
    
    def simulate_simple_strategy(self, data: pd.DataFrame) -> dict:
        """模拟简单有效策略"""
        try:
            print("🔄 模拟P05简单有效策略...")
            
            # 计算技术指标
            data = self.calculate_simple_indicators(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            # 交易控制变量
            last_trade_time = None
            entry_time = None
            entry_price = 0
            stop_loss_price = 0
            take_profit_price = 0
            
            # 强制交易计数器
            no_trade_count = 0
            force_trade_threshold = 1000  # 1000个数据点后强制交易
            
            for i in range(30, len(data)):  # 从第30个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']
                
                # 更新权益曲线
                current_equity = cash + position * current_price
                equity_curve.append(current_equity)
                
                # 检查数据有效性
                if pd.isna(current_price):
                    continue
                
                # 检查交易间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_trade_interval']:
                        continue
                
                # 无持仓时检查入场
                if position == 0:
                    no_trade_count += 1
                    
                    # 获取指标
                    rsi = current_data.get('RSI', 50)
                    ma = current_data.get('MA', current_price)
                    price_change = current_data.get('Price_Change', 0)
                    price_change_5 = current_data.get('Price_Change_5', 0)
                    trend = current_data.get('Trend', 0)
                    
                    # 检查是否有有效指标
                    if pd.isna(rsi) or pd.isna(ma):
                        continue
                    
                    # 简单买入条件
                    buy_signal = False
                    
                    # 条件1: RSI在合理范围 + 价格上涨
                    if (rsi > self.strategy_params['rsi_buy_threshold'] and 
                        rsi < 70 and 
                        price_change > 0):
                        buy_signal = True
                    
                    # 条件2: 价格突破均线 + 正动量
                    if (current_price > ma and 
                        price_change_5 > self.strategy_params['price_change_threshold']):
                        buy_signal = True
                    
                    # 条件3: 强制交易 (确保有交易产生)
                    if (self.strategy_params['force_trade'] and 
                        no_trade_count > force_trade_threshold and
                        rsi > 30 and rsi < 80 and
                        abs(price_change) > 0.001):
                        buy_signal = True
                        print(f"🔥 强制交易触发: RSI={rsi:.1f}, 价格变化={price_change*100:.3f}%")
                    
                    if buy_signal:
                        # 计算股数
                        position_value = cash * self.strategy_params['position_size']
                        shares = position_value / current_price
                        
                        if shares > 0:
                            position = shares
                            cash -= shares * current_price * 1.0005  # 手续费
                            entry_time = current_time
                            entry_price = current_price
                            
                            # 设置止损止盈
                            stop_loss_price = current_price * (1 - self.strategy_params['stop_loss'])
                            take_profit_price = current_price * (1 + self.strategy_params['take_profit'])
                            
                            trades.append({
                                'time': current_time,
                                'action': 'buy',
                                'price': current_price,
                                'shares': shares,
                                'rsi': rsi,
                                'trend': trend,
                                'price_change': price_change,
                                'stop_loss': stop_loss_price,
                                'take_profit': take_profit_price
                            })
                            
                            last_trade_time = current_time
                            no_trade_count = 0  # 重置计数器
                
                # 有持仓时检查出场
                elif position > 0:
                    # 获取指标
                    rsi = current_data.get('RSI', 50)
                    price_change = current_data.get('Price_Change', 0)
                    
                    # 计算持仓时间
                    holding_minutes = (current_time - entry_time).total_seconds() / 60
                    
                    # 出场条件
                    should_exit = False
                    exit_reason = ""
                    
                    # 止损
                    if current_price <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                    # 止盈
                    elif current_price >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                    # RSI过高
                    elif rsi > self.strategy_params['rsi_sell_threshold'] + 15:  # RSI > 70
                        should_exit = True
                        exit_reason = "RSI过高"
                    # 价格大幅下跌
                    elif price_change < -0.01:  # 1%下跌
                        should_exit = True
                        exit_reason = "价格下跌"
                    # 超时
                    elif holding_minutes > self.strategy_params['max_holding_minutes']:
                        should_exit = True
                        exit_reason = "超时平仓"
                    # 小幅盈利后保护
                    elif (current_price > entry_price * 1.02 and  # 盈利2%以上
                          current_price < entry_price * 1.03 and  # 但不到3%
                          rsi > 60):  # RSI较高
                        should_exit = True
                        exit_reason = "盈利保护"
                    
                    if should_exit:
                        cash += position * current_price * 0.9995  # 扣除手续费
                        
                        pnl = (current_price - entry_price) * position
                        pnl_pct = pnl / (entry_price * position) * 100
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': exit_reason,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'holding_minutes': holding_minutes
                        })
                        
                        position = 0
                        entry_time = None
                        last_trade_time = current_time
            
            # 期末平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                pnl = (final_price - entry_price) * position
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': pnl
                })
                position = 0
            
            final_equity = cash
            
            # 计算统计指标
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ P05简单有效策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")
            
            # 打印交易详情
            if total_trades > 0:
                print(f"\n📋 交易详情:")
                for i, trade in enumerate(trades[:10]):  # 显示前10笔交易
                    if trade['action'] == 'buy':
                        print(f"  {i+1}. {trade['time'].strftime('%m-%d %H:%M')} 买入 {trade['price']:.2f} RSI:{trade.get('rsi', 0):.1f}")
                    else:
                        print(f"     {trade['time'].strftime('%m-%d %H:%M')} 卖出 {trade['price']:.2f} {trade.get('reason', '')} PnL:{trade.get('pnl_pct', 0):+.2f}%")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"P05简单有效策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P05简单有效策略测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    strategy = SimpleEffectiveStrategy()
    
    print("\n🎯 P05简单有效策略特点:")
    print("=" * 60)
    print("  1. 回归基础交易逻辑")
    print("  2. 简单RSI + 均线组合")
    print("  3. 强制交易确保信号")
    print("  4. 2:1盈亏比设计")
    print("  5. 多种出场保护")
    print("=" * 60)
    
    # 测试2024年4月
    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_simple_strategy(data)
        
        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1
            
            print(f"\n📊 P05简单有效策略测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"交易次数: {result['total_trades']}")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            
            # 基础目标检查
            print(f"\n🎯 基础目标达成检查:")
            print("-" * 40)
            has_trades = result['total_trades'] > 0
            is_profitable = result['total_return'] > 0
            reasonable_drawdown = result['max_drawdown'] <= 0.25  # 25%回撤限制
            decent_winrate = result['win_rate'] >= 0.4  # 40%胜率
            
            print(f"产生交易: {result['total_trades']}次 {'✅' if has_trades else '❌'}")
            print(f"月度盈利: {result['total_return']*100:+.2f}% {'✅' if is_profitable else '❌'}")
            print(f"回撤控制: {result['max_drawdown']*100:.2f}% {'✅' if reasonable_drawdown else '❌'}")
            print(f"胜率表现: {result['win_rate']*100:.1f}% {'✅' if decent_winrate else '❌'}")
            
            targets_met = sum([has_trades, is_profitable, reasonable_drawdown, decent_winrate])
            
            print(f"\n🏆 P05简单有效策略评价:")
            if targets_met >= 3:
                print("🎉 简单有效! 策略基本可用!")
            elif targets_met == 2:
                print("✅ 有所改进! 策略显示潜力!")
            elif targets_met == 1:
                print("⚠️ 仍需优化! 但有进步!")
            else:
                print("❌ 效果不佳! 需要重新思考!")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
