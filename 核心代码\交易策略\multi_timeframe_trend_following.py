# -*- coding: utf-8 -*-
"""
多时间框架趋势跟踪策略 - MultiTimeframeTrendFollowingStrategy
结合多个时间框架的趋势确认，提高信号质量
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class MultiTimeframeTrendFollowingStrategy:
    """
    多时间框架趋势跟踪策略
    
    核心思想:
    1. 使用更高时间框架确认主趋势方向
    2. 在较低时间框架寻找入场时机
    3. 多重时间框架信号确认
    """
    
    def __init__(self, backtester=None, symbols=None, params=None):
        if params is None:
            params = {}
        
        self.strategy_name = "MultiTimeframeTrendFollowingStrategy"
        self.backtester = backtester
        self.symbols = symbols or ['BTCUSDT']
        
        # 时间框架设置
        self.primary_timeframe = params.get('primary_timeframe', '1H')  # 主时间框架
        self.signal_timeframe = params.get('signal_timeframe', '15T')   # 信号时间框架
        self.trend_timeframe = params.get('trend_timeframe', '4H')      # 趋势时间框架
        
        # 技术指标参数
        self.sma_short = params.get('sma_short', 12)
        self.sma_long = params.get('sma_long', 26)
        self.rsi_period = params.get('rsi_period', 14)
        self.rsi_overbought = params.get('rsi_overbought', 75)
        self.rsi_oversold = params.get('rsi_oversold', 25)
        self.atr_period = params.get('atr_period', 14)
        self.adx_period = params.get('adx_period', 14)
        
        # 多时间框架确认参数
        self.trend_adx_threshold = params.get('trend_adx_threshold', 25)  # 趋势框架ADX阈值
        self.primary_adx_threshold = params.get('primary_adx_threshold', 30)  # 主框架ADX阈值
        self.signal_adx_threshold = params.get('signal_adx_threshold', 35)   # 信号框架ADX阈值
        
        # 趋势一致性要求
        self.require_trend_alignment = params.get('require_trend_alignment', True)
        self.min_trend_strength = params.get('min_trend_strength', 0.6)  # 最小趋势强度
        
        # 风险管理参数
        self.risk_per_trade_pct = params.get('risk_per_trade_pct', 0.01)
        self.atr_sl_multiple = params.get('atr_sl_multiple', 2.5)
        self.atr_tp_multiple = params.get('atr_tp_multiple', 5.0)
        
        # 状态跟踪
        self.last_signal_time = {}
        self.timeframe_data = {}
        
        logger.info(f"{self.strategy_name}: 多时间框架策略初始化完成")
        logger.info(f"时间框架: 趋势={self.trend_timeframe}, 主要={self.primary_timeframe}, 信号={self.signal_timeframe}")
    
    def resample_to_timeframe(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """将数据重采样到指定时间框架"""
        try:
            # 定义重采样规则
            agg_rules = {
                'OPEN': 'first',
                'HIGH': 'max',
                'LOW': 'min',
                'CLOSE': 'last',
                'VOLUME': 'sum'
            }
            
            # 重采样
            resampled = data.resample(timeframe).agg(agg_rules).dropna()
            
            # 重新计算技术指标
            resampled = self.calculate_technical_indicators(resampled)
            
            return resampled
            
        except Exception as e:
            logger.warning(f"重采样到{timeframe}失败: {e}")
            return data
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()
            
            # 移动平均线
            df[f'SMA_{self.sma_short}'] = df['CLOSE'].rolling(self.sma_short).mean()
            df[f'SMA_{self.sma_long}'] = df['CLOSE'].rolling(self.sma_long).mean()
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df[f'RSI_{self.rsi_period}'] = 100 - (100 / (1 + rs))
            
            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df[f'ATR_{self.atr_period}'] = true_range.rolling(self.atr_period).mean()
            
            # ADX
            df = self.calculate_adx(df)
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def calculate_adx(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算ADX指标"""
        try:
            df = data.copy()
            period = self.adx_period
            
            # 计算+DI和-DI
            df['H-L'] = df['HIGH'] - df['LOW']
            df['H-C1'] = np.abs(df['HIGH'] - df['CLOSE'].shift(1))
            df['L-C1'] = np.abs(df['LOW'] - df['CLOSE'].shift(1))
            df['TR'] = df[['H-L', 'H-C1', 'L-C1']].max(axis=1)
            
            df['DMplus'] = np.where((df['HIGH'] - df['HIGH'].shift(1)) > (df['LOW'].shift(1) - df['LOW']),
                                   np.maximum(df['HIGH'] - df['HIGH'].shift(1), 0), 0)
            df['DMminus'] = np.where((df['LOW'].shift(1) - df['LOW']) > (df['HIGH'] - df['HIGH'].shift(1)),
                                    np.maximum(df['LOW'].shift(1) - df['LOW'], 0), 0)
            
            df['DIplus'] = 100 * (df['DMplus'].rolling(period).sum() / df['TR'].rolling(period).sum())
            df['DIminus'] = 100 * (df['DMminus'].rolling(period).sum() / df['TR'].rolling(period).sum())
            
            df['DX'] = 100 * np.abs(df['DIplus'] - df['DIminus']) / (df['DIplus'] + df['DIminus'])
            df[f'ADX_{period}'] = df['DX'].rolling(period).mean()
            
            # 清理临时列
            temp_cols = ['H-L', 'H-C1', 'L-C1', 'TR', 'DMplus', 'DMminus', 'DIplus', 'DIminus', 'DX']
            df = df.drop(columns=temp_cols, errors='ignore')
            
            return df
            
        except Exception as e:
            logger.warning(f"计算ADX失败: {e}")
            return data
    
    def analyze_timeframe_trend(self, data: pd.DataFrame, timeframe: str) -> Dict:
        """分析特定时间框架的趋势"""
        try:
            if data.empty:
                return {'trend': 'neutral', 'strength': 0, 'confidence': 0}
            
            latest = data.iloc[-1]
            
            # 获取技术指标
            sma_short = latest.get(f'SMA_{self.sma_short}', 0)
            sma_long = latest.get(f'SMA_{self.sma_long}', 0)
            adx = latest.get(f'ADX_{self.adx_period}', 0)
            rsi = latest.get(f'RSI_{self.rsi_period}', 50)
            close = latest.get('CLOSE', 0)
            
            # 趋势方向判断
            if sma_short > sma_long and close > sma_short:
                trend_direction = 'bullish'
            elif sma_short < sma_long and close < sma_short:
                trend_direction = 'bearish'
            else:
                trend_direction = 'neutral'
            
            # 趋势强度 (基于ADX)
            if timeframe == self.trend_timeframe:
                strength_threshold = self.trend_adx_threshold
            elif timeframe == self.primary_timeframe:
                strength_threshold = self.primary_adx_threshold
            else:
                strength_threshold = self.signal_adx_threshold
            
            trend_strength = min(1.0, adx / 50.0)  # 标准化到0-1
            
            # 信心度计算
            confidence = 0
            if adx > strength_threshold:
                confidence += 0.4
            if trend_direction != 'neutral':
                confidence += 0.3
            if (trend_direction == 'bullish' and rsi < self.rsi_overbought) or \
               (trend_direction == 'bearish' and rsi > self.rsi_oversold):
                confidence += 0.3
            
            return {
                'trend': trend_direction,
                'strength': trend_strength,
                'confidence': confidence,
                'adx': adx,
                'rsi': rsi
            }
            
        except Exception as e:
            logger.warning(f"分析{timeframe}趋势失败: {e}")
            return {'trend': 'neutral', 'strength': 0, 'confidence': 0}
    
    def check_multi_timeframe_alignment(self, historical_data: pd.DataFrame) -> Dict:
        """检查多时间框架趋势一致性"""
        try:
            # 重采样到不同时间框架
            trend_data = self.resample_to_timeframe(historical_data, self.trend_timeframe)
            primary_data = self.resample_to_timeframe(historical_data, self.primary_timeframe)
            signal_data = historical_data  # 假设输入数据已经是信号时间框架
            
            # 分析各时间框架趋势
            trend_analysis = self.analyze_timeframe_trend(trend_data, self.trend_timeframe)
            primary_analysis = self.analyze_timeframe_trend(primary_data, self.primary_timeframe)
            signal_analysis = self.analyze_timeframe_trend(signal_data, self.signal_timeframe)
            
            # 计算趋势一致性
            trends = [trend_analysis['trend'], primary_analysis['trend'], signal_analysis['trend']]
            
            # 统计趋势方向
            bullish_count = trends.count('bullish')
            bearish_count = trends.count('bearish')
            neutral_count = trends.count('neutral')
            
            # 确定整体趋势
            if bullish_count >= 2:
                overall_trend = 'bullish'
                alignment_score = bullish_count / 3
            elif bearish_count >= 2:
                overall_trend = 'bearish'
                alignment_score = bearish_count / 3
            else:
                overall_trend = 'neutral'
                alignment_score = 0.33
            
            # 计算综合强度
            avg_strength = (trend_analysis['strength'] + 
                          primary_analysis['strength'] + 
                          signal_analysis['strength']) / 3
            
            # 计算综合信心度
            avg_confidence = (trend_analysis['confidence'] + 
                            primary_analysis['confidence'] + 
                            signal_analysis['confidence']) / 3
            
            return {
                'overall_trend': overall_trend,
                'alignment_score': alignment_score,
                'avg_strength': avg_strength,
                'avg_confidence': avg_confidence,
                'trend_frame': trend_analysis,
                'primary_frame': primary_analysis,
                'signal_frame': signal_analysis,
                'is_aligned': alignment_score >= 0.67  # 至少2/3一致
            }
            
        except Exception as e:
            logger.warning(f"多时间框架分析失败: {e}")
            return {
                'overall_trend': 'neutral',
                'alignment_score': 0,
                'avg_strength': 0,
                'avg_confidence': 0,
                'is_aligned': False
            }
    
    def generate_signals(self, data: pd.DataFrame, current_time: pd.Timestamp, 
                        portfolio_value: float, positions: Dict) -> List[Dict]:
        """生成交易信号"""
        signals = []
        
        try:
            if current_time not in data.index:
                return signals
            
            symbol = 'BTCUSDT'
            current_data = data.loc[current_time]
            
            # 获取历史数据用于多时间框架分析
            historical_data = data.loc[:current_time]
            if len(historical_data) < 100:  # 需要足够的历史数据
                return signals
            
            # 多时间框架分析
            mtf_analysis = self.check_multi_timeframe_alignment(historical_data)
            
            # 检查是否需要趋势一致性
            if self.require_trend_alignment and not mtf_analysis['is_aligned']:
                return signals
            
            # 检查趋势强度
            if mtf_analysis['avg_strength'] < self.min_trend_strength:
                return signals
            
            # 获取当前技术指标
            close_price = current_data.get('CLOSE', 0)
            atr = current_data.get(f'ATR_{self.atr_period}', 0)
            
            if close_price <= 0 or atr <= 0:
                return signals
            
            # 检查当前持仓
            current_position = positions.get(symbol, {})
            has_position = current_position.get('shares', 0) > 1e-9
            
            # 生成信号
            overall_trend = mtf_analysis['overall_trend']
            signal_confidence = mtf_analysis['avg_confidence']
            
            if not has_position and signal_confidence > 0.6:
                if overall_trend == 'bullish':
                    # 买入信号
                    stop_loss = close_price - (atr * self.atr_sl_multiple)
                    take_profit = close_price + (atr * self.atr_tp_multiple)
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'price': close_price,
                        'size': self.risk_per_trade_pct,
                        'timestamp': current_time,
                        'signal_type': 'multi_timeframe_bullish',
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'confidence': signal_confidence,
                        'reason': f'多时间框架看涨信号 (一致性: {mtf_analysis["alignment_score"]:.2f})'
                    })
                
                elif overall_trend == 'bearish':
                    # 卖空信号 (如果支持)
                    stop_loss = close_price + (atr * self.atr_sl_multiple)
                    take_profit = close_price - (atr * self.atr_tp_multiple)
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell_short',
                        'price': close_price,
                        'size': self.risk_per_trade_pct,
                        'timestamp': current_time,
                        'signal_type': 'multi_timeframe_bearish',
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'confidence': signal_confidence,
                        'reason': f'多时间框架看跌信号 (一致性: {mtf_analysis["alignment_score"]:.2f})'
                    })
            
            elif has_position:
                # 检查退出条件
                if (overall_trend == 'neutral' or 
                    signal_confidence < 0.3 or 
                    not mtf_analysis['is_aligned']):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'price': close_price,
                        'size': current_position.get('shares', 0),
                        'timestamp': current_time,
                        'signal_type': 'multi_timeframe_exit',
                        'reason': '多时间框架信号减弱'
                    })
            
        except Exception as e:
            logger.error(f"生成多时间框架信号失败: {e}")
        
        return signals
    
    def on_bar(self, current_bars_all_symbols: pd.DataFrame) -> List[Dict]:
        """回测引擎兼容接口"""
        try:
            if current_bars_all_symbols.empty:
                return []
            
            current_time = current_bars_all_symbols.index[0]
            
            # 获取组合信息
            if hasattr(self.backtester, 'portfolio'):
                if hasattr(self.backtester.portfolio, 'total_value'):
                    portfolio_value = self.backtester.portfolio.total_value
                else:
                    portfolio_value = 100000
                positions = self.backtester.portfolio.positions
            else:
                portfolio_value = 100000
                positions = {}
            
            # 获取历史数据
            if hasattr(self.backtester, 'all_historical_data'):
                try:
                    if isinstance(self.backtester.all_historical_data, dict):
                        historical_data = self.backtester.all_historical_data.get('BTCUSDT', current_bars_all_symbols)
                    else:
                        historical_data = self.backtester.all_historical_data.xs('BTCUSDT', level=0)
                    available_data = historical_data.loc[:current_time]
                except:
                    available_data = current_bars_all_symbols
            else:
                available_data = current_bars_all_symbols
            
            return self.generate_signals(available_data, current_time, portfolio_value, positions)
            
        except Exception as e:
            logger.error(f"多时间框架on_bar失败: {e}")
            return []

# 注册策略
def get_strategy_class():
    return MultiTimeframeTrendFollowingStrategy
