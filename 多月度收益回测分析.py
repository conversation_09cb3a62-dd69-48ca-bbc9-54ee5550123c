# -*- coding: utf-8 -*-
"""
多月度收益回测分析
测试P01阿尔法X2024策略在多个月份的表现，生成详细的月度收益报告
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
from datetime import datetime, timedelta
import logging

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

# 设置seaborn样式
sns.set_style("whitegrid")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class MultiMonthProfitAnalyzer:
    """多月度收益分析器"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 测试期间列表
        self.test_periods = [
            {
                'name': '2024年1月',
                'start': '2024-01-01',
                'end': '2024-01-31',
                'description': '2024年第一个月'
            },
            {
                'name': '2024年2月',
                'start': '2024-02-01',
                'end': '2024-02-29',
                'description': '2024年第二个月'
            },
            {
                'name': '2024年3月',
                'start': '2024-03-01',
                'end': '2024-03-31',
                'description': '2024年第三个月'
            },
            {
                'name': '2024年4月',
                'start': '2024-04-01',
                'end': '2024-04-30',
                'description': '2024年第四个月'
            },
            {
                'name': '2024年5月',
                'start': '2024-05-01',
                'end': '2024-05-31',
                'description': '2024年第五个月'
            },
            {
                'name': '2024年6月',
                'start': '2024-06-01',
                'end': '2024-06-30',
                'description': '2024年第六个月'
            }
        ]
        
    def run_monthly_analysis(self) -> dict:
        """运行多月度分析"""
        try:
            print("🚀 开始多月度收益分析...")
            print("=" * 80)
            
            from P01阿尔法X2024_最终修复版 import FinalAlphaX2024Strategy
            
            monthly_results = {}
            strategy = FinalAlphaX2024Strategy()
            
            for period in self.test_periods:
                try:
                    print(f"\n📅 分析期间: {period['name']}")
                    print(f"   时间范围: {period['start']} 至 {period['end']}")
                    print("-" * 60)
                    
                    # 加载数据
                    data = strategy.load_historical_data(period['start'], period['end'])
                    
                    if data.empty:
                        print(f"⚠️ {period['name']} 数据为空，跳过")
                        continue
                    
                    # 运行策略模拟
                    result = strategy.simulate_final_strategy(data)
                    
                    if not result:
                        print(f"❌ {period['name']} 策略模拟失败")
                        continue
                    
                    # 计算月度指标
                    initial_capital = result['initial_capital']
                    final_equity = result['final_equity']
                    monthly_profit = final_equity - initial_capital
                    monthly_return = (final_equity / initial_capital - 1) * 100
                    
                    # 存储结果
                    monthly_results[period['name']] = {
                        'period': period,
                        'initial_capital': initial_capital,
                        'final_equity': final_equity,
                        'monthly_profit': monthly_profit,
                        'monthly_return': monthly_return,
                        'max_drawdown': result['max_drawdown'] * 100,
                        'sharpe_ratio': result['sharpe_ratio'],
                        'total_trades': result['total_trades'],
                        'win_rate': result['win_rate'] * 100,
                        'profit_loss_ratio': result['profit_loss_ratio'],
                        'trades': result['trades'],
                        'equity_curve': result['equity_curve']
                    }
                    
                    # 打印月度结果
                    print(f"✅ {period['name']} 分析完成:")
                    print(f"   月收益: {monthly_profit:+,.0f}元 ({monthly_return:+.2f}%)")
                    print(f"   最大回撤: {result['max_drawdown']*100:.2f}%")
                    print(f"   交易次数: {result['total_trades']}")
                    print(f"   胜率: {result['win_rate']*100:.1f}%")
                    
                except Exception as e:
                    logger.error(f"分析 {period['name']} 失败: {e}")
                    continue
            
            print(f"\n🎯 多月度分析完成! 成功分析 {len(monthly_results)} 个月份")
            return monthly_results
            
        except Exception as e:
            logger.error(f"多月度分析失败: {e}")
            raise
    
    def create_monthly_profit_chart(self, monthly_results: dict) -> str:
        """创建月度收益图表"""
        try:
            if not monthly_results:
                raise ValueError("没有月度结果数据")
            
            # 准备数据
            months = list(monthly_results.keys())
            profits = [monthly_results[month]['monthly_profit'] for month in months]
            returns = [monthly_results[month]['monthly_return'] for month in months]
            
            # 计算累计收益
            cumulative_profits = np.cumsum(profits)
            
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
            fig.suptitle('P01阿尔法X2024策略 - 多月度收益分析报告', fontsize=20, fontweight='bold')
            
            # 1. 月度收益柱状图
            colors = ['#00C851' if p > 0 else '#FF4444' if p < 0 else '#FFBB33' for p in profits]
            bars1 = ax1.bar(range(len(months)), profits, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
            
            # 添加数值标签
            for i, (bar, profit, return_pct) in enumerate(zip(bars1, profits, returns)):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + (100 if height > 0 else -300),
                        f'{profit:+,.0f}元\n({return_pct:+.2f}%)',
                        ha='center', va='bottom' if height > 0 else 'top', 
                        fontweight='bold', fontsize=11)
            
            ax1.set_title('每月收益金额', fontsize=16, fontweight='bold')
            ax1.set_ylabel('收益金额 (元)', fontsize=12)
            ax1.set_xticks(range(len(months)))
            ax1.set_xticklabels([m.replace('2024年', '') for m in months], rotation=45)
            ax1.axhline(y=0, color='black', linestyle='-', alpha=0.8)
            ax1.grid(True, alpha=0.3)
            ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
            
            # 2. 累计收益曲线
            ax2.plot(range(len(months)), cumulative_profits, marker='o', linewidth=3, 
                    markersize=8, color='#2E86AB', label='累计收益')
            ax2.fill_between(range(len(months)), cumulative_profits, alpha=0.3, color='#2E86AB')
            
            # 标注每个点的数值
            for i, cum_profit in enumerate(cumulative_profits):
                ax2.annotate(f'{cum_profit:+,.0f}元', 
                           xy=(i, cum_profit), 
                           xytext=(0, 15),
                           textcoords='offset points',
                           ha='center', va='bottom',
                           fontweight='bold', fontsize=10,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
            
            ax2.set_title('累计收益曲线', fontsize=16, fontweight='bold')
            ax2.set_ylabel('累计收益 (元)', fontsize=12)
            ax2.set_xticks(range(len(months)))
            ax2.set_xticklabels([m.replace('2024年', '') for m in months], rotation=45)
            ax2.axhline(y=0, color='black', linestyle='--', alpha=0.7)
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
            
            # 3. 月度收益率分布
            profit_months = len([p for p in profits if p > 0])
            loss_months = len([p for p in profits if p < 0])
            break_even_months = len([p for p in profits if p == 0])
            
            sizes = [profit_months, loss_months, break_even_months]
            labels = [f'盈利月份\n{profit_months}个月', f'亏损月份\n{loss_months}个月', f'持平月份\n{break_even_months}个月']
            colors_pie = ['#00C851', '#FF4444', '#FFBB33']
            
            # 只显示非零的部分
            non_zero_data = [(size, label, color) for size, label, color in zip(sizes, labels, colors_pie) if size > 0]
            if non_zero_data:
                sizes_nz, labels_nz, colors_nz = zip(*non_zero_data)
                wedges, texts, autotexts = ax3.pie(sizes_nz, labels=labels_nz, colors=colors_nz, 
                                                  autopct='%1.1f%%', startangle=90,
                                                  textprops={'fontsize': 12, 'fontweight': 'bold'})
            
            ax3.set_title('月度盈亏分布', fontsize=16, fontweight='bold')
            
            # 4. 关键指标表格
            ax4.axis('off')
            
            # 计算关键指标
            total_months = len(monthly_results)
            total_profit = sum(profits)
            avg_monthly_profit = np.mean(profits)
            best_month_idx = np.argmax(profits)
            worst_month_idx = np.argmin(profits)
            
            # 计算年化收益率
            if total_months > 0:
                total_return = total_profit / 100000  # 假设初始资金10万
                annual_return = (1 + total_return) ** (12 / total_months) - 1
            else:
                annual_return = 0
            
            # 计算胜率
            win_rate = profit_months / total_months * 100 if total_months > 0 else 0
            
            # 计算最大回撤
            max_drawdown = max([monthly_results[month]['max_drawdown'] for month in months])
            
            # 计算平均夏普比率
            avg_sharpe = np.mean([monthly_results[month]['sharpe_ratio'] for month in months])
            
            # 创建指标表格
            metrics_data = [
                ['测试月数', f'{total_months}个月'],
                ['累计收益', f'{total_profit:+,.0f}元'],
                ['平均月收益', f'{avg_monthly_profit:+,.0f}元'],
                ['年化收益率', f'{annual_return*100:+.2f}%'],
                ['月度胜率', f'{win_rate:.1f}%'],
                ['最佳月份', f'{months[best_month_idx].replace("2024年", "")} (+{profits[best_month_idx]:,.0f}元)'],
                ['最差月份', f'{months[worst_month_idx].replace("2024年", "")} ({profits[worst_month_idx]:+,.0f}元)'],
                ['最大月回撤', f'{max_drawdown:.2f}%'],
                ['平均夏普比率', f'{avg_sharpe:.2f}'],
                ['总交易次数', f'{sum([monthly_results[month]["total_trades"] for month in months])}次']
            ]
            
            # 绘制表格
            table = ax4.table(cellText=metrics_data,
                            colLabels=['关键指标', '数值'],
                            cellLoc='center',
                            loc='center',
                            colWidths=[0.5, 0.5])
            
            table.auto_set_font_size(False)
            table.set_fontsize(12)
            table.scale(1, 2.5)
            
            # 设置表格样式
            for i in range(len(metrics_data) + 1):
                for j in range(2):
                    cell = table[(i, j)]
                    if i == 0:  # 表头
                        cell.set_facecolor('#4472C4')
                        cell.set_text_props(weight='bold', color='white')
                    else:
                        if j == 0:  # 指标名称列
                            cell.set_facecolor('#F2F2F2')
                            cell.set_text_props(weight='bold')
                        else:  # 数值列
                            cell.set_facecolor('white')
                            # 根据数值类型设置颜色
                            text = metrics_data[i-1][1]
                            if '+' in text and '元' in text:
                                cell.set_text_props(color='#00C851', weight='bold')
                            elif '-' in text and '元' in text:
                                cell.set_text_props(color='#FF4444', weight='bold')
            
            ax4.set_title('策略关键指标汇总', fontsize=16, fontweight='bold')
            
            # 保存图表
            save_path = f"P01阿尔法X2024_多月度收益分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"📊 多月度收益图表已保存: {save_path}")
            
            # 显示图表
            plt.show()
            
            return save_path
            
        except Exception as e:
            logger.error(f"创建月度收益图表失败: {e}")
            raise
    
    def generate_monthly_report(self, monthly_results: dict):
        """生成月度收益报告"""
        try:
            print(f"\n📊 P01阿尔法X2024策略 - 多月度收益报告")
            print("=" * 80)
            print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"分析期间: {len(monthly_results)}个月")
            print()
            
            # 月度收益详情
            print("💰 月度收益详情:")
            print("-" * 80)
            print(f"{'月份':<12} {'收益金额':<15} {'收益率':<12} {'最大回撤':<12} {'交易次数':<10} {'胜率':<10}")
            print("-" * 80)
            
            total_profit = 0
            profit_months = 0
            
            for month, result in monthly_results.items():
                profit = result['monthly_profit']
                return_pct = result['monthly_return']
                drawdown = result['max_drawdown']
                trades = result['total_trades']
                win_rate = result['win_rate']
                
                total_profit += profit
                if profit > 0:
                    profit_months += 1
                
                status_emoji = "💚" if profit > 0 else "❤️" if profit < 0 else "💛"
                month_short = month.replace('2024年', '')
                
                print(f"{status_emoji} {month_short:<10} {profit:>+10,.0f}元 {return_pct:>+8.2f}% "
                      f"{drawdown:>8.2f}% {trades:>6d}次 {win_rate:>6.1f}%")
            
            print("-" * 80)
            print(f"📈 累计收益: {total_profit:+,.0f}元")
            print(f"🏆 盈利月份: {profit_months}/{len(monthly_results)} ({profit_months/len(monthly_results)*100:.1f}%)")
            
            # 客户关心的关键指标
            print(f"\n🎯 客户关键指标:")
            print("-" * 50)
            
            avg_monthly_profit = total_profit / len(monthly_results)
            annual_return = (1 + total_profit/100000) ** (12/len(monthly_results)) - 1
            
            print(f"平均月收益: {avg_monthly_profit:+,.0f}元")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"月度胜率: {profit_months/len(monthly_results)*100:.1f}%")
            
            # 客户目标达成分析
            print(f"\n🏅 客户目标达成分析:")
            print("-" * 50)
            
            annual_target = annual_return >= 0.15
            monthly_profit_target = avg_monthly_profit >= 1250  # 15%年化对应月均1250元
            win_rate_target = profit_months/len(monthly_results) >= 0.6  # 60%月度胜率
            
            print(f"年化收益率目标 (≥15%): {annual_return*100:+.2f}% {'✅' if annual_target else '❌'}")
            print(f"月均收益目标 (≥1250元): {avg_monthly_profit:+,.0f}元 {'✅' if monthly_profit_target else '❌'}")
            print(f"月度胜率目标 (≥60%): {profit_months/len(monthly_results)*100:.1f}% {'✅' if win_rate_target else '❌'}")
            
            targets_met = sum([annual_target, monthly_profit_target, win_rate_target])
            
            print(f"\n🏆 综合评价:")
            if targets_met == 3:
                print("🎉 完全达标! 策略表现优秀，完全满足客户需求!")
            elif targets_met == 2:
                print("✅ 基本达标! 策略表现良好，大部分满足客户需求!")
            elif targets_met == 1:
                print("⚠️ 部分达标! 策略有潜力，但需要进一步优化!")
            else:
                print("❌ 未达标! 策略需要重大改进!")
            
            # 保存报告到文件
            report_filename = f"P01阿尔法X2024_多月度收益报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write("P01阿尔法X2024策略 - 多月度收益报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"分析期间: {len(monthly_results)}个月\n\n")
                
                f.write("月度收益详情:\n")
                f.write("-" * 80 + "\n")
                for month, result in monthly_results.items():
                    profit = result['monthly_profit']
                    return_pct = result['monthly_return']
                    f.write(f"{month}: {profit:+,.0f}元 ({return_pct:+.2f}%)\n")
                
                f.write(f"\n累计收益: {total_profit:+,.0f}元\n")
                f.write(f"盈利月份: {profit_months}/{len(monthly_results)} ({profit_months/len(monthly_results)*100:.1f}%)\n")
                f.write(f"平均月收益: {avg_monthly_profit:+,.0f}元\n")
                f.write(f"年化收益率: {annual_return*100:+.2f}%\n")
            
            print(f"\n📄 详细报告已保存: {report_filename}")
            
        except Exception as e:
            logger.error(f"生成月度报告失败: {e}")

def main():
    """主函数"""
    print("🚀 启动多月度收益回测分析...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    try:
        # 创建分析器
        analyzer = MultiMonthProfitAnalyzer()
        
        # 运行多月度分析
        monthly_results = analyzer.run_monthly_analysis()
        
        if not monthly_results:
            print("❌ 没有获得有效的月度分析结果")
            return
        
        # 生成图表
        chart_path = analyzer.create_monthly_profit_chart(monthly_results)
        
        # 生成报告
        analyzer.generate_monthly_report(monthly_results)
        
        print(f"\n🎉 多月度收益分析完成!")
        print(f"📊 图表文件: {chart_path}")
        
    except Exception as e:
        logger.error(f"多月度分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
