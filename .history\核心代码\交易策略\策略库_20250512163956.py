# -*- coding: utf-8 -*-
# 在 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass, field
import importlib

# 检测backtesting库是否可用
try:
    import backtesting
    backtesting_available = True
except ImportError:
    backtesting_available = False

# --- 保持 BacktestResult 和 validate_input ---
@dataclass
class BacktestResult: # ... (保持不变) ...
    pass
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool: # ... (保持不变) ...
    pass

logger = logging.getLogger(__name__)

# --- 修改后的策略基类 ---
class TradingStrategy:
    """
    交易策略基类 (适配模拟事件驱动回测和backtesting)
    """
    parameters: Dict[str, Any] = field(default_factory=dict)
    # 默认的止损止盈比例，子策略可以在其 on_init 中根据参数覆盖
    default_stop_loss_pct = 0.03
    default_take_profit_pct = 0.08
    # 默认的信号仓位百分比，如果策略不指定，则使用此值
    default_signal_size_pct = 0.05 

    def __init__(self, engine: Any, symbol_list: List[str], params: Optional[Dict] = None):
        """
        初始化策略实例 (主要为事件驱动模式设计)。

        Args:
            engine: 交易引擎实例 (通常是 Portfolio 实例或其适配器)，
                    用于访问组合信息 (如持仓、现金) 和市场数据。
            symbol_list: 此策略实例负责的标的列表。
            params: 策略特定参数。
        """
        # --- 参数处理逻辑 ---
        final_params = {}
        cls = self.__class__
        # 1. 类定义的属性作为基础默认值
        potential_class_params = [p for p in dir(cls) if not p.startswith('_') and not callable(getattr(cls, p)) and isinstance(getattr(cls, p), (int, float, str, bool, list, dict))]
        for param_name in potential_class_params:
            final_params[param_name] = getattr(cls, param_name)
            
        # 2. 构造函数传入的 params 覆盖/添加值
        if params:
            for key, value in params.items():
                final_params[key] = value
                
        self.parameters = final_params
        
        # 将最终参数设为实例属性 (方便访问)
        for key, value in self.parameters.items():
            setattr(self, key, value)

        # --- 事件驱动模式初始化 ---
        if engine is None or symbol_list is None:
            raise ValueError("事件驱动策略必须提供 engine 和 symbol_list。")
            
        self.engine = engine # Portfolio instance
        self.symbol_list = symbol_list
        self.strategy_name = self.__class__.__name__ # 用于信号标记

        logger.info(f"策略 {self.strategy_name} 以事件驱动模式初始化 for symbols: {self.symbol_list}, Params: {self.parameters}")
        
        # 调用子类的初始化逻辑
        self.on_init()

    def on_init(self):
        """子类可以覆盖此方法进行初始化，例如加载历史数据或设置内部状态。"""
        # 确保子类中也正确设置了 self.stop_loss_pct, self.take_profit_pct, self.signal_size_pct
        # 如果子类参数中没有定义，则使用基类的默认值
        self.stop_loss_pct = self.parameters.get('stop_loss_pct', self.default_stop_loss_pct)
        self.take_profit_pct = self.parameters.get('take_profit_pct', self.default_take_profit_pct)
        self.signal_size_pct = self.parameters.get('signal_size_pct', self.default_signal_size_pct)
        pass

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        """
        处理新的分钟线 Bar 数据，并返回交易信号列表。
        这是事件驱动策略的核心逻辑入口。

        Args:
            current_bar_data (pd.DataFrame): 当前时间点所有 relevant 标的的最新 Bar 数据
                                             (MultiIndex: Symbol, Datetime)。
                                             通常只包含最新的一个时间点。需要包含 OHLCV 和预计算的因子列。
        Returns:
            Optional[List[Dict]]: 交易信号字典的列表，每个字典代表一个交易指令。
                                  例如: [{'symbol': 'AAPL', 'action': 'buy', 'price': 150.0, 'size_pct': 0.05, 'strategy': 'MyStrategy'}, ...]
                                  如果没有信号，则返回 None 或空列表。
        """
        raise NotImplementedError("子类必须实现 on_bar 方法并返回信号列表或None。")

    def _create_signal(self, symbol: str, action: str, price: float, 
                       size_pct: Optional[float] = None, size_abs: Optional[float] = None,
                       sl_pct: Optional[float] = None, tp_pct: Optional[float] = None) -> Dict:
        """辅助函数，用于创建标准格式的交易信号字典。"""
        if size_pct is None and size_abs is None:
            size_pct = self.signal_size_pct # 使用策略级别或基类默认的仓位百分比
        
        signal = {
            'symbol': symbol,
            'action': action.lower(),
            'price': price,
            'strategy': self.strategy_name, # 自动添加策略名
            'timestamp': pd.Timestamp.now() # 信号生成时间
        }
        if size_abs is not None:
            signal['size'] = size_abs
        elif size_pct is not None:
            signal['size_pct'] = size_pct
        else:
            # Should not happen if logic above is correct
            raise ValueError("Signal must have size_pct or size_abs")

        # 使用策略级别或基类默认的止损止盈（如果未在调用时指定）
        signal['stop_loss_pct'] = sl_pct if sl_pct is not None else self.stop_loss_pct
        signal['take_profit_pct'] = tp_pct if tp_pct is not None else self.take_profit_pct
        
        return signal

    def get_current_position(self, symbol: str) -> float:
         """获取当前持仓数量 (通过engine访问Portfolio)"""
         if not hasattr(self.engine, 'positions'):
             logger.error("Engine (Portfolio) 没有 'positions' 属性。")
             return 0.0
         return self.engine.positions.get(symbol, {}).get('shares', 0.0)

    def get_available_cash(self) -> float:
        """获取可用资金 (通过engine访问Portfolio)"""
        if not hasattr(self.engine, 'current_cash'):
            logger.error("Engine (Portfolio) 没有 'current_cash' 属性。")
            return 0.0
        return self.engine.current_cash


# --- 具体策略实现 (修改为使用 on_bar 并返回信号) ---

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略 (适配 on_bar)"""
    # 参数定义因子列名
    bbands_prefix = 'Bollinger Bands' # 与 calculate_factors 输出匹配
    rsi_factor = 'RSI'              # 与 calculate_factors 输出匹配
    # 策略逻辑参数
    rsi_ob = 75
    rsi_os = 25
    exit_on_middle = True # 是否在中轨平仓
    min_trade_interval = 60  # 最小交易间隔时间（分钟） #TODO: 这个参数应该由RiskManager或System控制
    min_trade_value_abs = 1000 # 最小名义交易额 (用于计算size_abs时的参考)

    def on_init(self):
        super().on_init() # 调用基类的 on_init 来设置默认的 SL/TP/SizePct
        
        # 从 self.parameters 获取特定于此策略的参数
        self.bbands_prefix = self.parameters.get('bbands_prefix', 'BBands_10_1.5')
        self.rsi_factor = self.parameters.get('rsi_factor', 'RSI_7')
        self.rsi_ob = self.parameters.get('rsi_ob', 68)
        self.rsi_os = self.parameters.get('rsi_os', 32)
        self.exit_on_middle = self.parameters.get('exit_on_middle', True)
        
        # 策略特定的止损止盈可以覆盖基类或参数传入的默认值
        self.stop_loss_pct = self.parameters.get('mean_reversion_sl_pct', self.stop_loss_pct)
        self.take_profit_pct = self.parameters.get('mean_reversion_tp_pct', self.take_profit_pct)
        # 策略特定的信号大小百分比
        self.signal_size_pct = self.parameters.get('mean_reversion_size_pct', self.signal_size_pct)

        # 预加载列名
        self.bb_lower_col = f"{self.bbands_prefix}_LowerBand"
        self.bb_upper_col = f"{self.bbands_prefix}_UpperBand"
        self.bb_middle_col = f"{self.bbands_prefix}_MiddleBand"
        
        self.required_cols = ['CLOSE', self.bb_lower_col, self.bb_upper_col, self.rsi_factor]
        if self.exit_on_middle:
            self.required_cols.append(self.bb_middle_col)
        self.required_cols = sorted(list(set(self.required_cols)))
            
        logger.info(f"{self.strategy_name}: 初始化完成。Params: {self.parameters}")
        logger.info(f"{self.strategy_name}: SL={self.stop_loss_pct:.2%}, TP={self.take_profit_pct:.2%}, SizePct={self.signal_size_pct:.2%}")
        logger.info(f"{self.strategy_name}: 查找必需列: {self.required_cols}")

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        """处理新的分钟线 Bar，并返回交易信号列表。"""
        signals = []
        
        # 检查所需列 (一次性)
        if not hasattr(self, '_checked_cols_present'):
            self._checked_cols_present = all(c in current_bar_data.columns for c in self.required_cols)
            if not self._checked_cols_present:
                logger.error(f"{self.strategy_name} on_bar 缺少必需列: {self.required_cols}. 可用: {current_bar_data.columns.tolist()}")
        
        if not self._checked_cols_present:
            return None # 如果列不全，则不产生信号

        for symbol in self.symbol_list:
            # The current_bar_data passed from MinuteEventBacktester has 'Symbol' as its index.
            # So, we should check if the symbol exists in this index.
            if symbol not in current_bar_data.index:
                # logger.debug(f"[{self.engine.current_dt}] {symbol}: No data in current_bar_data. Index: {current_bar_data.index}")
                continue

            try:
                # If current_bar_data.index is 'Symbol', then .loc[symbol] is appropriate.
                symbol_data_row = current_bar_data.loc[symbol]
                if not isinstance(symbol_data_row, pd.Series) or symbol_data_row.empty: # Ensure it's a Series and not empty
                    logger.warning(f"[{self.engine.current_dt}] {symbol}: Data for symbol is not a Series or is empty after .loc. Type: {type(symbol_data_row)}")
                    continue
                
                price = symbol_data_row['CLOSE'] # Access directly if it's a Series
                rsi = symbol_data_row[self.rsi_factor].iloc[0]
                bb_low = symbol_data_row[self.bb_lower_col].iloc[0]
                bb_up = symbol_data_row[self.bb_upper_col].iloc[0]
                bb_mid = symbol_data_row[self.bb_middle_col].iloc[0] if self.exit_on_middle else None

                if not all(pd.notna(v) for v in [price, bb_low, bb_up, rsi]):
                    continue
            except (IndexError, KeyError) as e:
                logger.warning(f"{self.strategy_name}: 提取 {symbol} 数据时出错: {e}")
                continue

            current_pos = self.get_current_position(symbol)
            # current_engine_time = pd.Timestamp.now(tz='Asia/Shanghai') # 使用实际时间戳
            current_engine_time = current_bar_data.index.get_level_values('datetime')[-1]


            logger.debug(f"[{current_engine_time}] {self.strategy_name} for {symbol}: Px={price:.2f}, BBup={bb_up:.2f}, BBlow={bb_low:.2f}, RSI={rsi:.2f}, Pos={current_pos}")

            # --- 交易逻辑：生成信号字典 ---
            # 做空信号 (价格高于上轨且RSI超买)
            if price > bb_up and rsi > self.rsi_ob:
                if current_pos > 0: # 持有多仓，则平多
                    signals.append(self._create_signal(symbol, 'sell', price, size_abs=abs(current_pos)))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 平多信号, Px={price:.2f}, Size={abs(current_pos)}")
                elif current_pos == 0 and self.engine.allow_short_selling: # 无仓位且允许做空，则开空
                    # 使用默认的 signal_size_pct 开空
                    signals.append(self._create_signal(symbol, 'sell', price, size_pct=self.signal_size_pct))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 开空信号, Px={price:.2f}, SizePct={self.signal_size_pct:.2%}")
            
            # 做多信号 (价格低于下轨且RSI超卖)
            elif price < bb_low and rsi < self.rsi_os:
                if current_pos < 0: # 持有空仓，则平空
                    signals.append(self._create_signal(symbol, 'buy', price, size_abs=abs(current_pos)))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 平空信号, Px={price:.2f}, Size={abs(current_pos)}")
                elif current_pos == 0: # 无仓位，则开多
                    signals.append(self._create_signal(symbol, 'buy', price, size_pct=self.signal_size_pct))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 开多信号, Px={price:.2f}, SizePct={self.signal_size_pct:.2%}")

            # 平仓信号 (回到中轨)
            elif self.exit_on_middle and pd.notna(bb_mid):
                 if current_pos > 0 and price >= bb_mid: # 多头回到中轨平仓
                      signals.append(self._create_signal(symbol, 'sell', price, size_abs=abs(current_pos)))
                      logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 多头中轨平仓, Px={price:.2f}, Size={abs(current_pos)}")
                 elif current_pos < 0 and price <= bb_mid: # 空头回到中轨平仓
                      signals.append(self._create_signal(symbol, 'buy', price, size_abs=abs(current_pos)))
                      logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 空头中轨平仓, Px={price:.2f}, Size={abs(current_pos)}")
        
        return signals if signals else None

# --- 可以添加其他策略，如 ORBStrategy, FactorRotationStrategy 等，实现 on_bar 并返回信号列表 ---
# class ORBStrategy(TradingStrategy): ...
# class FactorRotationStrategy(TradingStrategy): ...

class TrendFollowingStrategy(TradingStrategy):
    """趋势跟踪策略"""
    # 默认参数
    ma_fast = 10
    ma_slow = 30
    atr_window = 14
    atr_multiplier = 2.0
    
    def on_init(self):
        super().on_init()
        # 从参数中获取配置
        self.ma_fast = self.parameters.get('ma_fast', 10)
        self.ma_slow = self.parameters.get('ma_slow', 30) 
        self.atr_window = self.parameters.get('atr_window', 14)
        self.atr_multiplier = self.parameters.get('atr_multiplier', 2.0)
        
        # 设置列名
        self.ma_fast_col = f"MA_{self.ma_fast}"
        self.ma_slow_col = f"MA_{self.ma_slow}"
        self.atr_col = f"ATR_{self.atr_window}"
        
        self.required_cols = ['CLOSE', self.ma_fast_col, self.ma_slow_col, self.atr_col]
        
    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        
        # 检查所需列
        if not hasattr(self, '_checked_cols_present'):
            self._checked_cols_present = all(c in current_bar_data.columns for c in self.required_cols)
            if not self._checked_cols_present:
                logger.error(f"缺少必需列: {self.required_cols}")
                return None
                
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index.get_level_values('Symbol'):
                continue
                
            try:
                symbol_data = current_bar_data.xs(symbol, level='Symbol')
                close = symbol_data['CLOSE'].iloc[0]
                ma_fast = symbol_data[self.ma_fast_col]
                ma_slow = symbol_data[self.ma_slow_col]
                atr = symbol_data[self.atr_col]
                
                current_pos = self.get_current_position(symbol)
            except Exception as e:
                logger.warning(f"处理{symbol}数据出错: {e}")
                continue
                
            # 趋势信号
            if ma_fast > ma_slow and current_pos <= 0:  # 上升趋势
                entry_price = close
                stop_loss_price = entry_price - atr * self.atr_multiplier # Renamed for clarity
                signals.append(self._create_signal(
                    symbol, 'buy', entry_price,
                    sl_pct=(entry_price - stop_loss_price)/entry_price if entry_price > 0 else self.default_stop_loss_pct # Avoid division by zero
                ))
            elif ma_fast < ma_slow and current_pos >= 0:  # 下降趋势
                entry_price = close  
                stop_loss_price = entry_price + atr * self.atr_multiplier # Renamed for clarity
                signals.append(self._create_signal(
                    symbol, 'sell', entry_price,
                    sl_pct=(stop_loss_price - entry_price)/entry_price if entry_price > 0 else self.default_stop_loss_pct # Avoid division by zero
                ))
                
        return signals if signals else None

# 更新 STRATEGIES 字典
STRATEGIES = {
    'MeanReversion': MeanReversionStrategy,
    'TrendFollowing': TrendFollowingStrategy
}
