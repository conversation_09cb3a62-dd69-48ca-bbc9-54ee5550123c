# -*- coding: utf-8 -*-
"""
AI优化版AlphaX策略 - 使用Random Forest模型预测
结合传统技术指标和机器学习预测，提高信号质量
"""

import pandas as pd
import numpy as np
import joblib
import json
import os
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AIOptimizedAlphaXStrategy:
    """AI优化版AlphaX策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'AIOptimizedAlphaXStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # AI模型参数
        self.model_path_prefix = all_params.get('model_path_prefix', 'BTCUSDT_random_forest')
        self.model_confidence_threshold = all_params.get('model_confidence_threshold', 0.6)
        
        # 传统技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.adx_threshold = all_params.get('adx_threshold', 20)
        self.rsi_oversold = all_params.get('rsi_oversold', 30)
        self.rsi_overbought = all_params.get('rsi_overbought', 70)
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.015)
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 4.0)
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 120)
        
        # 新增参数
        self.enable_short = all_params.get('enable_short', False)  # 暂时禁用做空
        self.max_daily_trades = all_params.get('max_daily_trades', 3)
        
        # AI模型相关
        self._model = None
        self._scaler = None
        self._feature_list = None
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        # 加载AI模型
        self._load_model_assets()
        
        print(f"策略 AIOptimizedAlphaXStrategy 初始化...")
        print(f"AIOptimizedAlphaXStrategy: AI模型={self.model_path_prefix}, "
              f"置信度阈值={self.model_confidence_threshold}, ADX阈值={self.adx_threshold}")
    
    def _load_model_assets(self):
        """加载AI模型和相关文件"""
        if not self.model_path_prefix:
            logger.warning(f"[{self.strategy_name}] 未提供 'model_path_prefix' 参数，AI功能禁用。")
            return
        
        try:
            model_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', '模型文件')
            model_dir = os.path.normpath(model_dir)
            
            model_path = os.path.join(model_dir, f"{self.model_path_prefix}_model.joblib")
            scaler_path = os.path.join(model_dir, f"{self.model_path_prefix}_scaler.joblib")
            features_path = os.path.join(model_dir, f"{self.model_path_prefix}_features.json")
            
            self._model = joblib.load(model_path)
            self._scaler = joblib.load(scaler_path)
            
            with open(features_path, 'r') as f:
                self._feature_list = json.load(f)
            
            logger.info(f"[{self.strategy_name}] AI模型及相关文件加载成功: {self.model_path_prefix}")
            print(f"AI模型加载成功: {len(self._feature_list)}个特征")
            
        except Exception as e:
            logger.error(f"[{self.strategy_name}] 加载AI模型时出错: {e}")
            print(f"AI模型加载失败: {e}")
            self._model = None
            self._scaler = None
            self._feature_list = None
    
    def _get_ai_prediction(self, data: Dict[str, Any]) -> Tuple[Optional[int], float]:
        """获取AI模型预测"""
        if not all([self._model, self._scaler, self._feature_list]):
            return None, 0.0
        
        try:
            # 准备特征数据
            features_data = {}
            for feature in self._feature_list:
                if feature in data:
                    features_data[feature] = data[feature]
                else:
                    # 如果特征不存在，返回None
                    return None, 0.0
            
            # 转换为DataFrame
            features_df = pd.DataFrame([features_data])
            
            # 检查是否有缺失值
            if features_df.isnull().values.any():
                return None, 0.0
            
            # 标准化特征
            scaled_features = self._scaler.transform(features_df)
            
            # 获取预测概率
            probabilities = self._model.predict_proba(scaled_features)[0]
            
            # 返回预测结果和置信度
            if probabilities[1] >= self.model_confidence_threshold:
                return 1, probabilities[1]  # 买入信号
            else:
                return 0, probabilities[1]  # 无信号
                
        except Exception as e:
            logger.error(f"AI预测错误: {e}")
            return None, 0.0
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 结合AI预测和技术指标"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'ADX_14', 'RSI_14', 'ATR_14']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取AI预测
        ai_prediction, ai_confidence = self._get_ai_prediction(data)
        
        # 如果AI预测失败或置信度不够，不生成信号
        if ai_prediction != 1:
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        adx = data.get('ADX_14')
        rsi = data.get('RSI_14')
        atr = data.get('ATR_14')
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, adx, rsi, atr]):
            return signals
        
        # 结合技术指标的买入条件
        technical_conditions = [
            adx >= self.adx_threshold,  # 趋势强度足够
            rsi <= self.rsi_oversold or rsi >= self.rsi_overbought,  # RSI极值
        ]
        
        # AI预测 + 技术指标确认
        if ai_prediction == 1 and ai_confidence >= self.model_confidence_threshold and any(technical_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'AIOptimizedAlphaXStrategy',
                'signal_type': 'ai_entry',
                'reason': f'AI买入信号: 置信度={ai_confidence:.3f}, RSI={rsi:.2f}, ADX={adx:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] AIOptimizedAlphaXStrategy: AI买入信号 BTCUSDT, "
                  f"置信度={ai_confidence:.3f}, RSI={rsi:.2f}, ADX={adx:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'AIOptimizedAlphaXStrategy',
            'version': '1.0',
            'description': 'AI优化版AlphaX策略，使用Random Forest模型预测',
            'model_info': {
                'model_path_prefix': self.model_path_prefix,
                'model_loaded': self._model is not None,
                'features_count': len(self._feature_list) if self._feature_list else 0,
                'confidence_threshold': self.model_confidence_threshold
            },
            'parameters': {
                'adx_threshold': self.adx_threshold,
                'rsi_oversold': self.rsi_oversold,
                'rsi_overbought': self.rsi_overbought,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes
            },
            'status': {
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
