# -*- coding: utf-8 -*-
"""
检查AI特征数据是否存在
"""
import pandas as pd
import json
import os

def check_ai_features():
    """检查AI模型需要的特征是否在数据中存在"""
    
    print("=== 检查AI特征数据 ===")
    
    # 读取AI模型特征列表
    features_path = os.path.join('模型文件', 'BTCUSDT_random_forest_features.json')
    with open(features_path, 'r') as f:
        required_features = json.load(f)
    
    print(f"AI模型需要的特征 ({len(required_features)}个):")
    for i, feature in enumerate(required_features, 1):
        print(f"  {i}. {feature}")
    
    # 加载一些样本数据检查
    data_path = r"D:\市场数据\现金\BTCUSDT\BTCUSDT-1m-2024-04.csv"
    
    print(f"\n正在检查数据文件: {data_path}")
    
    # 读取前1000行数据
    df = pd.read_csv(data_path, nrows=1000)
    print(f"数据形状: {df.shape}")
    
    print(f"\n数据列名:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i}. {col}")
    
    # 检查哪些特征缺失
    missing_features = []
    available_features = []
    
    for feature in required_features:
        if feature in df.columns:
            available_features.append(feature)
        else:
            missing_features.append(feature)
    
    print(f"\n✅ 可用特征 ({len(available_features)}个):")
    for feature in available_features:
        print(f"  • {feature}")
    
    print(f"\n❌ 缺失特征 ({len(missing_features)}个):")
    for feature in missing_features:
        print(f"  • {feature}")
    
    # 分析缺失特征
    print(f"\n=== 特征分析 ===")
    
    if missing_features:
        print("需要计算的特征:")
        
        for feature in missing_features:
            if 'VWAP' in feature:
                print(f"  • {feature}: 需要计算成交量加权平均价格")
            elif 'pct_change' in feature:
                print(f"  • {feature}: 需要计算价格/成交量变化率")
            elif 'CLOSE_vs_SMA' in feature:
                print(f"  • {feature}: 需要计算价格相对于均线的位置")
            elif 'SMA_cross' in feature:
                print(f"  • {feature}: 需要计算均线交叉信号")
            elif 'hour_of_day' in feature:
                print(f"  • {feature}: 需要从时间戳提取小时")
            elif 'day_of_week' in feature:
                print(f"  • {feature}: 需要从时间戳提取星期")
            else:
                print(f"  • {feature}: 未知特征类型")
    
    # 建议解决方案
    print(f"\n=== 解决方案建议 ===")
    
    if len(missing_features) > len(available_features):
        print("❌ 大部分特征缺失，建议:")
        print("  1. 检查数据预处理流程")
        print("  2. 确认技术指标计算是否正确")
        print("  3. 重新训练模型或使用现有特征")
    elif missing_features:
        print("⚠️  部分特征缺失，建议:")
        print("  1. 在数据预处理中添加缺失特征的计算")
        print("  2. 或者降低AI模型的置信度阈值")
        print("  3. 或者使用传统策略作为备选")
    else:
        print("✅ 所有特征都可用，AI策略应该能正常工作")
        print("  如果仍无信号，检查:")
        print("  1. 置信度阈值是否过高")
        print("  2. 技术指标条件是否过严")
        print("  3. 信号间隔是否过长")
    
    return available_features, missing_features

def suggest_optimizations():
    """建议优化方案"""
    
    print(f"\n=== AI策略优化建议 ===")
    
    optimizations = [
        {
            'issue': '置信度阈值过高',
            'current': '0.6',
            'suggested': '0.5 或更低',
            'reason': '降低阈值增加交易机会'
        },
        {
            'issue': '技术指标条件过严',
            'current': 'ADX>=20 且 RSI极值',
            'suggested': '仅使用ADX>=15',
            'reason': '简化条件，依赖AI预测'
        },
        {
            'issue': '信号间隔过长',
            'current': '120分钟',
            'suggested': '60分钟',
            'reason': '增加交易频率'
        },
        {
            'issue': '特征数据缺失',
            'current': '使用原始数据',
            'suggested': '添加特征工程',
            'reason': '确保AI模型输入完整'
        }
    ]
    
    for i, opt in enumerate(optimizations, 1):
        print(f"{i}. {opt['issue']}:")
        print(f"   当前: {opt['current']}")
        print(f"   建议: {opt['suggested']}")
        print(f"   原因: {opt['reason']}")
        print()

if __name__ == '__main__':
    available, missing = check_ai_features()
    suggest_optimizations()
    
    print("=" * 50)
    if missing:
        print("⚠️  需要先解决特征缺失问题")
        print("💡 建议先使用传统策略，然后逐步完善AI功能")
    else:
        print("✅ 特征数据完整，可以优化AI策略参数")
        print("💡 建议降低置信度阈值并简化技术指标条件")
