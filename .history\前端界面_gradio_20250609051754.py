# -*- coding: utf-8 -*-
import gradio as gr
import pandas as pd
import threading
import sys
import os
import json
import yaml
import logging
from queue import Queue, Empty
from datetime import datetime
import traceback
import time

# ==============================================================================
# --- 后端模块导入与设置 ---
# ==============================================================================
# 将项目根目录添加到系统路径中，以便导入核心模块
# 假设此UI文件位于项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

BACKEND_AVAILABLE = False
try:
    # 直接从根目录导入回测引擎
    from 模拟回测引擎_分钟级 import MinuteEventBacktester, Config
    from 核心代码.交易策略.策略库 import STRATEGIES
    BACKEND_AVAILABLE = True
    print("成功导入后端核心模块。UI将与回测引擎对接。")
except ImportError as e:
    print(f"警告: 无法导入后端模块: {e}。UI将以纯前端模拟模式运行。")
    # 为纯UI展示定义一些模拟对象
    STRATEGIES = {"模拟策略A": None, "模拟策略B": None}
    class Config: pass
    class MinuteEventBacktester: pass


# ==============================================================================
# --- UI与后端通信的日志和状态管理 ---
# ==============================================================================
log_queue = Queue()
backtest_thread = None
backtest_status = "未开始"
latest_report_image_path = None
latest_results_df = pd.DataFrame([{"状态": "等待回测完成..."}])
accumulated_logs = ""  # 累积的日志文本

# 设置一个StreamHandler，将日志实时推送到队列中
class QueueLogHandler(logging.Handler):
    def __init__(self, queue):
        super().__init__()
        self.queue = queue
    def emit(self, record):
        self.queue.put(self.format(record))

# 获取根logger，并添加我们的队列处理器
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
# 移除所有旧的处理器，以完全控制日志流向
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)
# 添加新的流处理器（控制台）和队列处理器（UI）
log_format = logging.Formatter("%(asctime)s [%(levelname)s] [%(name)s]: %(message)s")
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(log_format)
queue_handler = QueueLogHandler(log_queue)
queue_handler.setFormatter(log_format)
root_logger.addHandler(console_handler)
root_logger.addHandler(queue_handler)


# ==============================================================================
# --- 后端任务执行函数 ---
# ==============================================================================
def run_backtest_backend(strategy_name, start_date, end_date, initial_cash_str, symbols_str, cost_str, param_json_str):
    """这个函数将在一个独立的后台线程中运行，负责执行完整的回测。"""
    global backtest_status, latest_report_image_path, latest_results_df

    if not BACKEND_AVAILABLE:
        log_queue.put("错误：后端模块不可用，无法开始回测。")
        backtest_status = "失败"
        return

    try:
        backtest_status = "运行中..."
        log_queue.put(f"--- 后台回测任务已启动 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ---")
        log_queue.put(f"策略: {strategy_name}, 日期: {start_date} to {end_date}, 标的: {symbols_str}")

        # 1. 创建配置对象
        config = Config()
        config.start_date = start_date
        config.end_date = end_date
        config.initial_cash = float(initial_cash_str)
        config.crypto_pairs = [s.strip().upper() for s in symbols_str.split(',')]
        config.benchmark_symbol = config.crypto_pairs[0] if config.crypto_pairs else 'BTCUSDT'
        config.transaction_cost_pct = float(cost_str)
        # 从UI或默认Config获取数据路径
        config.local_minute_data_path = getattr(config, 'local_minute_data_path', r'D:\min_data\data')
        config.min_trade_value_abs = getattr(config, 'min_trade_value_abs', 1000.0)
        config.position_limit = getattr(config, 'position_limit', 0.9)


        # 因子配置（硬编码，确保所有策略都能用）
        config.factor_config = {
            'SMA_20': {'function': 'SMA', 'params': {'window': 20}},
            'SMA_60': {'function': 'SMA', 'params': {'window': 60}},
            'RSI_14': {'function': 'RSI', 'params': {'window': 14}},
            'ATR_14': {'function': 'ATR', 'params': {'window': 14}},
            'ADX_14': {'function': 'ADX', 'params': {'window': 14}},
            'BBands_20_2.0': {'function': 'BBands', 'params': {'window': 20, 'num_std_dev': 2.0}},
        }

        # 2. 解析策略参数
        strategy_class = STRATEGIES.get(strategy_name)
        if not strategy_class:
            raise ValueError(f"找不到策略: {strategy_name}")
        
        strategy_params = json.loads(param_json_str) if param_json_str else {}

        # 3. 实例化并运行回测器
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
        results = backtester.run_backtest(start_date, end_date)

        # 4. 保存结果
        if results:
            backtest_status = "已完成"
            log_queue.put("回测成功完成！")
            
            log_dir = os.path.join(PROJECT_ROOT, '回测结果')
            latest_report_image_path = os.path.join(log_dir, f'backtest_report_{strategy_name}.png')
            
            results_to_display = {}
            for key, value in results.items():
                if isinstance(value, float):
                    if '率' in key or '比' in key or '%' in key: results_to_display[key] = f"{value:.2%}"
                    else: results_to_display[key] = f"{value:,.4f}"
                else: results_to_display[key] = str(value)
            latest_results_df = pd.DataFrame([results_to_display])

        else:
            backtest_status = "完成但无结果"
            log_queue.put("回测完成，但未生成有效结果。")

    except Exception as e:
        backtest_status = "失败"
        error_msg = f"回测后台任务失败: {e}\n{traceback.format_exc()}"
        log_queue.put(f"错误: {error_msg}")

def start_backtest_thread_ui(strategy_name, start_date, end_date, initial_cash, symbols_str, transaction_cost, param_json_str):
    """UI按钮点击后调用的函数，负责启动后台回测线程。"""
    global backtest_thread, backtest_status, latest_results_df, latest_report_image_path, accumulated_logs

    if backtest_thread and backtest_thread.is_alive():
        return "一个回测任务正在运行中，请等待其完成后再开始新的任务。"

    while not log_queue.empty(): log_queue.get()
    accumulated_logs = ""  # 清空累积的日志
    latest_results_df = pd.DataFrame([{"状态": "回测已提交，等待结果..."}])
    latest_report_image_path = None

    backtest_thread = threading.Thread(
        target=run_backtest_backend,
        args=(strategy_name, start_date, end_date, initial_cash, symbols_str, transaction_cost, param_json_str),
        daemon=True
    )
    backtest_thread.start()
    return "回测任务已提交到后台运行..."

def get_log_and_status_ui():
    """Gradio UI定时调用的函数，用于获取最新的日志和状态。"""
    global accumulated_logs
    log_updates = []
    try:
        while True: log_updates.append(log_queue.get_nowait())
    except Empty:
        pass

    if log_updates:
        new_logs = "\n".join(log_updates)
        accumulated_logs += "\n" + new_logs if accumulated_logs else new_logs
        # 限制日志长度，避免界面过于庞大
        if len(accumulated_logs) > 10000:
            accumulated_logs = accumulated_logs[-8000:]  # 保留最后8000个字符

    return backtest_status, accumulated_logs

def get_report_data_ui():
    """Gradio UI调用的函数，用于获取回测报告。"""
    if backtest_status in ["已完成", "完成但无结果"]:
        img_path = latest_report_image_path if latest_report_image_path and os.path.exists(latest_report_image_path) else None
        return img_path, latest_results_df
    return None, latest_results_df

# ==============================================================================
# --- 系统监控功能 ---
# ==============================================================================
monitor_thread = None
monitor_running = False
monitor_queue = Queue()

def run_system_monitor(interval_seconds):
    """运行系统监控的后台线程"""
    global monitor_running

    if not BACKEND_AVAILABLE:
        monitor_queue.put("错误：后端模块不可用，无法启动监控。")
        return

    try:
        # 简化监控，使用模拟数据
        from 配置.系统配置 import Config
        import random

        config = Config()
        monitor_queue.put("系统监控已启动...")

        # 模拟初始状态
        initial_cash = config.initial_capital
        current_cash = initial_cash
        positions_value = 0.0

        while monitor_running:
            try:
                # 模拟组合状态变化
                # 在实际应用中，这里会连接到真实的交易引擎
                daily_pnl = random.uniform(-1000, 1000)  # 模拟日盈亏
                positions_value += random.uniform(-500, 500)  # 模拟持仓价值变化
                positions_value = max(0, positions_value)  # 确保非负

                total_value = current_cash + positions_value

                # 格式化组合信息
                portfolio_data = [
                    ["总资产", f"{total_value:,.2f}"],
                    ["现金", f"{current_cash:,.2f}"],
                    ["持仓市值", f"{positions_value:,.2f}"],
                    ["今日盈亏", f"{daily_pnl:,.2f}"],
                ]

                # 模拟持仓信息
                if positions_value > 0:
                    position_data = [
                        ["BTCUSDT", "0.4032", f"{positions_value:,.2f}", f"{daily_pnl:,.2f}"]
                    ]
                else:
                    position_data = [["无持仓", "", "", ""]]


                monitor_queue.put(("portfolio", portfolio_data))
                monitor_queue.put(("positions", position_data))
                monitor_queue.put(("log", f"[{datetime.now().strftime('%H:%M:%S')}] 监控更新完成"))

                time.sleep(interval_seconds)

            except Exception as e:
                monitor_queue.put(("log", f"监控错误: {e}"))
                time.sleep(interval_seconds)

    except Exception as e:
        monitor_queue.put(("log", f"监控启动失败: {e}"))
        monitor_running = False

def start_monitor_ui(interval):
    """启动系统监控"""
    global monitor_thread, monitor_running

    if monitor_thread and monitor_thread.is_alive():
        return "监控已在运行中"

    monitor_running = True
    monitor_thread = threading.Thread(target=run_system_monitor, args=(interval,), daemon=True)
    monitor_thread.start()
    return "系统监控已启动"

def stop_monitor_ui():
    """停止系统监控"""
    global monitor_running
    monitor_running = False
    return "系统监控已停止"

def get_monitor_data_ui():
    """获取监控数据"""
    portfolio_data = [["等待数据...", ""]]
    position_data = [["等待数据...", "", "", ""]]
    logs = ""

    updates = []
    try:
        while True:
            updates.append(monitor_queue.get_nowait())
    except Empty:
        pass

    for update in updates:
        if isinstance(update, tuple) and len(update) == 2:
            update_type, data = update
            if update_type == "portfolio":
                portfolio_data = data
            elif update_type == "positions":
                position_data = data
            elif update_type == "log":
                logs += data + "\n"
        else:
            logs += str(update) + "\n"

    status = "运行中" if monitor_running else "已停止"
    return status, pd.DataFrame(portfolio_data), pd.DataFrame(position_data), logs

# ==============================================================================
# --- 策略优化功能 ---
# ==============================================================================
optimization_thread = None
optimization_status = "未开始"
optimization_results = pd.DataFrame()

def run_strategy_optimization(strategy_name, start_date, end_date, symbol, param_ranges_json):
    """运行策略参数优化"""
    global optimization_status, optimization_results

    if not BACKEND_AVAILABLE:
        log_queue.put("错误：后端模块不可用，无法进行优化。")
        optimization_status = "失败"
        return

    try:
        optimization_status = "运行中..."
        log_queue.put("开始策略参数优化...")

        import json
        import itertools
        from 模拟回测引擎_分钟级 import MinuteEventBacktester, Config

        # 解析参数范围
        param_ranges = json.loads(param_ranges_json)

        # 生成参数组合
        param_names = list(param_ranges.keys())
        param_values = []

        for param_name, (min_val, max_val, steps) in param_ranges.items():
            if steps <= 1:
                param_values.append([min_val])
            else:
                step_size = (max_val - min_val) / (steps - 1)
                values = [min_val + i * step_size for i in range(steps)]
                param_values.append(values)

        # 生成所有参数组合
        param_combinations = list(itertools.product(*param_values))

        log_queue.put(f"将测试 {len(param_combinations)} 个参数组合")

        results = []

        for i, param_combo in enumerate(param_combinations):
            try:
                # 创建参数字典
                params = dict(zip(param_names, param_combo))

                # 创建配置
                config = Config()
                config.start_date = start_date
                config.end_date = end_date
                config.crypto_pairs = [symbol]
                config.initial_cash = 100000

                # 运行回测
                strategy_class = STRATEGIES[strategy_name]
                backtester = MinuteEventBacktester(config, strategy_class, params)
                backtest_results = backtester.run_backtest(start_date, end_date)

                if backtest_results:
                    results.append({
                        "参数组合": str(params),
                        "年化收益率": f"{backtest_results.get('年化收益率', 0):.2%}",
                        "夏普比率": f"{backtest_results.get('夏普比率', 0):.2f}",
                        "最大回撤": f"{backtest_results.get('最大回撤', 0):.2%}"
                    })

                if i % 5 == 0:  # 每5个组合更新一次进度
                    log_queue.put(f"优化进度: {i+1}/{len(param_combinations)}")

            except Exception as e:
                log_queue.put(f"参数组合 {param_combo} 测试失败: {e}")

        optimization_results = pd.DataFrame(results)
        optimization_status = "已完成"
        log_queue.put(f"参数优化完成，共测试 {len(results)} 个有效组合")

    except Exception as e:
        optimization_status = "失败"
        log_queue.put(f"优化失败: {e}")

def start_optimization_ui(strategy_name, start_date, end_date, symbol, param_ranges):
    """启动策略优化"""
    global optimization_thread

    if optimization_thread and optimization_thread.is_alive():
        return "优化任务正在运行中"

    optimization_thread = threading.Thread(
        target=run_strategy_optimization,
        args=(strategy_name, start_date, end_date, symbol, param_ranges),
        daemon=True
    )
    optimization_thread.start()
    return "参数优化已启动"

def get_optimization_results_ui():
    """获取优化结果"""
    return optimization_status, optimization_results, None  # 暂时不返回图表

# ==============================================================================
# --- Gradio UI 定义 ---
# ==============================================================================
with gr.Blocks(title="量化回测控制台", theme=gr.themes.Soft()) as demo:
    gr.Markdown("# 量化回测与分析控制台 (V3 - 全功能修复版)")

    with gr.Tabs():
        with gr.TabItem("回测控制"):
            gr.Markdown("### 在此配置并启动一个新的回测任务")
            with gr.Row():
                with gr.Column(scale=1):
                    strategy_dd = gr.Dropdown(label="选择策略", choices=list(STRATEGIES.keys()), value="AlphaXInspiredStrategy")
                    start_date_picker = gr.Textbox(label="开始日期", value="2023-01-01")
                    end_date_picker = gr.Textbox(label="结束日期", value="2023-01-31")
                    symbols_tb = gr.Textbox(label="交易标的 (逗号分隔)", value="BTCUSDT")
                with gr.Column(scale=1):
                    initial_cash_num = gr.Number(label="初始资金", value=100000)
                    cost_num = gr.Number(label="交易成本/滑点 (如0.0005表示0.05%)", value=0.0005)
                    param_json_tb = gr.Textbox(label="策略参数 (JSON格式)", lines=4, placeholder='例如: {"rsi_oversold": 30, "atr_sl_multiple": 2.5}')
            
            start_button = gr.Button("开始回测", variant="primary")
            status_tb = gr.Textbox(label="回测状态", interactive=False)
            log_ta = gr.TextArea(label="回测日志", lines=15, interactive=False, autoscroll=True)

        with gr.TabItem("回测报告"):
            gr.Markdown("### 查看最近一次完成的回测结果")
            refresh_report_button = gr.Button("刷新报告", variant="secondary")
            with gr.Row():
                report_image = gr.Image(label="权益曲线图", type="filepath", show_label=False)
            with gr.Row():
                report_metrics_df = gr.DataFrame(label="核心性能指标")
        
        with gr.TabItem("系统监控"):
            gr.Markdown("### 实时交易系统监控")
            with gr.Row():
                with gr.Column(scale=1):
                    monitor_interval = gr.Number(label="监控间隔(秒)", value=30, minimum=1, maximum=300)
                    start_monitor_btn = gr.Button("启动实时监控", variant="primary")
                    stop_monitor_btn = gr.Button("停止监控", variant="secondary")
                with gr.Column(scale=2):
                    monitor_status = gr.Textbox(label="监控状态", interactive=False)

            with gr.Row():
                with gr.Column():
                    portfolio_info = gr.DataFrame(label="组合状态", headers=["指标", "数值"])
                with gr.Column():
                    position_info = gr.DataFrame(label="持仓信息", headers=["标的", "数量", "市值", "盈亏"])

            monitor_logs = gr.TextArea(label="监控日志", lines=10, interactive=False, autoscroll=True)

        with gr.TabItem("策略优化"):
            gr.Markdown("### 策略参数优化")
            with gr.Row():
                with gr.Column(scale=1):
                    opt_strategy = gr.Dropdown(label="选择策略", choices=list(STRATEGIES.keys()), value="AlphaXInspiredStrategy")
                    opt_start_date = gr.Textbox(label="优化开始日期", value="2025-04-01")
                    opt_end_date = gr.Textbox(label="优化结束日期", value="2025-04-29")
                    opt_symbol = gr.Textbox(label="优化标的", value="BTCUSDT")
                with gr.Column(scale=1):
                    opt_param_ranges = gr.TextArea(
                        label="参数范围 (JSON格式)",
                        lines=6,
                        value='{\n  "rsi_oversold": [30, 70, 5],\n  "adx_threshold": [15, 30, 3],\n  "atr_sl_multiple": [1.5, 3.0, 4]\n}',
                        placeholder='格式: {"参数名": [最小值, 最大值, 步数]}'
                    )

            optimize_btn = gr.Button("开始参数优化", variant="primary")
            opt_status = gr.Textbox(label="优化状态", interactive=False)

            with gr.Row():
                opt_results = gr.DataFrame(label="优化结果", headers=["参数组合", "年化收益率", "夏普比率", "最大回撤"])
                opt_chart = gr.Image(label="优化结果图表", type="filepath")


    # --- 事件绑定与定时器 ---
    start_button.click(
        fn=start_backtest_thread_ui,
        inputs=[strategy_dd, start_date_picker, end_date_picker, initial_cash_num, symbols_tb, cost_num, param_json_tb],
        outputs=[status_tb]
    )

    gr.Timer(2).tick(
        fn=get_log_and_status_ui,
        inputs=None,
        outputs=[status_tb, log_ta]
    )

    refresh_report_button.click(
        fn=get_report_data_ui,
        inputs=None,
        outputs=[report_image, report_metrics_df]
    )

    # 系统监控事件绑定
    start_monitor_btn.click(
        fn=start_monitor_ui,
        inputs=[monitor_interval],
        outputs=[monitor_status]
    )

    stop_monitor_btn.click(
        fn=stop_monitor_ui,
        inputs=None,
        outputs=[monitor_status]
    )

    # 监控数据定时更新
    gr.Timer(5).tick(
        fn=get_monitor_data_ui,
        inputs=None,
        outputs=[monitor_status, portfolio_info, position_info, monitor_logs]
    )

    # 策略优化事件绑定
    optimize_btn.click(
        fn=start_optimization_ui,
        inputs=[opt_strategy, opt_start_date, opt_end_date, opt_symbol, opt_param_ranges],
        outputs=[opt_status]
    )

    # 优化结果定时更新
    gr.Timer(3).tick(
        fn=get_optimization_results_ui,
        inputs=None,
        outputs=[opt_status, opt_results, opt_chart]
    )

if __name__ == "__main__":
    print("正在启动Gradio界面...")
    print(f"后端模块可用性: {BACKEND_AVAILABLE}")

    if not BACKEND_AVAILABLE:
        print("\n\n" + "="*80)
        print("警告：后端模块导入失败！UI将以纯前端模式运行，无法执行真实的回测任务。")
        print("请确保您已在项目根目录下运行此脚本，并且所有核心代码文件都存在且无语法错误。")
        print("="*80 + "\n\n")

    print("启动Gradio服务器...")
    try:
        demo.launch(server_port=7862, debug=True, share=False)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()