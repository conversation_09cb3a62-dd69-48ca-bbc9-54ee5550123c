# -*- coding: utf-8 -*-
"""
创建优化版本的AlphaX策略
"""
import sys
import os

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def create_optimized_alphax_strategy():
    """创建优化版本的AlphaX策略"""
    
    strategy_code = '''# -*- coding: utf-8 -*-
"""
优化版AlphaX策略 - 基于原版AlphaXInspiredStrategy的改进版本
主要优化：
1. 更严格的RSI阈值（35→25）
2. 更高的ADX趋势要求（25→30）
3. 更大的ATR止损倍数（2.0→2.5）
4. 更长的信号间隔（120→240分钟）
5. 添加成交量确认
6. 添加连续亏损保护
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta

class OptimizedAlphaXStrategy:
    """优化版AlphaX策略"""
    
    def __init__(self, **kwargs):
        """初始化策略参数"""
        # 基础参数（优化后）
        self.sma_short_key = kwargs.get('sma_short_key', 'SMA_20')
        self.sma_long_key = kwargs.get('sma_long_key', 'SMA_60')
        self.adx_threshold = kwargs.get('adx_threshold', 30)  # 提高趋势要求
        self.rsi_oversold = kwargs.get('rsi_oversold', 25)    # 更严格的超卖条件
        self.risk_per_trade_pct = kwargs.get('risk_per_trade_pct', 0.01)
        self.atr_sl_multiple = kwargs.get('atr_sl_multiple', 2.5)  # 增加止损距离
        self.atr_tp_multiple = kwargs.get('atr_tp_multiple', 5.0)  # 增加止盈距离
        self.min_signal_interval_minutes = kwargs.get('min_signal_interval_minutes', 240)  # 延长信号间隔
        
        # 新增优化参数
        self.volume_threshold = kwargs.get('volume_threshold', 1.2)  # 成交量阈值
        self.max_consecutive_losses = kwargs.get('max_consecutive_losses', 3)  # 最大连续亏损次数
        self.max_daily_trades = kwargs.get('max_daily_trades', 2)  # 每日最大交易次数
        self.price_near_sma_pct = kwargs.get('price_near_sma_pct', 0.005)  # 价格接近均线的阈值
        
        # 状态变量
        self.last_signal_time = None
        self.consecutive_losses = 0
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        print(f"策略 OptimizedAlphaXStrategy 初始化...")
        print(f"OptimizedAlphaXStrategy: 优化参数 - RSI阈值={self.rsi_oversold}, ADX阈值={self.adx_threshold}, "
              f"ATR止损倍数={self.atr_sl_multiple}, 信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        # 如果是新的一天，重置计数
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_consecutive_loss_protection(self) -> bool:
        """检查连续亏损保护"""
        return self.consecutive_losses < self.max_consecutive_losses
    
    def check_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查成交量确认"""
        current_volume = data.get('VOLUME', 0)
        avg_volume = data.get('VOLUME_SMA_20', current_volume)  # 使用20期成交量均线
        
        if avg_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        volume_ratio = current_volume / avg_volume
        return volume_ratio >= self.volume_threshold
    
    def check_price_near_sma(self, data: Dict[str, Any]) -> bool:
        """检查价格是否接近短期均线"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if sma_short <= 0:
            return True  # 如果没有均线数据，跳过检查
        
        price_diff_pct = abs(price - sma_short) / sma_short
        return price_diff_pct <= self.price_near_sma_pct
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.sma_short_key, self.sma_long_key, 'ADX_14', 'RSI_14', 'ATR_14']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查信号间隔
        if not self.check_signal_interval(current_time):
            return signals
        
        # 检查每日交易次数限制
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 检查连续亏损保护
        if not self.check_consecutive_loss_protection():
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key)
        adx = data.get('ADX_14')
        rsi = data.get('RSI_14')
        atr = data.get('ATR_14')
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, sma_short, sma_long, adx, rsi, atr]):
            return signals
        
        # 优化后的买入条件
        buy_conditions = [
            sma_short > sma_long,  # 短期均线在长期均线之上
            adx >= self.adx_threshold,  # 趋势强度足够（提高要求）
            rsi <= self.rsi_oversold,  # RSI超卖（更严格）
            self.check_volume_confirmation(data),  # 成交量确认
            self.check_price_near_sma(data)  # 价格接近短期均线
        ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值，实际应从组合管理器获取
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy_to_open',
                'size': position_size,
                'price': price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'timestamp': current_time,
                'strategy': 'OptimizedAlphaXStrategy',
                'reason': f'优化买入信号: RSI={rsi:.2f}, ADX={adx:.2f}, SMA趋势向上'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] OptimizedAlphaXStrategy: 为 BTCUSDT 生成买入信号, "
                  f"RSI={rsi:.2f}, ADX={adx:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def on_trade_closed(self, trade_result: Dict[str, Any]):
        """交易结束回调，用于更新连续亏损计数"""
        pnl = trade_result.get('pnl', 0)
        
        if pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0  # 盈利后重置连续亏损计数
        
        print(f"OptimizedAlphaXStrategy: 交易结束，PnL={pnl:.2f}, 连续亏损次数={self.consecutive_losses}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'OptimizedAlphaXStrategy',
            'version': '1.0',
            'description': '优化版AlphaX策略，改进了参数设置和风险控制',
            'parameters': {
                'rsi_oversold': self.rsi_oversold,
                'adx_threshold': self.adx_threshold,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'volume_threshold': self.volume_threshold,
                'max_consecutive_losses': self.max_consecutive_losses,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'consecutive_losses': self.consecutive_losses,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
'''
    
    # 保存策略文件
    strategy_file_path = os.path.join(CORE_CODE_PATH, '交易策略', 'optimized_alphax_strategy.py')
    
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.write(strategy_code)
    
    print(f"优化策略已保存到: {strategy_file_path}")
    
    # 更新策略库文件，添加新策略
    update_strategy_library()

def update_strategy_library():
    """更新策略库，添加优化策略"""
    
    strategy_lib_path = os.path.join(CORE_CODE_PATH, '交易策略', '策略库.py')
    
    # 读取现有策略库文件
    with open(strategy_lib_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加导入语句
    import_line = "from .optimized_alphax_strategy import OptimizedAlphaXStrategy"
    
    if import_line not in content:
        # 在文件末尾添加导入和策略映射
        additional_content = f'''

# 优化策略导入
{import_line}

# 更新策略映射字典
STRATEGY_MAP = {{
    'AlphaXInspiredStrategy': AlphaXInspiredStrategy,
    'TrendFollowingStrategy': TrendFollowingStrategy,
    'MeanReversionStrategy': MeanReversionStrategy,
    'OptimizedAlphaXStrategy': OptimizedAlphaXStrategy,  # 新增优化策略
}}
'''
        
        # 检查是否已有STRATEGY_MAP，如果有则替换，否则添加
        if 'STRATEGY_MAP' in content:
            # 替换现有的STRATEGY_MAP
            import re
            pattern = r'STRATEGY_MAP\s*=\s*\{[^}]*\}'
            new_map = '''STRATEGY_MAP = {
    'AlphaXInspiredStrategy': AlphaXInspiredStrategy,
    'TrendFollowingStrategy': TrendFollowingStrategy,
    'MeanReversionStrategy': MeanReversionStrategy,
    'OptimizedAlphaXStrategy': OptimizedAlphaXStrategy,  # 新增优化策略
}'''
            content = re.sub(pattern, new_map, content, flags=re.DOTALL)
            
            # 添加导入语句
            if import_line not in content:
                # 在其他导入语句后添加
                import_pos = content.rfind('from typing import')
                if import_pos != -1:
                    line_end = content.find('\n', import_pos)
                    content = content[:line_end+1] + import_line + '\n' + content[line_end+1:]
        else:
            content += additional_content
        
        # 写回文件
        with open(strategy_lib_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"策略库已更新: {strategy_lib_path}")

if __name__ == '__main__':
    print("创建优化版AlphaX策略")
    print("=" * 50)
    
    create_optimized_alphax_strategy()
    
    print("\n优化策略创建完成！")
    print("\n主要改进：")
    print("1. RSI阈值：35 → 25（更严格的超卖条件）")
    print("2. ADX阈值：25 → 30（更强的趋势要求）")
    print("3. ATR止损倍数：2.0 → 2.5（更大的止损距离）")
    print("4. ATR止盈倍数：4.0 → 5.0（更好的盈亏比）")
    print("5. 信号间隔：120 → 240分钟（减少过度交易）")
    print("6. 新增成交量确认机制")
    print("7. 新增连续亏损保护")
    print("8. 新增每日交易次数限制")
    print("9. 新增价格接近均线过滤")
    
    print("\n测试命令：")
    print("./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2023-01-01 --end_date 2023-01-07 --strategy OptimizedAlphaXStrategy")
