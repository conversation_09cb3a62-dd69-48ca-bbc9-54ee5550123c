# -*- coding: utf-8 -*-
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)

class EventDrivenStrategy:
    """事件驱动策略基类"""

    def __init__(self, params: Optional[Dict] = None):
        self.params = params or {}
        self.engine = None  # 交易引擎引用
        self.initialized = False

    def on_init(self, engine):
        """策略初始化回调"""
        self.engine = engine
        self.initialized = True
        logger.info(f"策略 {self.__class__.__name__} 初始化完成")

    def on_bar(self, bar_data: Dict):
        """K线数据回调"""
        raise NotImplementedError

    def on_tick(self, tick_data: Dict):
        """Tick数据回调"""
        raise NotImplementedError

    def on_order(self, order_data: Dict):
        """订单状态更新回调"""
        pass

    def on_trade(self, trade_data: Dict):
        """成交回报回调"""
        pass

    def on_stop(self):
        """策略停止回调"""
        pass

    def subscribe(self, symbol: str, data_type: str = 'bar'):
        """订阅行情数据"""
        if self.engine:
            self.engine.subscribe(symbol, data_type, self)

    def buy(self, symbol: str, volume: float, price: Optional[float] = None, sl: Optional[float] = None, tp: Optional[float] = None):
        """买入操作"""
        if self.engine:
            return self.engine.buy(symbol, volume, price)

    def sell(self, symbol: str, volume: float, price: Optional[float] = None, sl: Optional[float] = None, tp: Optional[float] = None):
        """卖出操作"""
        if self.engine:
            return self.engine.sell(symbol, volume, price)

    def cancel_order(self, order_id: str):
        """取消订单"""
        if self.engine:
            return self.engine.cancel_order(order_id)
