#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试脚本 - 逐步诊断问题
"""

def test_imports():
    """测试基础导入"""
    print("1. 测试基础导入...")
    
    try:
        from 配置.系统配置 import Config
        print("✅ 配置导入成功")
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        return False
    
    try:
        from 核心代码.市场数据.数据获取器 import MarketData
        print("✅ 市场数据类导入成功")
    except Exception as e:
        print(f"❌ 市场数据类导入失败: {e}")
        return False
    
    try:
        from 核心代码.因子计算.因子库 import calculate_factors, FACTOR_FUNCTIONS
        print(f"✅ 因子库导入成功，可用函数: {list(FACTOR_FUNCTIONS.keys())}")
    except Exception as e:
        print(f"❌ 因子库导入失败: {e}")
        return False
    
    try:
        from 核心代码.交易策略.策略库 import STRATEGIES
        print(f"✅ 策略库导入成功，可用策略: {list(STRATEGIES.keys())}")
    except Exception as e:
        print(f"❌ 策略库导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n2. 测试配置...")
    
    try:
        from 配置.系统配置 import Config
        config = Config()
        
        print(f"✅ 配置初始化成功")
        print(f"   初始资金: {config.initial_capital}")
        print(f"   加密货币对: {config.crypto_pairs}")
        print(f"   策略数量: {len(config.strategies)}")
        print(f"   因子配置数量: {len(config.factor_config)}")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n3. 测试数据加载...")
    
    try:
        from 配置.系统配置 import Config
        from 核心代码.市场数据.数据获取器 import MarketData
        
        config = Config()
        market_data = MarketData(config)
        
        print("✅ 数据管理器初始化成功")
        
        # 测试加载数据
        symbol = "BTCUSDT"
        start_date = "2025-04-01"
        end_date = "2025-04-02"  # 只加载2天数据进行测试
        
        print(f"   尝试加载 {symbol} 数据 ({start_date} 到 {end_date})...")
        
        df = market_data.get_market_data(symbol, start_date, end_date, source='local')
        
        if df is None or df.empty:
            print("❌ 数据加载失败或为空")
            return False
        
        print(f"✅ 数据加载成功: {len(df)} 条记录")
        print(f"   时间范围: {df.index[0]} 到 {df.index[-1]}")
        print(f"   列名: {list(df.columns)}")
        print(f"   数据样本:")
        print(df.head(3))
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_factor_calculation():
    """测试因子计算"""
    print("\n4. 测试因子计算...")
    
    try:
        from 配置.系统配置 import Config
        from 核心代码.市场数据.数据获取器 import MarketData
        from 核心代码.因子计算.因子库 import calculate_factors
        
        config = Config()
        market_data = MarketData(config)
        
        # 加载少量数据
        df = market_data.get_market_data("BTCUSDT", "2025-04-01", "2025-04-02", source='local')
        
        if df is None or df.empty:
            print("❌ 无法获取测试数据")
            return False
        
        print(f"   使用 {len(df)} 条数据进行因子计算...")
        
        # 计算因子
        factors_df = calculate_factors(df, config.factor_config)
        
        if factors_df is None or factors_df.empty:
            print("❌ 因子计算失败")
            return False
        
        print(f"✅ 因子计算成功: {len(factors_df)} 条记录")
        print(f"   因子数量: {len(factors_df.columns)}")
        print(f"   因子列名: {list(factors_df.columns)}")
        
        # 检查关键因子
        key_factors = ['SMA_20', 'RSI_14', 'ATR_14']
        for factor in key_factors:
            if factor in factors_df.columns:
                non_null = factors_df[factor].count()
                print(f"   {factor}: {non_null}/{len(factors_df)} 非空值")
            else:
                print(f"   ⚠️ 缺失因子: {factor}")
        
        return True
        
    except Exception as e:
        print(f"❌ 因子计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_initialization():
    """测试策略初始化"""
    print("\n5. 测试策略初始化...")
    
    try:
        from 配置.系统配置 import Config
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        config = Config()
        
        for strategy_config in config.strategies:
            strategy_name = strategy_config['name']
            strategy_params = strategy_config['params']
            
            print(f"   测试策略: {strategy_name}")
            
            if strategy_name not in STRATEGIES:
                print(f"   ❌ 策略 {strategy_name} 不存在于策略库中")
                continue
            
            try:
                strategy_class = STRATEGIES[strategy_name]
                # 策略需要engine, symbol_list, params三个参数
                # 这里用None作为engine，['BTCUSDT']作为symbol_list进行测试
                strategy = strategy_class(None, ['BTCUSDT'], strategy_params)
                print(f"   ✅ 策略 {strategy_name} 初始化成功")
            except Exception as e:
                print(f"   ❌ 策略 {strategy_name} 初始化失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 策略初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("="*60)
    print("系统诊断测试")
    print("="*60)
    
    tests = [
        ("基础导入", test_imports),
        ("配置", test_config),
        ("数据加载", test_data_loading),
        ("因子计算", test_factor_calculation),
        ("策略初始化", test_strategy_initialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    print(f"\n总体结果: {'🎉 所有测试通过' if all_passed else '⚠️ 存在问题需要修复'}")
