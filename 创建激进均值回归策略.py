# -*- coding: utf-8 -*-
"""
创建激进均值回归策略 - 大幅增加交易频率
"""
import sys
import os

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def create_aggressive_mean_reversion_strategy():
    """创建激进均值回归策略"""
    
    strategy_code = '''# -*- coding: utf-8 -*-
"""
激进均值回归策略 - 大幅增加交易频率，保持合理风险控制
主要特点：
1. 大幅降低入场门槛
2. 使用多种触发条件
3. 更频繁的交易机会
4. 保持良好的风险管理
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AggressiveMeanReversionStrategy:
    """激进均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'AggressiveMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 激进的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.015)  # 降低至1.5%
        
        # 激进的RSI参数
        self.rsi_key = all_params.get('rsi_key', 'RSI_14')
        self.rsi_oversold = all_params.get('rsi_oversold', 40)  # 大幅放宽至40
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.006)  # 降低单笔风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 1.5)  # 更小止损
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 2.5)  # 保持合理盈亏比
        
        # 激进的交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 60)  # 缩短至60分钟
        self.max_daily_trades = all_params.get('max_daily_trades', 8)  # 增加至8次
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        print(f"策略 AggressiveMeanReversionStrategy 初始化...")
        print(f"AggressiveMeanReversionStrategy: 价格偏离={self.price_deviation_pct*100}%, "
              f"RSI≤{self.rsi_oversold}, 盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, "
              f"信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_mean_reversion_opportunity(self, data: Dict[str, Any]) -> bool:
        """检查均值回归机会（多种条件）"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 条件1：价格低于短期均线
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            if deviation >= self.price_deviation_pct:
                return True
        
        # 条件2：价格在两个均线之间且接近下方
        if sma_long > 0 and sma_long < price < sma_short:
            # 价格更接近长期均线
            distance_to_long = abs(price - sma_long)
            distance_to_short = abs(price - sma_short)
            if distance_to_long < distance_to_short:
                return True
        
        # 条件3：价格刚好触及短期均线
        if sma_short > 0:
            price_diff_pct = abs(price - sma_short) / sma_short
            if price_diff_pct <= 0.005:  # 0.5%以内
                return True
        
        return False
    
    def check_rsi_condition(self, data: Dict[str, Any]) -> bool:
        """检查RSI条件（放宽）"""
        rsi = data.get(self.rsi_key, 50)
        
        if pd.isna(rsi):
            return True  # 如果没有RSI数据，跳过检查
        
        # 放宽的RSI条件
        return rsi <= self.rsi_oversold
    
    def check_trend_condition(self, data: Dict[str, Any]) -> bool:
        """检查趋势条件（非常宽松）"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 非常宽松的趋势条件：只要不是极强的下跌趋势
        trend_strength = (sma_long - sma_short) / sma_long
        return trend_strength <= 0.15  # 15%以内的下跌趋势都可以
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 激进均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.sma_short_key]
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key, sma_short)  # 如果没有长期均线，使用短期
        rsi = data.get(self.rsi_key, 50)  # 默认中性RSI
        atr = data.get('ATR_14', price * 0.02)  # 默认2%的ATR
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, sma_short]):
            return signals
        
        # 激进的买入条件
        buy_conditions = [
            self.check_mean_reversion_opportunity(data),  # 均值回归机会
            self.check_rsi_condition(data),  # RSI条件
            self.check_trend_condition(data),  # 趋势条件
        ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100 if sma_short > price else 0
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'AggressiveMeanReversionStrategy',
                'signal_type': 'aggressive_mean_reversion',
                'reason': f'激进均值回归: 偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}vs SMA={sma_short:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] AggressiveMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}vs SMA={sma_short:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'AggressiveMeanReversionStrategy',
            'version': '1.0',
            'description': '激进均值回归策略，大幅增加交易频率',
            'features': [
                '大幅降低入场门槛',
                '多种触发条件',
                '更频繁的交易机会',
                '保持合理风险管理'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'rsi_oversold': self.rsi_oversold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
'''
    
    # 保存策略文件
    strategy_file_path = os.path.join(CORE_CODE_PATH, '交易策略', 'aggressive_mean_reversion_strategy.py')
    
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.write(strategy_code)
    
    print(f"激进均值回归策略已保存到: {strategy_file_path}")
    return strategy_file_path

def update_strategy_library_aggressive():
    """更新策略库，添加激进均值回归策略"""
    
    strategy_lib_path = os.path.join(CORE_CODE_PATH, '交易策略', '策略库.py')
    
    # 读取现有策略库文件
    with open(strategy_lib_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加激进均值回归策略导入
    import_line = "from .aggressive_mean_reversion_strategy import AggressiveMeanReversionStrategy"
    
    if import_line not in content:
        # 在文件末尾添加
        additional_content = f'''

# 激进均值回归策略导入
{import_line}

# 更新策略字典
STRATEGIES['AggressiveMeanReversionStrategy'] = AggressiveMeanReversionStrategy
'''
        content += additional_content
        
        # 写回文件
        with open(strategy_lib_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"策略库已更新: {strategy_lib_path}")

if __name__ == '__main__':
    print("创建激进均值回归策略")
    print("=" * 50)
    
    # 创建激进策略
    create_aggressive_mean_reversion_strategy()
    update_strategy_library_aggressive()
    
    print("\n✅ 激进均值回归策略创建完成！")
    print("\n🚀 激进优化：")
    print("• 价格偏离阈值：2.0% → 1.5%（大幅降低）")
    print("• RSI条件：≤35 → ≤40（大幅放宽）")
    print("• 信号间隔：90 → 60分钟（更高频率）")
    print("• 每日交易：6次 → 8次（更多机会）")
    print("• 多种触发条件：价格偏离、均线位置、价格触及")
    print("• 非常宽松的趋势过滤")
    
    print("\n📊 预期效果：")
    print("• 交易次数：大幅增加至30-50次/月")
    print("• 资金利用率：显著提升")
    print("• 风险控制：降低单笔风险至0.6%")
    print("• 盈亏比：保持1.67:1的合理水平")
    
    print("\n🧪 测试命令：")
    print("./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2024-04-01 --end_date 2024-04-30 --strategy AggressiveMeanReversionStrategy")
