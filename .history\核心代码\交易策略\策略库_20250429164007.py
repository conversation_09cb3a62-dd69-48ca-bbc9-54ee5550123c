# -*- coding: utf-8 -*-
import logging
from typing import Dict, List, Tuple, Optional, Any, Type
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from collections import defaultdict

# 尝试导入 backtesting 用于类型提示和基类
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    BASE_STRATEGY = BacktestStrategy
    backtesting_available = True
except ImportError:
    logging.warning("backtesting库未安装，部分基于该库的策略功能可能受限。")
    class DummyStrategy:
         def __init__(self, broker, data, params): pass
         def init(self): pass
         def next(self): pass
         def I(self, func, *args, **kwargs): return pd.Series(dtype=np.float64)
         @property
         def data(self):
              return pd.DataFrame({'Open':[], 'High':[], 'Low':[], 'Close':[], 'Volume':[]})
         @property
         def position(self):
              class MockPosition:
                   size = 0
                   is_long = False
                   is_short = False
                   pl_pct = 0.0
                   entry_price = 0.0
                   def close(self): return
              return MockPosition()
         def buy(self, **kwargs): pass
         def sell(self, **kwargs): pass
    BASE_STRATEGY = DummyStrategy
    backtesting_available = False
    def crossover(s1, s2): return False

logger = logging.getLogger(__name__)

# --- 辅助函数和数据类 ---
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool:
    if data is None or data.empty:
        logger.error("输入数据为空")
        return False
    if not isinstance(data, pd.DataFrame):
        logger.error(f"输入数据类型错误，应为pd.DataFrame，得到：{type(data)}")
        return False
    data_columns_lower = [str(c).lower() for c in data.columns]
    required_cols_lower = [c.lower() for c in required_cols]
    for col_lower in required_cols_lower:
        if col_lower not in data_columns_lower:
            original_col = next((c for c in required_cols if c.lower() == col_lower), col_lower.capitalize())
            logger.error(f"输入数据缺少必要列: {original_col}")
            return False
    check_cols = [c for c in data.columns if str(c).lower() in required_cols_lower]
    if data[check_cols].isnull().values.any():
        logger.warning("输入数据包含NaN值，可能影响策略计算。建议填充。")
    return True

@dataclass
class BacktestResult:
    sharpe: float = np.nan
    returns: float = np.nan
    drawdown: float = np.nan
    trades: int = 0
    win_rate: float = np.nan
    net_return: float = np.nan
    annual_return: float = np.nan
    volatility: float = np.nan
    calmar_ratio: float = np.nan

# --- 交易策略基类 ---
class TradingStrategy(BASE_STRATEGY):
    window = 20
    threshold = 1.5
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    parameters: Dict[str, Any] = field(default_factory=dict)
    transaction_cost_pct: float = 0.001
    risk_manager = None
    _data = None

    def __init__(self, broker=None, data=None, params: Optional[Dict] = None):
        self.parameters = {}
        cls = self.__class__
        potential_params = ['window', 'threshold', 'stop_loss_pct', 'take_profit_pct']
        for param_name in potential_params:
             if hasattr(cls, param_name):
                 self.parameters[param_name] = getattr(cls, param_name)
        if params:
            self.parameters.update(params)
        for key, value in self.parameters.items():
             if hasattr(self, key):
                 setattr(self, key, value)
        if backtesting_available:
            super().__init__(broker, data, self.parameters)
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        if data is not None and not isinstance(data, list):
             self._data = data

    def set_data(self, data: pd.DataFrame):
        if validate_input(data):
            self._data = data
            if not hasattr(self, 'data') or self.data is None:
                 self.data = data
        else:
            raise ValueError("设置的数据无效")

    def set_transaction_cost(self, cost_pct: float):
        if 0 <= cost_pct < 1:
            self.transaction_cost_pct = cost_pct
            self.parameters['transaction_cost_pct'] = cost_pct
        else:
            logger.error(f"无效的交易成本比例: {cost_pct}")

    def generate_signal(self, current_data: pd.DataFrame) -> str:
        logger.warning(f"{self.__class__.__name__} 未实现 generate_signal 方法，返回 HOLD")
        return 'HOLD'

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if target_data is None or target_data.empty:
            logger.error("无法生成信号DataFrame：无有效数据")
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)
        logger.warning(f"{self.__class__.__name__} 未实现 generate_signals_dataframe 方法，返回全0信号")
        return pd.DataFrame({'Signal': 0}, index=target_data.index)

    def run_simple_backtest(self, data: Optional[pd.DataFrame] = None, initial_capital: float = 100000.0) -> BacktestResult:
        target_data = data if data is not None else self._data
        if not validate_input(target_data):
            logger.error("无法运行简单回测：数据无效")
            return BacktestResult()
        if not isinstance(target_data.index, pd.DatetimeIndex):
            logger.error("简单回测需要 DataFrame 索引为 DatetimeIndex")
            return BacktestResult()
        signals_df = self.generate_signals_dataframe(target_data)
        if 'Signal' not in signals_df.columns:
             logger.error("generate_signals_dataframe 未返回包含 'Signal' 列的 DataFrame")
             return BacktestResult()
        merged_df = target_data.join(signals_df[['Signal']], how='left')
        merged_df['Position'] = merged_df['Signal'].ffill().fillna(0)
        merged_df['Position'] = np.sign(merged_df['Position']).astype(int)
        merged_df['MarketReturn'] = merged_df['Close'].pct_change()
        merged_df['StrategyReturn'] = merged_df['Position'].shift(1) * merged_df['MarketReturn']
        trades = merged_df['Position'].diff().fillna(0) != 0
        trade_cost = trades * self.transaction_cost_pct * 2
        merged_df['StrategyReturn'] -= trade_cost
        merged_df['StrategyReturn'] = merged_df['StrategyReturn'].fillna(0)
        merged_df['EquityCurve'] = initial_capital * (1 + merged_df['StrategyReturn']).cumprod()
        final_equity = merged_df['EquityCurve'].iloc[-1] if not merged_df.empty else initial_capital
        initial_equity = merged_df['EquityCurve'].iloc[0] if not merged_df.empty else initial_capital
        try:
            total_return = (final_equity / initial_equity) - 1 if initial_equity != 0 else 0.0
        except Exception as e:
            logger.error(f"计算总收益率时发生错误: {str(e)}")
            total_return = 0.0
        daily_strategy_returns = merged_df['StrategyReturn'].iloc[1:]
        if daily_strategy_returns.empty or daily_strategy_returns.std() == 0:
             sharpe = 0.0
             volatility = 0.0
             calmar = 0.0
             annual_return = 0.0
        else:
             volatility = daily_strategy_returns.std() * np.sqrt(252)
             sharpe = np.sqrt(252) * daily_strategy_returns.mean() / daily_strategy_returns.std()
             if not merged_df.empty and len(merged_df.index) > 1:
                 time_delta = merged_df.index[-1] - merged_df.index[0]
                 years = max(time_delta.days / 365.25, 1/252)
             else:
                 years = 1.0
             annual_return = (1 + total_return) ** (1 / years) - 1
        rolling_max = merged_df['EquityCurve'].cummax()
        daily_drawdown = (merged_df['EquityCurve'] / rolling_max) - 1.0
        max_drawdown = abs(daily_drawdown.min())
        if max_drawdown == 0:
            calmar = np.inf if annual_return > 0 else 0
        else:
            calmar = annual_return / max_drawdown
        num_trades = trades.sum() // 2
        trade_returns = merged_df.loc[trades, 'StrategyReturn']
        win_rate = (trade_returns > 0).sum() / len(trade_returns) if len(trade_returns) > 0 else 0.0
        logger.info(f"简单回测: Ret={total_return:.2%}, Sharpe={sharpe:.2f}, MaxDD={max_drawdown:.2%}, Trades={num_trades}, WinRate={win_rate:.1%}")
        return BacktestResult(
            sharpe=float(sharpe),
            returns=float(total_return),
            drawdown=float(max_drawdown),
            trades=int(num_trades),
            win_rate=float(win_rate),
            net_return=float(total_return),
            annual_return=float(annual_return),
            volatility=float(volatility),
            calmar_ratio=float(calmar) if calmar != np.inf else None
        )

# --- 具体策略实现 ---
class MeanReversionStrategy(TradingStrategy):
    window = 20
    threshold = 2.0
    rsi_window = 14
    rsi_overbought = 70
    rsi_oversold = 30

    def init(self):
        close = pd.Series(self.data.Close)
        self.mean = self.I(lambda x: x.rolling(self.window).mean(), close)
        self.std = self.I(lambda x: x.rolling(self.window).std(), close)
        def rsi_func(c, n):
            delta = c.diff()
            gain = delta.where(delta > 0, 0).ewm(com=n-1, adjust=False).mean()
            loss = -delta.where(delta < 0, 0).ewm(com=n-1, adjust=False).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
        self.rsi = self.I(rsi_func, close, self.rsi_window)

    def next(self):
        if len(self.mean) < 1 or len(self.std) < 1 or self.std[-1] == 0 or len(self.rsi) < 1:
            return
        price = self.data.Close[-1]
        z_score = (price - self.mean[-1]) / self.std[-1]
        rsi_val = self.rsi[-1]
        sl_buy = price * (1 - self.stop_loss_pct)
        tp_buy = price * (1 + self.take_profit_pct)
        sl_sell = price * (1 + self.stop_loss_pct)
        tp_sell = price * (1 - self.take_profit_pct)
        if z_score > self.threshold and rsi_val > self.rsi_overbought:
            if not self.position:
                self.sell(sl=sl_sell, tp=tp_sell)
        elif z_score < -self.threshold and rsi_val < self.rsi_oversold:
            if not self.position:
                self.buy(sl=sl_buy, tp=tp_buy)
        elif abs(z_score) < 0.5 and self.position:
            self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if not validate_input(target_data, ['Close']):
            raise ValueError("数据无效")
        win = self.parameters.get('window', self.window)
        thresh = self.parameters.get('threshold', self.threshold)
        signals = pd.DataFrame(index=target_data.index)
        mean = target_data['Close'].rolling(win).mean()
        std = target_data['Close'].rolling(win).std().replace(0, np.nan)
        z_score = (target_data['Close'] - mean) / std
        signals['Signal'] = np.where(z_score > thresh, -1, 
                                   np.where(z_score < -thresh, 1, 0))
        signals['Signal'] = signals['Signal'].diff().fillna(0)
        signals['Signal'] = np.sign(signals['Signal']).astype(int)
        return signals

class TrendFollowingStrategy(TradingStrategy):
    short_window = 20
    long_window = 50

    def init(self):
        n1 = self.parameters.get('short_window', 10)
        n2 = self.parameters.get('long_window', 30)
        close = self.data.Close
        
        # 打印初始数据长度和内容
        print(f"Initial data length: {len(close)}")
        print(f"First 5 close prices: {close[:5]}")
        print(f"Last 5 close prices: {close[-5:]}")
        
        # 确保数据长度足够计算移动平均线
        if len(close) >= n2:
            # 计算移动平均线并确保没有NaN值
            self.short_ma = self.I(lambda x: x.rolling(n1, min_periods=1).mean(), close)
            self.long_ma = self.I(lambda x: x.rolling(n2, min_periods=1).mean(), close)
            
            # 打印移动平均线计算后的值
            print(f"Short MA values: {self.short_ma[:5]}")
            print(f"Long MA values: {self.long_ma[:5]}")
            print(f"Last Short MA: {self.short_ma[-1]}, Last Long MA: {self.long_ma[-1]}")
            
            # 确保移动平均线有足够的数据点
            if len(self.short_ma) < n1 or len(self.long_ma) < n2:
                print(f"警告：移动平均线数据不足，短周期需要{n1}个数据点，现有{len(self.short_ma)}")
                print(f"警告：移动平均线数据不足，长周期需要{n2}个数据点，现有{len(self.long_ma)}")
                return
        else:
            # 如果数据不足，使用简单平均
            self.short_ma = self.I(lambda x: x.expanding().mean(), close)
            self.long_ma = self.I(lambda x: x.expanding().mean(), close)
            
            # 打印简单平均计算后的值
            print(f"Simple Short MA values: {self.short_ma[:5]}")
            print(f"Simple Long MA values: {self.long_ma[:5]}")
            print(f"Last Simple Short MA: {self.short_ma[-1]}, Last Simple Long MA: {self.long_ma[-1]}")
            
            # 警告数据不足
            print(f"警告：可用数据点不足，需要至少{n2}个，现有{len(close)}")

    def next(self):
        if len(self.short_ma) < 1 or len(self.long_ma) < 1:
            print("Not enough data for moving averages")
            return
            
        price = self.data.Close[-1]
        sl_pct = self.parameters.get('stop_loss_pct', 0.05)
        tp_pct = self.parameters.get('take_profit_pct', 0.10)
        sl = price * (1 - sl_pct)
        tp = price * (1 + tp_pct)
        
        # 确保移动平均线有足够的数据
        if np.isnan(self.short_ma[-1]) or np.isnan(self.long_ma[-1]):
            print("Moving averages contain NaN values")
            return
            
        # 打印调试信息
        print(f"Price: {price}")
        print(f"Short MA: {self.short_ma[-1]}, Long MA: {self.long_ma[-1]}")
        print(f"Previous Short MA: {self.short_ma[-2]}, Previous Long MA: {self.long_ma[-2]}")
            
        # 检查短期均线是否上穿长期均线
        if len(self.short_ma) >= 2 and len(self.long_ma) >= 2:
            if self.short_ma[-1] > self.long_ma[-1] and self.short_ma[-2] <= self.long_ma[-2]:
                if not self.position: 
                    print(f"Buy signal triggered: Price={price}, Short MA={self.short_ma[-1]}, Long MA={self.long_ma[-1]}")
                    print(f"Previous Short MA={self.short_ma[-2]}, Previous Long MA={self.long_ma[-2]}")
                    self.buy(sl=sl, tp=tp)
                    return
            # 检查短期均线是否下穿长期均线
            elif self.short_ma[-1] < self.long_ma[-1] and self.short_ma[-2] >= self.long_ma[-2]:
                if self.position: 
                    print(f"Sell signal triggered: Price={price}, Short MA={self.short_ma[-1]}, Long MA={self.long_ma[-1]}")
                    print(f"Previous Short MA={self.short_ma[-2]}, Previous Long MA={self.long_ma[-2]}")
                    self.position.close()
                    return
        print(f"No crossover detected: Price={price}, Short MA={self.short_ma[-1]}, Long MA={self.long_ma[-1]}")

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if not validate_input(target_data, ['Close']): raise ValueError("数据无效")
        win_s = self.parameters.get('short_window', 20)
        win_l = self.parameters.get('long_window', 50)
        if len(target_data) < win_l: return pd.DataFrame(index=target_data.index, data={'Signal': 0})
        signals_df = pd.DataFrame(index=target_data.index)
        short_ma = target_data['Close'].rolling(win_s).mean()
        long_ma = target_data['Close'].rolling(win_l).mean()
        signals_df['RawSignal'] = np.where(short_ma > long_ma, 1, np.where(short_ma < long_ma, -1, 0))
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]

class TrendWithVolatilityStrategy(TradingStrategy):
    short_window = 10
    long_window = 50
    vol_window = 20
    vol_threshold = 0.20

    def init(self):
        close = pd.Series(self.data.Close)
        self.short_ma = self.I(lambda x: x.rolling(self.short_window).mean(), close)
        self.long_ma = self.I(lambda x: x.rolling(self.long_window).mean(), close)
        self.volatility = self.I(lambda x: x.pct_change().rolling(self.vol_window).std() * np.sqrt(252), close)

    def next(self):
        if len(self.volatility) < 1 or np.isnan(self.volatility[-1]):
            return
        current_vol = self.volatility[-1]
        price = self.data.Close[-1]
        sl = price * (1 - self.stop_loss_pct)
        tp = price * (1 + self.take_profit_pct)
        if current_vol < self.vol_threshold:
            if self.position:
                self.position.close()
            return
        if crossover(self.short_ma, self.long_ma):
            if not self.position:
                self.buy(sl=sl, tp=tp)
        elif crossover(self.long_ma, self.short_ma):
            if self.position:
                self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if not validate_input(target_data, ['Close']):
            raise ValueError("数据无效")
        win_s = self.parameters.get('short_window', 10)
        win_l = self.parameters.get('long_window', 50)
        win_v = self.parameters.get('vol_window', 20)
        vol_th = self.parameters.get('vol_threshold', 0.20)
        signals = pd.DataFrame(index=target_data.index)
        short = target_data['Close'].rolling(win_s).mean()
        long = target_data['Close'].rolling(win_l).mean()
        vol = target_data['Close'].pct_change().rolling(win_v).std() * np.sqrt(252)
        signals['Trend'] = np.where(short > long, 1, np.where(short < long, -1, 0))
        signals['Signal'] = np.where(vol >= vol_th, signals['Trend'], 0)
        signals['Signal'] = signals['Signal'].diff().fillna(0)
        signals['Signal'] = np.sign(signals['Signal']).astype(int)
        return signals[['Signal']]

# --- 策略注册表 ---
STRATEGIES: Dict[str, Type[TradingStrategy]] = {
    'TrendWithVolatility': TrendWithVolatilityStrategy,
    'MeanReversion': MeanReversionStrategy,
    'TrendFollowing': TrendFollowingStrategy
}
