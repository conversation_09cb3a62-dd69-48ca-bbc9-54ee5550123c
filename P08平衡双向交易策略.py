# -*- coding: utf-8 -*-
"""
P08平衡双向交易策略
在信号质量和交易频率之间找到最佳平衡点
确保既有足够的交易机会，又保持较高的信号质量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class BalancedBidirectionalStrategy:
    """P08平衡双向交易策略 - 质量与频率的最佳平衡"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 平衡优化的策略参数
        self.strategy_params = {
            # 技术指标参数 (适中设置)
            'ema_fast': 12,                # 快速EMA
            'ema_slow': 26,                # 慢速EMA
            'rsi_period': 14,              # RSI周期
            'macd_signal': 9,              # MACD信号线
            
            # 平衡的信号阈值
            'long_rsi_min': 35,            # 做多RSI最小值
            'long_rsi_max': 75,            # 做多RSI最大值
            'long_momentum_min': 0.003,    # 做多最小动量
            'long_volume_min': 1.0,        # 做多最小成交量比
            
            'short_rsi_min': 25,           # 做空RSI最小值
            'short_rsi_max': 65,           # 做空RSI最大值
            'short_momentum_max': -0.003,  # 做空最大动量
            'short_volume_min': 1.0,       # 做空最小成交量比
            
            # 信号质量控制 (放宽)
            'min_signal_strength': 0.8,    # 最小信号强度 (降低)
            'signal_confirmation_bars': 2, # 信号确认K线数 (减少)
            'max_signals_per_day': 8,      # 每日最大信号数
            
            # 仓位管理
            'base_position': 0.2,          # 基础仓位
            'max_position': 0.4,           # 最大仓位
            'risk_per_trade': 0.02,        # 每笔风险
            
            # 风控参数
            'stop_loss_base': 0.025,       # 基础止损2.5%
            'take_profit_base': 0.05,      # 基础止盈5%
            'trailing_stop': True,         # 移动止损
            
            # 交易控制
            'min_signal_gap': 60,          # 最小信号间隔1小时
            'max_holding_hours': 12,       # 最大持仓12小时
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_balanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算平衡的技术指标"""
        try:
            df = data.copy()
            
            # EMA系统
            df['EMA_Fast'] = df['CLOSE'].ewm(span=self.strategy_params['ema_fast']).mean()
            df['EMA_Slow'] = df['CLOSE'].ewm(span=self.strategy_params['ema_slow']).mean()
            
            # MACD
            df['MACD'] = df['EMA_Fast'] - df['EMA_Slow']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['macd_signal']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 动量指标
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            return df
            
        except Exception as e:
            logger.warning(f"计算平衡指标失败: {e}")
            return data
    
    def generate_balanced_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成平衡的交易信号"""
        try:
            # 初始化信号
            df['Long_Signal'] = 0
            df['Short_Signal'] = 0
            df['Signal_Quality'] = 0
            
            # 做多信号 (简化但有效)
            long_conditions = (
                # 基础趋势
                (df['EMA_Fast'] > df['EMA_Slow']) &
                (df['MACD'] > df['MACD_Signal']) &
                
                # RSI条件
                (df['RSI'] >= self.strategy_params['long_rsi_min']) &
                (df['RSI'] <= self.strategy_params['long_rsi_max']) &
                
                # 动量条件
                (df['Momentum_5'] > self.strategy_params['long_momentum_min']) &
                
                # 成交量条件
                (df['Volume_Ratio'] > self.strategy_params['long_volume_min']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.005) & (df['Volatility'] < 0.1)
            )
            
            # 做空信号 (简化但有效)
            short_conditions = (
                # 基础趋势
                (df['EMA_Fast'] < df['EMA_Slow']) &
                (df['MACD'] < df['MACD_Signal']) &
                
                # RSI条件
                (df['RSI'] >= self.strategy_params['short_rsi_min']) &
                (df['RSI'] <= self.strategy_params['short_rsi_max']) &
                
                # 动量条件
                (df['Momentum_5'] < self.strategy_params['short_momentum_max']) &
                
                # 成交量条件
                (df['Volume_Ratio'] > self.strategy_params['short_volume_min']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.005) & (df['Volatility'] < 0.1)
            )
            
            # 简化的信号确认
            confirm_bars = self.strategy_params['signal_confirmation_bars']
            
            # 做多信号确认
            for i in range(confirm_bars, len(df)):
                if long_conditions.iloc[i] and long_conditions.iloc[i-1]:
                    df.iloc[i, df.columns.get_loc('Long_Signal')] = 1
            
            # 做空信号确认
            for i in range(confirm_bars, len(df)):
                if short_conditions.iloc[i] and short_conditions.iloc[i-1]:
                    df.iloc[i, df.columns.get_loc('Short_Signal')] = 1
            
            # 计算信号质量 (简化)
            df['Signal_Quality'] = (
                abs(df['MACD_Histogram']) * 500 * 0.4 +
                abs(df['Momentum_5']) * 100 * 0.3 +
                (df['Volume_Ratio'] - 1) * 0.2 +
                (50 - abs(df['RSI'] - 50)) / 50 * 0.1
            )
            
            return df
            
        except Exception as e:
            logger.error(f"生成平衡信号失败: {e}")
            return df

    def simulate_balanced_strategy(self, data: pd.DataFrame) -> dict:
        """模拟平衡双向交易策略"""
        try:
            print("🔄 模拟P08平衡双向交易策略...")

            # 计算技术指标
            data = self.calculate_balanced_indicators(data)
            data = self.generate_balanced_signals(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            long_position = 0
            short_position = 0
            trades = []
            equity_curve = [initial_capital]

            # 交易控制变量
            last_trade_time = None
            long_entry_time = None
            short_entry_time = None
            long_entry_price = 0
            short_entry_price = 0
            daily_signals = {}

            for i in range(50, len(data)):  # 从第50个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']

                # 计算当前权益
                long_value = long_position * current_price if long_position > 0 else 0
                short_value = short_position * (2 * short_entry_price - current_price) if short_position > 0 else 0
                current_equity = cash + long_value + short_value
                equity_curve.append(current_equity)

                # 检查数据有效性
                if pd.isna(current_price):
                    continue

                # 每日信号限制
                current_date = current_time.date()
                if current_date not in daily_signals:
                    daily_signals[current_date] = 0

                if daily_signals[current_date] >= self.strategy_params['max_signals_per_day']:
                    continue

                # 检查信号间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_signal_gap']:
                        continue

                # 获取信号
                long_signal = current_data.get('Long_Signal', 0)
                short_signal = current_data.get('Short_Signal', 0)
                signal_quality = current_data.get('Signal_Quality', 0)

                # 信号质量过滤
                if signal_quality < self.strategy_params['min_signal_strength']:
                    continue

                # 检查做多信号
                if long_signal == 1:
                    # 平空头仓位
                    if short_position > 0:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': '反向信号平仓'
                        })

                        short_position = 0
                        short_entry_time = None

                    # 开多头仓位
                    if long_position == 0:
                        position_size = self.strategy_params['base_position']

                        # 根据信号质量调整仓位
                        if signal_quality > 1.5:
                            position_size = min(position_size * 1.5, self.strategy_params['max_position'])

                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            long_position = shares
                            cash -= shares * current_price * 1.0005
                            long_entry_time = current_time
                            long_entry_price = current_price

                            trades.append({
                                'time': current_time,
                                'action': 'buy_long',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'stop_loss': current_price * (1 - self.strategy_params['stop_loss_base']),
                                'take_profit': current_price * (1 + self.strategy_params['take_profit_base'])
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 检查做空信号
                elif short_signal == 1:
                    # 平多头仓位
                    if long_position > 0:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': '反向信号平仓'
                        })

                        long_position = 0
                        long_entry_time = None

                    # 开空头仓位
                    if short_position == 0:
                        position_size = self.strategy_params['base_position']

                        # 根据信号质量调整仓位
                        if signal_quality > 1.5:
                            position_size = min(position_size * 1.5, self.strategy_params['max_position'])

                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            short_position = shares
                            cash -= shares * current_price * 1.0005
                            short_entry_time = current_time
                            short_entry_price = current_price

                            trades.append({
                                'time': current_time,
                                'action': 'sell_short',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'stop_loss': current_price * (1 + self.strategy_params['stop_loss_base']),
                                'take_profit': current_price * (1 - self.strategy_params['take_profit_base'])
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 多头出场逻辑
                if long_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'buy_long'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price > long_entry_price * 1.02:
                            new_stop = current_price * 0.985
                            stop_loss = max(stop_loss, new_stop)

                    holding_hours = (current_time - long_entry_time).total_seconds() / 3600

                    should_exit = False
                    exit_reason = ""

                    if current_price <= stop_loss:
                        should_exit = True
                        exit_reason = "多头止损"
                    elif current_price >= take_profit:
                        should_exit = True
                        exit_reason = "多头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "多头超时"

                    if should_exit:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        long_position = 0
                        long_entry_time = None
                        last_trade_time = current_time

                # 空头出场逻辑
                if short_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'sell_short'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price < short_entry_price * 0.98:
                            new_stop = current_price * 1.015
                            stop_loss = min(stop_loss, new_stop)

                    holding_hours = (current_time - short_entry_time).total_seconds() / 3600

                    should_exit = False
                    exit_reason = ""

                    if current_price >= stop_loss:
                        should_exit = True
                        exit_reason = "空头止损"
                    elif current_price <= take_profit:
                        should_exit = True
                        exit_reason = "空头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "空头超时"

                    if should_exit:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        short_position = 0
                        short_entry_time = None
                        last_trade_time = current_time

            # 期末平仓
            final_price = data['CLOSE'].iloc[-1]

            if long_position > 0:
                long_pnl = (final_price - long_entry_price) * long_position
                cash += long_position * final_price * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell_long',
                    'price': final_price,
                    'shares': long_position,
                    'pnl': long_pnl,
                    'reason': '期末平仓'
                })

            if short_position > 0:
                short_pnl = short_position * (short_entry_price - final_price)
                cash += short_position * short_entry_price + short_pnl * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'cover_short',
                    'price': final_price,
                    'shares': short_position,
                    'pnl': short_pnl,
                    'reason': '期末平仓'
                })

            final_equity = cash

            # 计算统计指标
            entry_trades = [t for t in trades if t['action'] in ['buy_long', 'sell_short']]
            exit_trades = [t for t in trades if t.get('pnl') is not None]

            total_trades = len(entry_trades)
            long_trades = len([t for t in trades if t['action'] == 'buy_long'])
            short_trades = len([t for t in trades if t['action'] == 'sell_short'])

            profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in exit_trades if t.get('pnl', 0) < 0])

            win_rate = profitable_trades / (profitable_trades + losing_trades) if (profitable_trades + losing_trades) > 0 else 0

            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in exit_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in exit_trades if t.get('pnl', 0) < 0]

            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital

            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            print(f"✅ P08平衡双向交易策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   总交易次数: {total_trades} (多头:{long_trades}, 空头:{short_trades})")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")

            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'long_trades': long_trades,
                'short_trades': short_trades,
                'winning_trades': profitable_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }

        except Exception as e:
            logger.error(f"P08平衡双向交易策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P08平衡双向交易策略测试...")

    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return

    strategy = BalancedBidirectionalStrategy()

    print("\n🎯 P08平衡双向交易策略特点:")
    print("=" * 60)
    print("  1. ⚖️ 质量与频率的最佳平衡")
    print("  2. 🎯 适中的信号强度阈值")
    print("  3. 📊 简化但有效的确认机制")
    print("  4. 🔄 双向交易能力")
    print("  5. 🛡️ 移动止损保护")
    print("  6. 📈 2:1盈亏比设计")
    print("=" * 60)

    # 测试2024年4月
    try:
        print("\n📊 开始平衡策略测试...")
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_balanced_strategy(data)

        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1

            print(f"\n📊 P08平衡双向交易策略测试结果:")
            print("=" * 60)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"总交易次数: {result['total_trades']}")
            print(f"多头交易: {result['long_trades']}次")
            print(f"空头交易: {result['short_trades']}次")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            print("=" * 60)

            # 策略进化对比
            print(f"\n📈 策略进化历程:")
            print("-" * 50)
            print(f"P05简单策略: -16.24% (435次, 胜率53%, 盈亏比0.73)")
            print(f"P06双向策略: -7.95% (227次, 胜率37%, 盈亏比1.40)")
            print(f"P07优化策略: +0.00% (0次, 无交易)")
            print(f"P08平衡策略: {result['total_return']*100:+.2f}% ({result['total_trades']}次, 胜率{result['win_rate']*100:.1f}%, 盈亏比{result['profit_loss_ratio']:.2f})")

            # 关键改进分析
            print(f"\n🎯 关键改进分析:")
            print("-" * 40)

            # 与P06对比
            improvement_return = result['total_return'] - (-0.0795)
            improvement_trades = 227 - result['total_trades']
            improvement_winrate = result['win_rate'] - 0.37
            improvement_ratio = result['profit_loss_ratio'] - 1.40

            print(f"收益改进: {improvement_return*100:+.2f}个百分点")
            print(f"交易减少: {improvement_trades:+d}次 (质量提升)")
            print(f"胜率变化: {improvement_winrate*100:+.1f}个百分点")
            print(f"盈亏比变化: {improvement_ratio:+.2f}")

            # 客户目标达成检查
            print(f"\n🎯 客户目标达成检查:")
            print("-" * 50)
            is_profitable = result['total_return'] > 0
            decent_annual = annual_return >= 0.10
            good_annual = annual_return >= 0.15
            decent_winrate = result['win_rate'] >= 0.45
            good_winrate = result['win_rate'] >= 0.60
            decent_sharpe = result['sharpe_ratio'] >= 1.0
            good_sharpe = result['sharpe_ratio'] >= 2.0
            good_drawdown = result['max_drawdown'] <= 0.15
            decent_drawdown = result['max_drawdown'] <= 0.20
            good_ratio = result['profit_loss_ratio'] >= 1.5
            has_trades = result['total_trades'] > 0

            print(f"产生交易: {result['total_trades']}次 {'✅' if has_trades else '❌'}")
            print(f"月度盈利: {result['total_return']*100:+.2f}% {'🎉' if is_profitable else '❌'}")
            print(f"年化收益: {annual_return*100:+.2f}% {'🎉' if good_annual else '✅' if decent_annual else '❌'} (目标≥15%)")
            print(f"胜率: {result['win_rate']*100:.1f}% {'🎉' if good_winrate else '✅' if decent_winrate else '❌'} (目标≥60%)")
            print(f"夏普比率: {result['sharpe_ratio']:.2f} {'🎉' if good_sharpe else '✅' if decent_sharpe else '❌'} (目标≥2.0)")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}% {'🎉' if good_drawdown else '✅' if decent_drawdown else '❌'} (目标≤15%)")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f} {'🎉' if good_ratio else '❌'} (目标≥1.5)")

            # 综合评价
            excellent_count = sum([is_profitable, good_annual, good_winrate, good_sharpe, good_drawdown, good_ratio])
            good_count = sum([has_trades, is_profitable, decent_annual, decent_winrate, decent_sharpe, decent_drawdown])

            print(f"\n🏆 P08平衡双向交易策略综合评价:")
            if excellent_count >= 4:
                print("🎉🎉🎉 卓越表现! 策略接近完美!")
            elif excellent_count >= 2:
                print("🎉🎉 优秀表现! 策略显著改进!")
            elif good_count >= 4:
                print("🎉 良好表现! 策略基本可用!")
            elif good_count >= 2:
                print("✅ 有所改进! 策略朝正确方向发展!")
            else:
                print("⚠️ 仍需优化! 但已建立良好基础!")

            # 最终建议
            print(f"\n💡 最终优化建议:")
            if result['total_trades'] == 0:
                print("  🔧 降低信号阈值，确保有交易机会")
            elif result['win_rate'] < 0.5:
                print("  🔧 提高信号质量，改善胜率")
            elif result['profit_loss_ratio'] < 1.5:
                print("  🔧 优化止盈止损比例")
            elif result['max_drawdown'] > 0.15:
                print("  🔧 加强风险控制")
            else:
                print("  🎯 策略已基本成型，可进行实盘测试!")

    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
