# -*- coding: utf-8 -*-
import logging
import pandas as pd
import time
from typing import Dict, List, Optional, Type, Any
import sys
import os
import argparse
import threading

# ==============================================================================
# --- 路径设置与模块导入 ---
# ==============================================================================
# 将项目根目录添加到系统路径中
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
CONFIG_PATH = os.path.join(PROJECT_ROOT, '配置')
if CORE_CODE_PATH not in sys.path: sys.path.insert(0, CORE_CODE_PATH)
if CONFIG_PATH not in sys.path: sys.path.insert(0, CONFIG_PATH)
sys.path.insert(0, PROJECT_ROOT)

try:
    from 配置.系统配置 import Config
    from 核心代码.市场数据.数据获取器 import MarketData
    from 核心代码.交易策略.策略库 import TradingStrategy, STRATEGIES
    from 核心代码.风险管理.风险控制器 import DefaultRiskManager
    from 核心代码.资产组合.组合管理器 import Portfolio
    from 核心代码.因子计算.因子库 import calculate_factors
    # 暂不引入实盘执行器，聚焦模拟运行
    # from 核心代码.订单执行.实盘执行器基类 import RealExchangeExecutor
except ImportError as e:
    print(f"错误：无法从标准项目结构导入模块: {e}")
    print("请确保在项目根目录运行，或PYTHONPATH已正确设置。")
    sys.exit(1)

# ==============================================================================
# --- 日志配置 ---
# ==============================================================================
log_dir = '日志'
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'live_trading_system.log')
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] [%(name)s]: %(message)s",
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8', mode='a'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("实盘模拟系统")

# ==============================================================================
# --- 实时交易系统核心类 (原交易系统基类整合于此) ---
# ==============================================================================
class LiveTradingSystem:
    """
    一个为实时模拟或实盘交易设计的、持续运行的量化系统。
    """
    def __init__(self, config: Config):
        self.config = config
        self.stop_requested = False
        self._last_processed_time: Dict[str, float] = {}

        logger.info(f"初始化实时交易系统: 初始资金={config.initial_capital:.2f}, 模式={'实盘' if config.live_trading else '内部模拟'}")

        # --- 初始化核心组件 ---
        # 注意：这里的 portfolio, risk_manager, market_data_handler
        # 都是我们之前修复好的、可靠的版本。
        self.portfolio = Portfolio(config)
        self.risk_manager = DefaultRiskManager(config.__dict__.copy())
        self.market_data = MarketData(config)

        # --- 加载策略 ---
        self.strategies: List[TradingStrategy] = []
        all_symbols = list(set(config.us_symbols + config.cn_symbols + config.crypto_pairs))
        for strategy_conf in config.strategies:
            name = strategy_conf.get('name')
            params = strategy_conf.get('params', {})
            if name in STRATEGIES:
                try:
                    # 注意：实时系统中的'engine'应是其自身，以便策略能调用 get_position_size 等方法
                    instance = STRATEGIES[name](self, all_symbols, params)
                    self.strategies.append(instance)
                    logger.info(f"已加载策略: {name}")
                except Exception as e:
                    logger.error(f"加载策略 {name} 失败: {e}", exc_info=True)
            else:
                logger.warning(f"策略 '{name}' 不在库中: {list(STRATEGIES.keys())}")
        
        # --- 初始化实盘执行器 (未来扩展) ---
        self.executor = None
        if self.config.live_trading:
            logger.warning("实盘交易模式已选择，但执行器尚未实现。系统将以日志模式运行。")
            # from 核心代码.订单执行.LogBasedExecutor import LogBasedExecutor
            # self.executor = LogBasedExecutor(...)
            # self.executor.connect()
            pass

    def request_stop(self):
        """请求优雅地停止主循环"""
        logger.info("收到停止请求，将在当前循环结束后退出。")
        self.stop_requested = True

    def run(self, interval_seconds: int = 60):
        """运行主交易循环，按固定间隔轮询。"""
        logger.info(f"交易系统主循环启动... 处理间隔: {interval_seconds} 秒")
        try:
            while not self.stop_requested:
                loop_start_time = time.monotonic()
                now_str = pd.Timestamp.now(tz='Asia/Shanghai').strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"======> 循环开始: {now_str} <======")
                
                try:
                    self.process_bar()
                except Exception as loop_error:
                    logger.exception("主循环迭代时发生错误:")
                
                elapsed = time.monotonic() - loop_start_time
                sleep_time = max(0, interval_seconds - elapsed)
                logger.info(f"======> 循环结束, 耗时: {elapsed:.2f}s, 休眠: {sleep_time:.2f}s <======\n")
                if sleep_time > 0:
                    time.sleep(sleep_time)
        except KeyboardInterrupt:
            logger.info("用户中断 (Ctrl+C)，开始关闭系统...")
        finally:
            self.shutdown()

    def process_bar(self):
        """处理一个时间单位（Bar）的数据和交易逻辑。"""
        # 1. 获取所有相关品种的最新市场数据
        # 在实时系统中，我们需要获取“刚刚”结束的那个K线的数据
        now = pd.Timestamp.now(tz='UTC')
        self.current_dt = now # 设定当前时间，供策略使用
        
        all_symbols = list(set(self.config.crypto_pairs + self.config.us_symbols + self.config.cn_symbols))
        if not all_symbols:
            logger.warning("未配置任何交易品种，跳过处理。")
            return
            
        # 简化：假设我们能获取到所有品种的最新价格快照
        # 真实场景中，数据获取会更复杂
        # 此处我们用一个简化的方式模拟获取最新价格
        current_prices_dict = {}
        all_bars_data = []

        # 模拟获取最新K线数据和计算因子
        for symbol in all_symbols:
            # 真实场景中，这里会调用API获取最近的K线数据
            # 为演示，我们从本地文件加载最近的一段数据
            end_date = now.strftime('%Y-%m-%d')
            start_date = (now - pd.Timedelta(days=90)).strftime('%Y-%m-%d')
            df = self.market_data.get_market_data(symbol, start_date, end_date, source='local')
            if df is None or df.empty:
                logger.warning(f"无法获取 {symbol} 的最新数据，跳过。")
                continue
            
            # 计算因子
            df_with_factors = calculate_factors(df, self.config.factor_config)
            latest_bar = df_with_factors.iloc[-1:].copy() # 取最后一行作为当前bar
            latest_bar['Symbol'] = symbol
            all_bars_data.append(latest_bar)
            current_prices_dict[symbol] = latest_bar['CLOSE'].iloc[0]

        if not all_bars_data:
            logger.info("未能获取任何品种的最新市场数据。")
            return

        current_bars_snapshot = pd.concat(all_bars_data).set_index('Symbol')
        current_prices_series = pd.Series(current_prices_dict)

        # 2. 优先执行止损止盈检查
        logger.debug("开始监控止损止盈...")
        self.portfolio.monitor_and_execute_sl_tp(current_prices_series, now)

        # 3. 运行所有策略，生成交易信号
        all_signals: List[Dict] = []
        for strategy in self.strategies:
            logger.debug(f"运行策略 {strategy.strategy_name}...")
            try:
                signals = strategy.on_bar(current_bars_snapshot)
                if signals:
                    all_signals.extend(signals)
            except Exception as e:
                logger.error(f"运行策略 {strategy.strategy_name} 时出错: {e}", exc_info=True)

        # 4. 执行交易信号
        if all_signals:
            logger.info(f"共生成 {len(all_signals)} 个交易信号，准备执行...")
            for signal in all_signals:
                if self.risk_manager.check_trade(**signal, portfolio=self.portfolio)[0]:
                    if self.config.live_trading and self.executor:
                        # self.executor.execute_order(signal) # 对接实盘执行器
                        logger.info(f"(模拟实盘) 执行订单: {signal}")
                    else:
                        self.portfolio.execute_trade(signal) # 使用内部模拟执行
        else:
            logger.info("本轮无交易信号生成。")
        
        # 5. 打印当前组合状态
        self.report_portfolio_status(current_prices_series)

    def report_portfolio_status(self, current_prices: pd.Series):
        """打印当前投资组合的详细状态"""
        portfolio_info = self.portfolio.get_portfolio_value(current_prices)
        logger.info("-" * 50)
        logger.info(f"组合状态报告 @ {self.current_dt.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"  总资产: {portfolio_info['total']:,.2f}")
        logger.info(f"  现金: {portfolio_info['cash']:,.2f}")
        logger.info(f"  持仓市值: {portfolio_info['positions_value']:,.2f}")
        if self.portfolio.positions:
            logger.info("  当前持仓:")
            for symbol, details in self.portfolio.positions.items():
                current_price = current_prices.get(symbol, 0)
                market_value = details['shares'] * current_price
                pnl = (current_price - details['avg_price']) * details['shares'] if details['avg_price'] > 0 else 0
                logger.info(f"    - {symbol}: 数量={details['shares']:.4f}, 成本={details['avg_price']:.4f}, "
                            f"现价={current_price:.4f}, 市值={market_value:,.2f}, 浮动盈亏={pnl:,.2f}")
        else:
            logger.info("  当前无持仓。")
        logger.info("-" * 50)

    def shutdown(self):
        """关闭系统，释放资源"""
        logger.info("开始关闭交易系统...")
        if self.config.live_trading and self.executor:
            # self.executor.disconnect()
            pass
        logger.info("交易系统已成功关闭。")

    # 为策略提供与回测引擎兼容的接口
    def get_position_size(self, symbol: str) -> float:
        return self.portfolio.get_position_size(symbol)
        
    def get_portfolio_value(self, current_prices_snapshot: Optional[pd.Series] = None) -> Dict:
        # 在实时系统中，价格快照在 process_bar 中已经获取
        # 此处为策略提供一个统一接口
        return self.portfolio.get_portfolio_value(current_prices_snapshot)

# ==============================================================================
# --- 主程序入口 ---
# ==============================================================================
def main():
    parser = argparse.ArgumentParser(description='实时模拟/实盘量化交易系统')
    parser.add_argument('-i', '--interval', type=int, default=60, help='主循环的处理间隔时间 (秒)')
    parser.add_argument('--live', action='store_true', help='!!! 启动实盘交易模式（需要配置执行器）!!!')
    args = parser.parse_args()

    logger.info(f"====== 启动实时交易系统 ({'实盘' if args.live else '内部模拟'}) ======")
    
    if args.live:
        confirm = input("警告：您即将启动实盘交易模式！请确认了解所有风险。[输入 'yes' 确认]: ")
        if confirm.lower() != 'yes':
            print("操作取消。")
            sys.exit(0)

    # 加载默认配置
    config = Config()
    config.live_trading = args.live
    
    # 可以在这里覆盖默认配置，例如：
    # config.strategies = [
    #     {'name': 'AlphaXInspiredStrategy', 'params': {'risk_per_trade_pct': 0.005}}
    # ]

    try:
        system = LiveTradingSystem(config)
        system.run(interval_seconds=args.interval)
    except Exception as e:
        logger.critical(f"系统启动或运行时发生致命错误: {e}", exc_info=True)
        sys.exit(1)
    
    logger.info("====== 实时交易系统正常结束 ======")

if __name__ == "__main__":
    main()