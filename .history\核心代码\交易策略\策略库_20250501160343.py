# -*- coding: utf-8 -*-
import logging
import pandas as pd
from abc import ABC, abstractmethod
from typing import Optional
from backtesting import Strategy

logger = logging.getLogger(__name__)

class TradingStrategy(ABC):
    """交易策略基类"""
    def __init__(self):
        self._ready = False

    @abstractmethod
    def next(self):
        """执行策略逻辑"""
        pass

    def init(self):
        """初始化策略"""
        self._ready = True

class MeanReversionStrategy(Strategy):
    # 优化参数声明
    optimize_params = {
        'position_size': (1.0, 2.0, 0.1),
        'stop_loss': (0.01, 0.1, 0.01),
        'take_profit': (0.05, 0.2, 0.01),
        'rsi_overbought': (70, 80, 1),
        'rsi_oversold': (20, 30, 1),
        'bollinger_period': (10, 30, 1),
        'bollinger_std': (1.5, 2.5, 0.1),
        'trade_frequency': ('1D', '1W', '2W')  # 新增交易频率优化参数
    }

    # 默认参数值
    trade_frequency = '2W'  # 降低交易频率
    position_size = 1.0     # 降低仓位
    stop_loss = 0.05        # 增加止损
    take_profit = 0.1       # 降低止盈
    rsi_overbought = 80     # 提高RSI超买阈值
    rsi_oversold = 20       # 降低RSI超卖阈值
    bollinger_period = 30   # 增加布林带周期
    bollinger_std = 2.5     # 增加布林带标准差

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.parameters = {
            'trade_frequency': kwargs.get('trade_frequency', self.trade_frequency),
            'position_size': kwargs.get('position_size', self.position_size),
            'stop_loss': kwargs.get('stop_loss', self.stop_loss),
            'take_profit': kwargs.get('take_profit', self.take_profit),
            'rsi_overbought': kwargs.get('rsi_overbought', self.rsi_overbought),
            'rsi_oversold': kwargs.get('rsi_oversold', self.rsi_oversold),
            'bollinger_period': kwargs.get('bollinger_period', self.bollinger_period),
            'bollinger_std': kwargs.get('bollinger_std', self.bollinger_std)
        }
        # 初始化因子列名
        self.rsi_factor_name = 'RSI'
        self.macd_line_factor_name = 'MACD_Line'
        self.macd_signal_factor_name = 'Signal_Line'
        self.bb_lower_col = 'BB_Lower'
        self.bb_upper_col = 'BB_Upper'
        self.bb_middle_col = 'BB_Middle'

    # 因子列名定义
    bbands_factor_prefix = 'BB'
    rsi_factor_name = 'RSI'
    atr_factor_name = 'ATR'
    macd_line_factor_name = 'MACD_Line'
    macd_signal_factor_name = 'Signal_Line'
    macd_hist_factor_name = 'MACD_Hist'

    _ready: bool = False

    def init(self):
        logger.debug(f"初始化 {self.__class__.__name__}...")
        self.bb_lower_col = "BB_Lower"
        self.bb_upper_col = "BB_Upper"
        self.bb_middle_col = "BB_Middle"

        # 检查所需的因子列是否存在
        required_factors = [
            'Close',
            self.bb_lower_col,
            self.bb_upper_col,
            self.bb_middle_col,
            self.rsi_factor_name,
            self.macd_line_factor_name,
            self.macd_signal_factor_name,
        ]

        if not hasattr(self, 'data') or not hasattr(self.data, 'df'):
            logger.error("回测数据未正确初始化")
            self._ready = False
            return

        # 检查所需列是否存在
        missing = [f for f in required_factors if f not in self.data.df.columns]
        
        if missing:
            logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing}")
            logger.debug(f"可用列: {self.data.df.columns.tolist()}")
            self._ready = False
            return

        # --- 强制将数据列名转换为大写，以匹配 required_factors ---
        if hasattr(self.data, 'df') and self.data.df is not None:
             self.data.df.columns = [col.upper() for col in self.data.df.columns]
             logger.debug(f"策略 '{self.__class__.__name__}' 内部数据列名 (转换为大写): {self.data.df.columns.tolist()}")
        # --- 强制转换结束 ---

        # 检查所需列是否存在
        missing = [f for f in required_factors if f not in self.data.df.columns]

        # --- 调试检查：在策略内部检查数据列 ---
        logger.debug(f"策略 '{self.__class__.__name__}' 内部数据列名: {self.data.df.columns.tolist()}")
        # --- 调试检查结束 ---

        if missing:
            logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing}")
            logger.debug(f"可用列: {self.data.df.columns.tolist()}")
            self._ready = False
            return

        logger.info(f"策略 '{self.__class__.__name__}' 初始化成功。")
        self._ready = True

    def next(self):
        if not self._ready:
            return

        # 获取数据
        price = self.data.Close[-1]
        bb_low_val = self.data[self.bb_lower_col][-1]
        bb_up_val = self.data[self.bb_upper_col][-1]
        rsi_val = self.data[self.rsi_factor_name][-1]

        try:
            # 检查MACD列是否存在
            if self.macd_line_factor_name in self.data.df.columns and self.macd_signal_factor_name in self.data.df.columns:
                macd_line_val = self.data[self.macd_line_factor_name][-1]
                macd_signal_val = self.data[self.macd_signal_factor_name][-1]
            else:
                # 如果MACD列不存在，使用默认值
                macd_line_val = 0
                macd_signal_val = 0

            if not all(pd.notna(v) for v in [price, bb_low_val, bb_up_val, rsi_val]):
                return

            # 获取参数
            rsi_ob = self.parameters.get('rsi_overbought', 75)
            rsi_os = self.parameters.get('rsi_oversold', 25)
            sl_pct = self.parameters.get('stop_loss_pct', 0.03)
            tp_pct = self.parameters.get('take_profit_pct', 0.15)

            sl_buy = price * (1 - sl_pct)
            tp_buy = price * (1 + tp_pct)
            sl_sell = price * (1 + sl_pct)
            tp_sell = price * (1 - tp_pct)

            # 调试日志
            logger.debug(f"日期: {self.data.index[-1]}, 价格: {price:.2f}, "
                         f"BB上轨: {bb_up_val:.2f}, BB下轨: {bb_low_val:.2f}, "
                         f"RSI: {rsi_val:.2f} (超卖:{rsi_os}, 超买:{rsi_ob}), "
                         f"MACD线: {macd_line_val:.3f}, MACD信号: {macd_signal_val:.3f}")

            # 改进的交易逻辑
            # 添加MACD交叉确认
            macd_cross_up = macd_line_val > macd_signal_val and self.data[self.macd_line_factor_name][-2] <= self.data[self.macd_signal_factor_name][-2]
            macd_cross_down = macd_line_val < macd_signal_val and self.data[self.macd_line_factor_name][-2] >= self.data[self.macd_signal_factor_name][-2]

            # 进一步优化的买入条件
            buy_condition = (price < bb_low_val * 1.05 or  # 进一步放宽布林带下轨条件
                           rsi_val < 25 or  # 放宽RSI超卖条件
                           macd_cross_up)  # 保留MACD金叉

            # 进一步优化的卖出条件
            sell_condition = (price > bb_up_val * 0.95 or  # 进一步放宽布林带上轨条件
                            rsi_val > 75 or  # 放宽RSI超买条件
                            macd_cross_down)  # 保留MACD死叉

            logger.debug(f"买入条件: {buy_condition}")
            logger.debug(f"卖出条件: {sell_condition}")

            if sell_condition:
                if not self.position:
                    logger.info(">>> 触发卖出信号")
                    self.sell(sl=sl_sell, tp=tp_sell)
            elif buy_condition:
                if not self.position:
                    logger.info(">>> 触发买入信号")
                    self.buy(sl=sl_buy, tp=tp_buy)

        except Exception as e:
            logger.error(f"执行策略时发生错误: {e}", exc_info=True)
            self._ready = False  # 出错时暂停策略

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        bb_low_col = "BB_Lower"
        bb_up_col = "BB_Upper"
        rsi_col = self.rsi_factor_name
        required = ['Close', bb_low_col, bb_up_col, rsi_col]

        # 检查列是否存在
        missing_cols = [col for col in required if col not in target_data.columns]
        if missing_cols:
            logger.error(f"生成信号缺少列: {missing_cols}，可用列: {target_data.columns.tolist()}")
            return pd.DataFrame({'Signal': 0}, index=target_data.index)

        signals_df = pd.DataFrame(index=target_data.index)
        price = target_data['Close']
        bb_low = target_data[bb_low_col]
        bb_up = target_data[bb_up_col]
        rsi = target_data[rsi_col]
        rsi_ob = self.parameters.get('rsi_overbought', 75)
        rsi_os = self.parameters.get('rsi_oversold', 25)

        buy_condition = (price < bb_low) & (rsi < rsi_os)
        sell_condition = (price > bb_up) & (rsi > rsi_ob)

        signals_df['RawSignal'] = pd.Series(0, index=target_data.index)
        signals_df.loc[buy_condition, 'RawSignal'] = 1
        signals_df.loc[sell_condition, 'RawSignal'] = -1
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        return signals_df[['Signal']]

from dataclasses import dataclass

@dataclass
class BacktestResult:
    """封装回测结果的类"""
    strategy_name: str
    returns: pd.Series
    trades: pd.DataFrame
    stats: dict

# 定义可用的策略
STRATEGIES = {
    'EnhancedMeanReversion': MeanReversionStrategy
}
