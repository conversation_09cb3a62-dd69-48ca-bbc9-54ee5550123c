# 策略库重构迁移指南

## 概述

为了解决策略库命名混乱、分类不清、使用不便的问题，我们对策略库进行了全面重构。

## 主要改进

1. **规范化命名**: 采用分类前缀 + 编号的方式 (如: B01, O03, A02)
2. **清晰分类**: 按功能和复杂度分为6大类
3. **用户友好**: 提供简短的中文名称
4. **智能推荐**: 根据用户画像推荐合适策略

## 策略分类体系

- **B类 (基础策略)**: 适合新手，简单易懂
- **O类 (优化策略)**: 经过参数优化，适合有经验用户
- **A类 (AI策略)**: 使用机器学习，适合高级用户
- **H类 (高级策略)**: 复杂策略，适合专业用户
- **C类 (保守策略)**: 低风险，适合稳健投资
- **G类 (激进策略)**: 高风险高收益
- **S类 (特殊策略)**: 特定目标策略

## 迁移对照表

| 原策略名称 | 新策略ID | 新策略名称 | 分类 |
|-----------|---------|-----------|------|
| MeanReversionStrategy | B01 | 均值回归 | 基础策略 |
| TrendFollowingStrategy | B02 | 趋势跟踪 | 基础策略 |
| OptimizedMeanReversionStrategy | O01 | 优化均值回归 | 优化策略 |
| BalancedMeanReversionStrategy | O02 | 平衡均值回归 | 优化策略 |
| ImprovedTrendFollowingStrategy | O03 | 改进趋势跟踪 | 优化策略 |
| PracticalMeanReversionStrategy | O04 | 实用均值回归 | 优化策略 |
| OptimizedTrendStrategyAI | A01 | AI趋势预测 | AI策略 |
| AIOptimizedAlphaXStrategy | A02 | AI优化阿尔法 | AI策略 |
| MLEnhancedTrendFollowingStrategy | A03 | ML增强趋势 | AI策略 |
| SimplifiedAIAlphaXStrategy | A04 | 简化AI阿尔法 | AI策略 |
| AlphaXInspiredStrategy | H01 | 阿尔法X | 高级策略 |
| MultiTimeframeTrendFollowingStrategy | H02 | 多时间框架 | 高级策略 |
| OptimizedAlphaXStrategy | H03 | 优化阿尔法X | 高级策略 |
| EnhancedAlphaXStrategyV2 | H04 | 增强阿尔法V2 | 高级策略 |
| UltraConservativeStrategy | C01 | 超保守 | 保守策略 |
| AggressiveMeanReversionStrategy | G01 | 激进均值回归 | 激进策略 |
| FinalProfitableStrategy | S01 | 最终盈利 | 特殊策略 |
| MonthlyProfitableStrategy | S02 | 月月盈利 | 特殊策略 |
| FixedMeanReversionStrategy | S03 | 修复均值回归 | 特殊策略 |

## 使用方法

### 1. 使用策略选择器 (推荐)
```bash
python 策略选择器.py
```

### 2. 使用策略管理器
```python
from 核心代码.交易策略.策略库管理器 import StrategyLibraryManager

manager = StrategyLibraryManager()

# 搜索策略
results = manager.search_strategies('AI')

# 获取推荐
recommendations = manager.recommend_strategies({
    'experience': '新手',
    'risk_tolerance': '低', 
    'goal': '稳健'
})
```

### 3. 向后兼容
原有的策略名称仍然可以使用，系统会自动映射到新的策略。

## 注意事项

1. 建议逐步迁移到新的命名体系
2. 新项目请使用新的策略ID和名称
3. 如有问题，请参考策略映射表
