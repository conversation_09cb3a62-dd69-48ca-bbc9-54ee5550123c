# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

from ..交易策略.事件驱动策略基类 import EventDrivenStrategy

class TradingStrategy(EventDrivenStrategy, BASE_STRATEGY):  # 多重继承，支持事件驱动和回测
    """交易策略基类，同时支持事件驱动和回测模式"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功
    performance_stats = {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_pnl': 0.0,
        'max_win': 0.0,
        'max_loss': 0.0,
        'long_trades': 0,
        'short_trades': 0
    }

    def update_performance_stats(self, pnl: float, trade_type: str):
        """
        更新绩效统计数据
        
        Args:
            pnl: 盈亏比例
            trade_type: 交易类型 ('long' 或 'short')
        """
        self.performance_stats['total_trades'] += 1
        self.performance_stats['total_pnl'] += pnl
        
        if trade_type == 'long':
            self.performance_stats['long_trades'] += 1
        else:
            self.performance_stats['short_trades'] += 1
            
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            if pnl > self.performance_stats['max_win']:
                self.performance_stats['max_win'] = pnl
        else:
            self.performance_stats['losing_trades'] += 1
            if pnl < self.performance_stats['max_loss']:
                self.performance_stats['max_loss'] = pnl
                
        logger.info(f"更新绩效统计: 总交易数={self.performance_stats['total_trades']}, 总盈亏={self.performance_stats['total_pnl']:.2%}")

    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 分别调用两个父类的 __init__ 方法
        # 调用 EventDrivenStrategy 的 __init__，只传递 params
        EventDrivenStrategy.__init__(self, params_input) # 使用原始传入的 params

        # 调用 backtesting.Strategy (或其替身 BASE_STRATEGY) 的 __init__
        # 必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        if backtesting_available and isinstance(self, BacktestStrategy):
             BASE_STRATEGY.__init__(self, broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init().


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass

    # 重写 buy 方法以支持回测和事件驱动模式
    def buy(self, symbol: Optional[str] = None, volume: Optional[float] = None, price: Optional[float] = None,
            size: Optional[float] = None, sl: Optional[float] = None, tp: Optional[float] = None, **kwargs):
        """
        买入操作。根据运行模式调用相应的父类方法。
        在回测模式下，使用 size, sl, tp 参数。
        在事件驱动模式下，使用 symbol, volume, price 参数。
        """
        if backtesting_available and isinstance(self, BacktestStrategy):
            # 回测模式，调用 backtesting.Strategy 的 buy
            # backtesting 的 buy 方法不接受 symbol 参数，它操作的是当前数据源的品种
            # size 参数是必需的，sl/tp 是可选的
            if size is None:
                 logger.error("在回测模式下调用 buy 必须提供 size 参数")
                 return None
            return BASE_STRATEGY.buy(self, size=size, sl=sl, tp=tp, **kwargs)
        else:
            # 事件驱动模式，调用 EventDrivenStrategy 的 buy
            # EventDrivenStrategy 的 buy 方法需要 symbol 和 volume
            if symbol is None or volume is None:
                 logger.error("在事件驱动模式下调用 buy 必须提供 symbol 和 volume 参数")
                 return None
            return EventDrivenStrategy.buy(self, symbol=symbol, volume=volume, price=price)

    # 重写 sell 方法以支持回测和事件驱动模式
    def sell(self, symbol: Optional[str] = None, volume: Optional[float] = None, price: Optional[float] = None,
             size: Optional[float] = None, sl: Optional[float] = None, tp: Optional[float] = None, **kwargs):
        """
        卖出操作。根据运行模式调用相应的父类方法。
        在回测模式下，使用 size, sl, tp 参数。
        在事件驱动模式下，使用 symbol, volume, price 参数。
        """
        if backtesting_available and isinstance(self, BacktestStrategy):
            # 回测模式，调用 backtesting.Strategy 的 sell
            # backtesting 的 sell 方法不接受 symbol 参数，它操作的是当前数据源的品种
            # size 参数是必需的，sl/tp 是可选的
            if size is None:
                 logger.error("在回测模式下调用 sell 必须提供 size 参数")
                 return None
            return BASE_STRATEGY.sell(self, size=size, sl=sl, tp=tp, **kwargs)
        else:
            # 事件驱动模式，调用 EventDrivenStrategy 的 sell
            # EventDrivenStrategy 的 sell 方法需要 symbol 和 volume
            if symbol is None or volume is None:
                 logger.error("在事件驱动模式下调用 sell 必须提供 symbol 和 volume 参数")
                 return None
            return EventDrivenStrategy.sell(self, symbol=symbol, volume=volume, price=price)


class TrendFollowingStrategy(TradingStrategy):
    """增强版趋势跟踪策略(多因子组合)"""
    # --- 可优化参数 ---
    momentum_window = 20     # 动量窗口(日)
    volatility_window = 10   # 波动率窗口 
    rsi_window = 14         # RSI窗口
    stop_loss_pct = 0.02    # 基础止损比例
    take_profit_pct = 0.06  # 基础止盈比例
    max_position_pct = 0.2  # 最大仓位比例
    # --- 固定参数 ---
    atr_window = 14         # ATR窗口
    pe_threshold = 15       # PE估值阈值
    
    def __init__(self, broker, data, params=None):
        super().__init__(broker, data, params)
        # 动态调整仓位大小的因子权重
        self.position_adjust_factors = {
            'momentum': 0.4,
            'volatility': 0.3,
            'valuation': 0.3
        }

    def init(self):
        # 多因子计算
        # 动量因子(20日收益率)
        self.momentum = self.I(
            lambda: np.concatenate([
                [np.nan] * self.momentum_window,
                (self.data.Close[self.momentum_window:] / 
                 self.data.Close[:-self.momentum_window] - 1)
            ])
        )
        # 波动率因子(ATR标准化)
        self.atr = self.I(
            lambda: np.array([
                np.std(self.data.Close[max(0,i-self.atr_window+1):i+1]) 
                for i in range(len(self.data.Close))
            ])
        )
        self.norm_atr = self.I(
            lambda: self.atr / np.array([
                np.mean(self.data.Close[max(0,i-self.volatility_window+1):i+1])
                for i in range(len(self.data.Close))
            ])
        )
        # RSI指标
        def calc_rsi(prices, window):
            deltas = np.diff(prices)
            seed = deltas[:window]
            up = seed[seed >= 0].sum()/window
            down = -seed[seed < 0].sum()/window
            rs = up/down
            rsi = np.zeros_like(prices)
            rsi[:window] = 100. - 100./(1.+rs)
            
            for i in range(window, len(prices)):
                delta = deltas[i-1]
                if delta > 0:
                    upval = delta
                    downval = 0.
                else:
                    upval = 0.
                    downval = -delta
                
                up = (up*(window-1) + upval)/window
                down = (down*(window-1) + downval)/window
                rs = up/down
                rsi[i] = 100. - 100./(1.+rs)
            return rsi
        
        self.rsi = self.I(lambda: calc_rsi(self.data.Close, self.rsi_window))

        # 布林带指标 (如果还需要的话，也应将参数设为类变量)
        # self.bb_window = self.parameters.get('bb_window', 20)
        # self.bb_std = self.parameters.get('bb_std', 2)
        # self.bb_middle = self.I(pd.Series(self.data.Close).rolling(self.bb_window).mean)
        # self.bb_std_dev = self.I(pd.Series(self.data.Close).rolling(self.bb_window).std)
        # self.bb_upper = self.I(lambda: self.bb_middle + self.bb_std * self.bb_std_dev)
        # self.bb_lower = self.I(lambda: self.bb_middle - self.bb_std * self.bb_std_dev)


    def next(self):
        current_price = self.data.Close[-1]
        momentum = self.momentum[-1]
        norm_atr = self.norm_atr[-1]
        rsi = self.rsi[-1]

        # 初始化止损/止盈水平
        stop_loss_level = self.stop_loss_pct
        take_profit_level = self.take_profit_pct
        
        # 动态仓位计算
        position_score = 0
        # 动量因子评分(0-1)
        momentum_score = min(max((momentum + 0.1) / 0.3, 0), 1) 
        # 波动率因子评分(反向,0-1)
        volatility_score = 1 - min(max(norm_atr / 0.1, 0), 1)
        # 估值因子评分(简化版,0-1)
        valuation_score = 0.7 if hasattr(self.data, 'PE') and self.data.PE[-1] < self.pe_threshold else 0.3
        
        # 综合评分
        position_score = (
            momentum_score * self.position_adjust_factors['momentum'] +
            volatility_score * self.position_adjust_factors['volatility'] + 
            valuation_score * self.position_adjust_factors['valuation']
        )
        
        # 计算动态仓位
        dynamic_position = min(
            self.max_position_pct * position_score,
            self.max_position_pct
        )

        # 严格风险控制
        if self.position:
            entry_price = getattr(self.position, 'entry_price', current_price)
            current_pnl = (current_price - entry_price) / entry_price
            
            # 动态止损(2倍ATR或固定止损)
            stop_loss_level = max(
                self.stop_loss_pct,
                2 * self.norm_atr[-1]
            )
            
            # 动态止盈(3倍ATR或固定止盈)
            take_profit_level = max(
                self.take_profit_pct,
                3 * self.norm_atr[-1]
            )
            
            # 止损/止盈逻辑
            if current_pnl <= -stop_loss_level or current_pnl >= take_profit_level:
                self.position.close()
                self.update_performance_stats(current_pnl, self.position.type)
                return

        # 交易信号生成(需满足多因子条件)
        if (momentum > 0.1 and                  # 动量向上
            rsi > 30 and rsi < 70 and           # RSI合理区间
            norm_atr < 0.15 and                 # 波动率不过高
            len(self.trades) == 0 or            # 无持仓或
            (len(self.trades) > 0 and           # 满足最小持仓周期
             len(self.data) - self.trades[-1].exit_bar > 10)):
            
            # 买入信号
            if momentum > 0.15 and rsi < 60:
                entry_price = current_price
                stop_loss_price = entry_price * (1 - stop_loss_level)
                take_profit_price = entry_price * (1 + take_profit_level)
                
                self.buy(size=dynamic_position, 
                        sl=stop_loss_price,
                        tp=take_profit_price)
            
            # 卖出信号(做空)
            elif momentum < -0.1 and rsi > 40:
                entry_price = current_price
                stop_loss_price = entry_price * (1 + stop_loss_level)
                take_profit_price = entry_price * (1 - take_profit_level)
                
                self.sell(size=dynamic_position,
                         sl=stop_loss_price,
                         tp=take_profit_price)

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略"""
    def __init__(self, broker, data, params=None):
        super().__init__(broker, data, params)
        self.window = params.get('window', 10)
        self.threshold = params.get('threshold', 1.0)

    def init(self):
        self.sma = self.I(pd.Series(self.data.Close).rolling(self.window).mean)
        self.std = self.I(pd.Series(self.data.Close).rolling(self.window).std)

    def next(self):
        if self.position:
            return

        current_price = self.data.Close[-1]
        upper_band = self.sma[-1] + self.threshold * self.std[-1]
        lower_band = self.sma[-1] - self.threshold * self.std[-1]

        if current_price > upper_band:
            self.sell()
        elif current_price < lower_band:
            self.buy()

# 策略注册表
STRATEGIES = {
    "MeanReversion": MeanReversionStrategy,
    "TrendFollowing": TrendFollowingStrategy
}
