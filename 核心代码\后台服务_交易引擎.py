# -*- coding: utf-8 -*-
"""
量化交易后台引擎服务 (WebSocket 版本)
版本：v3.0 (逻辑梳理与功能增强)
功能：实时交易执行模拟、账户状态监控、通过WebSocket与前端交互
"""

import asyncio
import websockets
import json
import logging
import time
import pandas as pd
import numpy as np
from typing import Dict, Set
from dataclasses import dataclass, field
import traceback

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s: %(message)s", handlers=[logging.StreamHandler()])
logger = logging.getLogger("交易引擎服务")

# --- 核心数据结构 ---
@dataclass
class Position:
    symbol: str
    shares: float
    avg_price: float
    current_price: float = 0.0

    @property
    def market_value(self) -> float:
        return self.shares * self.current_price

    @property
    def pnl(self) -> float:
        if self.avg_price == 0: return 0.0
        return (self.current_price - self.avg_price) * self.shares

    def to_dict(self):
        return {"shares": self.shares, "avg_price": self.avg_price, "current_price": self.current_price,
                "market_value": self.market_value, "pnl": self.pnl}

@dataclass
class Account:
    cash: float = 1000000.0
    positions: Dict[str, Position] = field(default_factory=dict)

    @property
    def total_assets(self) -> float:
        return self.cash + sum(pos.market_value for pos in self.positions.values())

    def to_dict(self):
        return {"total": self.total_assets, "cash": self.cash, "positions": {sym: pos.to_dict() for sym, pos in self.positions.items()}}

# --- 交易引擎核心 ---
class TradingEngine:
    def __init__(self):
        self.status = "stopped"
        self.account = Account()
        self.connected_clients: Set[websockets.WebSocketServerProtocol] = set()
        
        # 模拟市场数据
        self.symbols = ["AAPL", "BTC-USD", "GOOG", "TSLA"]
        self.price_history = {s: [np.random.uniform(100, 500)] for s in self.symbols}
        self.market_data_task = None

    async def broadcast(self, message: dict):
        if self.connected_clients:
            await asyncio.wait([client.send(json.dumps(message)) for client in self.connected_clients])

    async def update_market_data(self):
        """模拟市场数据更新并广播"""
        logger.info("市场数据模拟器启动。")
        while self.status == "running":
            for symbol in self.symbols:
                last_price = self.price_history[symbol][-1]
                new_price = abs(last_price * (1 + np.random.normal(0, 0.001))) # 保证价格为正
                self.price_history[symbol].append(new_price)
                if len(self.price_history[symbol]) > 200: self.price_history[symbol].pop(0)
                if symbol in self.account.positions:
                    self.account.positions[symbol].current_price = new_price
            
            await self.broadcast({"type": "portfolio_update", "data": self.account.to_dict()})
            await asyncio.sleep(2) # 每2秒更新一次
        logger.info("市场数据模拟器停止。")

    async def execute_trade(self, symbol: str, amount: float) -> Tuple[bool, str]:
        """执行交易逻辑，返回成功状态和消息。"""
        if symbol not in self.symbols:
            return False, f"交易失败: 未知的交易品种 {symbol}"
            
        current_price = self.price_history[symbol][-1]
        cost = abs(amount) * current_price
        
        if amount > 0:  # 买入
            if self.account.cash < cost:
                return False, f"交易失败: 现金不足，需要 {cost:.2f}"
            self.account.cash -= cost
            if symbol in self.account.positions:
                pos = self.account.positions[symbol]
                new_total_cost = (pos.avg_price * pos.shares) + (current_price * amount)
                pos.shares += amount
                pos.avg_price = new_total_cost / pos.shares
            else:
                self.account.positions[symbol] = Position(symbol=symbol, shares=amount, avg_price=current_price, current_price=current_price)
            return True, f"买入 {amount} 股 {symbol} @ {current_price:.2f} 成功"
        else:  # 卖出
            abs_amount = abs(amount)
            if symbol not in self.account.positions or self.account.positions[symbol].shares < abs_amount:
                return False, f"交易失败: 持仓不足，需要 {abs_amount} 股"
            self.account.cash += cost
            self.account.positions[symbol].shares += amount # amount is negative
            if abs(self.account.positions[symbol].shares) < 1e-9:
                del self.account.positions[symbol]
            return True, f"卖出 {abs_amount} 股 {symbol} @ {current_price:.2f} 成功"

    async def start(self):
        if self.status == "stopped":
            self.status = "running"
            self.market_data_task = asyncio.create_task(self.update_market_data())
            await self.broadcast({"type": "status_update", "data": "running"})
            logger.info("交易引擎已启动")

    async def stop(self):
        if self.status == "running":
            self.status = "stopped"
            if self.market_data_task:
                self.market_data_task.cancel()
                try:
                    await self.market_data_task
                except asyncio.CancelledError:
                    pass
            await self.broadcast({"type": "status_update", "data": "stopped"})
            logger.info("交易引擎已停止")

# --- WebSocket 服务端 ---
engine = TradingEngine()

async def handler(websocket: websockets.WebSocketServerProtocol):
    """处理客户端连接和消息"""
    engine.connected_clients.add(websocket)
    logger.info(f"客户端连接: {websocket.remote_address}")
    try:
        await websocket.send(json.dumps({"type": "status_update", "data": engine.status}))
        await websocket.send(json.dumps({"type": "portfolio_update", "data": engine.account.to_dict()}))
        
        async for message in websocket:
            try:
                msg = json.loads(message)
                command = msg.get("command")

                if command == "start": await engine.start()
                elif command == "stop": await engine.stop()
                elif command == "trade":
                    symbol = msg.get("symbol")
                    amount = float(msg.get("amount", 0))
                    success, feedback = await engine.execute_trade(symbol, amount)
                    level = "info" if success else "error"
                    await engine.broadcast({"type": "log", "level": level, "data": feedback})
            except json.JSONDecodeError:
                await websocket.send(json.dumps({"type": "error", "data": "无效的JSON消息"}))
            except Exception as e:
                error_info = f"处理消息时出错: {e}\n{traceback.format_exc()}"
                logger.error(error_info)
                await websocket.send(json.dumps({"type": "error", "data": str(e)}))
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"客户端断开: {websocket.remote_address}")
    finally:
        engine.connected_clients.remove(websocket)

async def main_server():
    """启动WebSocket服务器"""
    async with websockets.serve(handler, "0.0.0.0", 7867, ping_interval=20):
        logger.info("交易引擎WebSocket服务已启动，监听 ws://0.0.0.0:7867")
        await asyncio.Future()  # run forever

if __name__ == "__main__":
    try:
        asyncio.run(main_server())
    except KeyboardInterrupt:
        logger.info("服务被手动中断。")