# -*- coding: utf-8 -*-
"""
创建简化均值回归策略 - 使用原始价格数据和简单技术指标
"""
import sys
import os

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def create_simple_mean_reversion_strategy():
    """创建简化均值回归策略"""
    
    strategy_code = '''# -*- coding: utf-8 -*-
"""
简化均值回归策略 - 使用现有技术指标，避免复杂的布林带计算
主要特点：
1. 使用价格相对于SMA的偏离度作为均值回归信号
2. 结合RSI超卖条件
3. 更好的盈亏比和交易频率控制
4. 适应现有数据结构
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class SimpleMeanReversionStrategy:
    """简化均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'SimpleMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 简化的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.025)  # 价格偏离2.5%
        
        # RSI参数
        self.rsi_key = all_params.get('rsi_key', 'RSI_14')
        self.rsi_oversold = all_params.get('rsi_oversold', 30)
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.01)
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 3.0)
        
        # 交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 120)
        self.max_daily_trades = all_params.get('max_daily_trades', 4)
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        print(f"策略 SimpleMeanReversionStrategy 初始化...")
        print(f"SimpleMeanReversionStrategy: 价格偏离={self.price_deviation_pct*100}%, "
              f"RSI≤{self.rsi_oversold}, 盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_price_deviation(self, data: Dict[str, Any]) -> bool:
        """检查价格是否偏离均线足够远（均值回归机会）"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 价格低于短期均线一定百分比时，认为是均值回归机会
        deviation = (sma_short - price) / sma_short
        return deviation >= self.price_deviation_pct
    
    def check_trend_not_too_strong(self, data: Dict[str, Any]) -> bool:
        """检查趋势不要太强（适合均值回归）"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 短期均线和长期均线差距不要太大
        trend_strength = abs(sma_short - sma_long) / sma_long
        return trend_strength <= 0.05  # 5%以内的差距
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 简化均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.sma_short_key]
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        sma_short = data.get(self.sma_short_key)
        rsi = data.get(self.rsi_key, 50)  # 默认中性RSI
        atr = data.get('ATR_14', price * 0.02)  # 默认2%的ATR
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, sma_short]):
            return signals
        
        # 简化的买入条件
        buy_conditions = [
            self.check_price_deviation(data),  # 价格偏离均线足够远
            rsi <= self.rsi_oversold,  # RSI超卖（如果有RSI数据）
            self.check_trend_not_too_strong(data),  # 趋势不要太强
        ]
        
        # 如果没有RSI数据，移除RSI条件
        if pd.isna(rsi) or rsi == 50:
            buy_conditions = [
                self.check_price_deviation(data),
                self.check_trend_not_too_strong(data),
            ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'SimpleMeanReversionStrategy',
                'signal_type': 'simple_mean_reversion',
                'reason': f'简化均值回归: 价格偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}<SMA={sma_short:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] SimpleMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}<SMA={sma_short:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'SimpleMeanReversionStrategy',
            'version': '1.0',
            'description': '简化均值回归策略，使用价格偏离度和RSI',
            'features': [
                '使用价格相对SMA偏离度',
                '结合RSI超卖条件',
                '适应现有数据结构',
                '良好的盈亏比',
                '交易频率控制'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'rsi_oversold': self.rsi_oversold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
'''
    
    # 保存策略文件
    strategy_file_path = os.path.join(CORE_CODE_PATH, '交易策略', 'simple_mean_reversion_strategy.py')
    
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.write(strategy_code)
    
    print(f"简化均值回归策略已保存到: {strategy_file_path}")
    return strategy_file_path

def update_strategy_library_simple():
    """更新策略库，添加简化均值回归策略"""
    
    strategy_lib_path = os.path.join(CORE_CODE_PATH, '交易策略', '策略库.py')
    
    # 读取现有策略库文件
    with open(strategy_lib_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加简化均值回归策略导入
    import_line = "from .simple_mean_reversion_strategy import SimpleMeanReversionStrategy"
    
    if import_line not in content:
        # 在文件末尾添加
        additional_content = f'''

# 简化均值回归策略导入
{import_line}

# 更新策略字典
STRATEGIES['SimpleMeanReversionStrategy'] = SimpleMeanReversionStrategy
'''
        content += additional_content
        
        # 写回文件
        with open(strategy_lib_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"策略库已更新: {strategy_lib_path}")

if __name__ == '__main__':
    print("创建简化均值回归策略")
    print("=" * 50)
    
    # 创建简化策略
    create_simple_mean_reversion_strategy()
    update_strategy_library_simple()
    
    print("\n✅ 简化均值回归策略创建完成！")
    print("\n🔧 简化设计：")
    print("• 使用价格相对SMA_20的偏离度（2.5%）")
    print("• 结合RSI≤30超卖条件（如果有数据）")
    print("• 检查趋势强度，避免强趋势中逆势")
    print("• 盈亏比：1.5:1（3.0ATR止盈，2.0ATR止损）")
    print("• 信号间隔：120分钟")
    print("• 每日最多4次交易")
    
    print("\n📊 预期效果：")
    print("• 适应现有数据结构")
    print("• 减少对复杂指标的依赖")
    print("• 保持均值回归策略的核心逻辑")
    print("• 控制交易频率和风险")
    
    print("\n🧪 测试命令：")
    print("./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2024-04-01 --end_date 2024-04-30 --strategy SimpleMeanReversionStrategy")
