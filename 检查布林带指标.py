# -*- coding: utf-8 -*-
"""
检查布林带指标名称
"""
import pandas as pd
import os

def check_bollinger_bands():
    """检查数据中的布林带指标名称"""
    
    print("=== 检查布林带指标名称 ===")
    
    # 加载一些样本数据检查
    data_path = r"D:\市场数据\现金\BTCUSDT\BTCUSDT-1m-2024-04.csv"
    
    print(f"正在检查数据文件: {data_path}")
    
    # 读取前1000行数据
    df = pd.read_csv(data_path, nrows=1000)
    print(f"数据形状: {df.shape}")
    
    print(f"\n所有列名:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i}. {col}")
    
    # 查找布林带相关的列
    bb_columns = [col for col in df.columns if 'BB' in col or 'Band' in col or 'bband' in col.lower()]
    
    print(f"\n布林带相关列 ({len(bb_columns)}个):")
    for col in bb_columns:
        print(f"  • {col}")
    
    # 查找RSI相关的列
    rsi_columns = [col for col in df.columns if 'RSI' in col]
    
    print(f"\nRSI相关列 ({len(rsi_columns)}个):")
    for col in rsi_columns:
        print(f"  • {col}")
    
    # 查找ADX相关的列
    adx_columns = [col for col in df.columns if 'ADX' in col]
    
    print(f"\nADX相关列 ({len(adx_columns)}个):")
    for col in adx_columns:
        print(f"  • {col}")
    
    # 查找ATR相关的列
    atr_columns = [col for col in df.columns if 'ATR' in col]
    
    print(f"\nATR相关列 ({len(atr_columns)}个):")
    for col in atr_columns:
        print(f"  • {col}")
    
    # 查找成交量相关的列
    volume_columns = [col for col in df.columns if 'VOLUME' in col or 'Volume' in col]
    
    print(f"\n成交量相关列 ({len(volume_columns)}个):")
    for col in volume_columns:
        print(f"  • {col}")
    
    # 检查一些样本数据
    if bb_columns:
        print(f"\n布林带数据样本:")
        sample_data = df[bb_columns + ['CLOSE']].head()
        print(sample_data.to_string())
    
    return bb_columns, rsi_columns, adx_columns, atr_columns

def suggest_correct_strategy():
    """建议正确的策略配置"""
    
    print(f"\n=== 建议的策略修正 ===")
    
    # 基于实际数据列名的建议
    suggestions = [
        "1. 检查布林带指标名称格式",
        "2. 可能需要使用标准格式：BBands_20_2_0_BB_Lower",
        "3. 或者简化策略，使用现有的技术指标",
        "4. 考虑直接使用价格相对于SMA的位置作为均值回归信号"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    print(f"\n=== 简化版均值回归策略建议 ===")
    
    simplified_approach = [
        "使用价格相对于SMA的偏离度作为信号",
        "当价格低于SMA_20的2%时买入",
        "结合RSI<30的超卖条件",
        "使用ATR计算止损止盈",
        "避免复杂的布林带计算"
    ]
    
    for approach in simplified_approach:
        print(f"  • {approach}")

if __name__ == '__main__':
    bb_cols, rsi_cols, adx_cols, atr_cols = check_bollinger_bands()
    suggest_correct_strategy()
    
    print("\n" + "=" * 50)
    if not bb_cols:
        print("❌ 没有找到布林带指标，需要修改策略实现")
        print("💡 建议使用SMA偏离度替代布林带")
    else:
        print("✅ 找到布林带指标，检查名称格式是否匹配")
        print("💡 建议使用实际的列名格式")
