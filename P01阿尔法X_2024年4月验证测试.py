# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 2024年4月验证测试
验证策略在已知盈利期间的实际表现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import logging
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

def load_april_2024_data():
    """加载2024年4月数据"""
    try:
        data_path = "D:/市场数据/现金/BTCUSDT/BTCUSDT-1m-2024-04.csv"
        
        if not os.path.exists(data_path):
            print(f"❌ 数据文件不存在: {data_path}")
            return None
        
        print(f"📊 加载2024年4月BTCUSDT数据...")
        
        # 读取数据
        df = pd.read_csv(data_path)
        
        # 标准化列名
        if len(df.columns) >= 6:
            df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
        
        # 转换时间戳
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        # 重命名列
        df.rename(columns={
            'open': 'OPEN',
            'high': 'HIGH', 
            'low': 'LOW',
            'close': 'CLOSE',
            'volume': 'VOLUME'
        }, inplace=True)
        
        print(f"✅ 数据加载完成: {len(df)} 条记录")
        print(f"   时间范围: {df.index[0]} 至 {df.index[-1]}")
        print(f"   价格范围: {df['CLOSE'].min():.2f} - {df['CLOSE'].max():.2f}")
        
        return df
        
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return None

def run_alphax_backtest():
    """运行阿尔法X策略回测"""
    try:
        print("🚀 开始P01阿尔法X2024策略验证测试")
        print("=" * 60)
        print("测试期间: 2024年4月1日 - 2024年4月30日")
        print("测试目标: 验证策略在已知盈利期间的表现")
        print("=" * 60)
        
        # 导入回测引擎
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 创建配置
        config = Config()
        config.start_date = '2024-04-01'
        config.end_date = '2024-04-30'
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 获取策略类
        strategy_class = STRATEGIES.get('AlphaXInspiredStrategy')
        if not strategy_class:
            print("❌ 未找到AlphaXInspiredStrategy策略")
            return None
        
        # 策略参数 - 使用更宽松的条件
        strategy_params = {
            'sma_short': 12,
            'sma_long': 26,
            'rsi_period': 14,
            'atr_period': 14,
            'adx_period': 14,
            'adx_threshold': 20,  # 降低ADX阈值
            'rsi_overbought': 75,  # 放宽RSI条件
            'rsi_oversold': 25,
            'risk_per_trade_pct': 0.02,  # 增加单笔风险
            'atr_sl_multiple': 2.0,  # 降低止损倍数
            'atr_tp_multiple': 4.0,  # 降低止盈倍数
            'min_signal_interval_minutes': 60,  # 缩短信号间隔
            'max_position_size': 0.95,
            'trailing_stop_enabled': True,
            'dynamic_position_sizing': True
        }
        
        print(f"📋 策略参数:")
        for key, value in strategy_params.items():
            print(f"   {key}: {value}")
        print()
        
        # 创建回测引擎
        print("🔧 初始化回测引擎...")
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
        
        # 运行回测
        print("⚡ 开始回测...")
        start_time = time.time()
        results = backtester.run_backtest(config.start_date, config.end_date)
        execution_time = time.time() - start_time
        
        if results:
            print(f"\n✅ 回测完成! (耗时: {execution_time:.1f}秒)")
            print("=" * 60)
            print("📊 回测结果:")
            print("-" * 40)
            
            # 显示关键指标
            key_metrics = [
                ('总收益率', '总收益率', '%'),
                ('年化收益率', '年化收益率', '%'),
                ('最大回撤率', '最大回撤率', '%'),
                ('夏普比率', '夏普比率', ''),
                ('索提诺比率', '索提诺比率', ''),
                ('总交易次数', '总交易次数', '次'),
                ('盈利次数', '盈利次数', '次'),
                ('亏损次数', '亏损次数', '次'),
                ('胜率', '胜率', '%'),
                ('盈亏比', '盈亏比', ''),
                ('年化波动率', '年化波动率', '%')
            ]
            
            for display_name, key, unit in key_metrics:
                value = results.get(key, 0)
                if unit == '%':
                    print(f"{display_name}: {value*100:+.2f}%")
                elif unit == '次':
                    print(f"{display_name}: {value}")
                else:
                    print(f"{display_name}: {value:.2f}")
            
            # 客户目标达成分析
            print(f"\n🎯 客户目标达成分析:")
            print("-" * 40)
            
            annual_return = results.get('年化收益率', 0) * 100
            sharpe_ratio = results.get('夏普比率', 0)
            max_drawdown = results.get('最大回撤率', 0) * 100
            
            annual_ok = annual_return >= 15
            sharpe_ok = sharpe_ratio >= 2
            drawdown_ok = max_drawdown <= 15
            
            print(f"年化收益率: {annual_return:+.2f}% {'✅' if annual_ok else '❌'} (目标: ≥15%)")
            print(f"夏普比率: {sharpe_ratio:.2f} {'✅' if sharpe_ok else '❌'} (目标: ≥2.0)")
            print(f"最大回撤: {max_drawdown:.2f}% {'✅' if drawdown_ok else '❌'} (目标: ≤15%)")
            
            all_targets_met = annual_ok and sharpe_ok and drawdown_ok
            print(f"\n🏆 综合评价: {'完全达标' if all_targets_met else '部分达标' if sum([annual_ok, sharpe_ok, drawdown_ok]) >= 2 else '未达标'}")
            
            # 交易分析
            total_trades = results.get('总交易次数', 0)
            if total_trades > 0:
                print(f"\n📈 交易分析:")
                print("-" * 40)
                print(f"交易频率: {total_trades/30:.1f} 次/天")
                print(f"平均持仓时间: 估算 {24*30/total_trades:.1f} 小时")
                
                win_rate = results.get('胜率', 0) * 100
                profit_loss_ratio = results.get('盈亏比', 0)
                
                if win_rate >= 50:
                    print(f"胜率评价: 优秀 ({win_rate:.1f}%)")
                elif win_rate >= 40:
                    print(f"胜率评价: 良好 ({win_rate:.1f}%)")
                else:
                    print(f"胜率评价: 需改进 ({win_rate:.1f}%)")
                
                if profit_loss_ratio >= 2:
                    print(f"盈亏比评价: 优秀 ({profit_loss_ratio:.2f})")
                elif profit_loss_ratio >= 1.5:
                    print(f"盈亏比评价: 良好 ({profit_loss_ratio:.2f})")
                else:
                    print(f"盈亏比评价: 需改进 ({profit_loss_ratio:.2f})")
            else:
                print(f"\n⚠️ 警告: 策略在测试期间未产生任何交易!")
                print("可能原因:")
                print("  1. 信号生成条件过于严格")
                print("  2. 市场条件不符合策略要求")
                print("  3. 参数设置需要调整")
            
            # 保存结果
            save_results(results, execution_time)
            
            return results
        else:
            print("❌ 回测失败")
            return None
            
    except Exception as e:
        logger.error(f"回测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def save_results(results, execution_time):
    """保存测试结果"""
    try:
        # 创建结果DataFrame
        result_data = {
            '指标': [],
            '数值': [],
            '单位': []
        }
        
        metrics = [
            ('总收益率', results.get('总收益率', 0) * 100, '%'),
            ('年化收益率', results.get('年化收益率', 0) * 100, '%'),
            ('最大回撤率', results.get('最大回撤率', 0) * 100, '%'),
            ('夏普比率', results.get('夏普比率', 0), ''),
            ('索提诺比率', results.get('索提诺比率', 0), ''),
            ('总交易次数', results.get('总交易次数', 0), '次'),
            ('盈利次数', results.get('盈利次数', 0), '次'),
            ('亏损次数', results.get('亏损次数', 0), '次'),
            ('胜率', results.get('胜率', 0) * 100, '%'),
            ('盈亏比', results.get('盈亏比', 0), ''),
            ('年化波动率', results.get('年化波动率', 0) * 100, '%'),
            ('执行时间', execution_time, '秒')
        ]
        
        for metric, value, unit in metrics:
            result_data['指标'].append(metric)
            result_data['数值'].append(value)
            result_data['单位'].append(unit)
        
        df = pd.DataFrame(result_data)
        
        # 保存到CSV
        filename = f"P01阿尔法X_2024年4月验证测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"\n📄 测试结果已保存: {filename}")
        
    except Exception as e:
        logger.error(f"保存结果失败: {e}")

def main():
    """主函数"""
    print("🚀 启动P01阿尔法X2024策略验证测试...")
    
    # 检查数据
    data = load_april_2024_data()
    if data is None:
        return
    
    # 运行回测
    results = run_alphax_backtest()
    
    if results:
        print(f"\n🎉 验证测试完成!")
        
        # 结论
        total_return = results.get('总收益率', 0) * 100
        annual_return = results.get('年化收益率', 0) * 100
        total_trades = results.get('总交易次数', 0)
        
        print(f"\n📋 测试结论:")
        print("-" * 40)
        
        if total_return > 0:
            print(f"✅ 策略在2024年4月实现盈利: {total_return:+.2f}%")
            print(f"✅ 年化收益率: {annual_return:+.2f}%")
        else:
            print(f"❌ 策略在2024年4月未实现盈利: {total_return:+.2f}%")
        
        if total_trades > 0:
            print(f"✅ 策略产生了 {total_trades} 次交易")
        else:
            print(f"⚠️ 策略未产生任何交易，需要调整参数")
        
        print(f"\n💡 建议:")
        if total_trades == 0:
            print("  1. 降低ADX阈值 (当前20，建议15)")
            print("  2. 放宽RSI条件 (当前25-75，建议20-80)")
            print("  3. 缩短信号间隔 (当前60分钟，建议30分钟)")
            print("  4. 检查技术指标计算是否正确")
        elif total_return <= 0:
            print("  1. 优化止损止盈比例")
            print("  2. 调整仓位管理策略")
            print("  3. 改进信号过滤条件")
        else:
            print("  1. 策略表现良好，可以考虑扩展到其他时间段")
            print("  2. 进一步优化参数以提高收益")
    else:
        print("❌ 验证测试失败")

if __name__ == "__main__":
    main()
