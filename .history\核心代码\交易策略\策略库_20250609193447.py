# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import logging
from typing import Optional, Dict, List, Any, Tuple
import joblib
import os
import json

logger = logging.getLogger(__name__)

# --- 模型文件路径定义 ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
MODEL_DIR = os.path.join(PROJECT_ROOT, "模型文件")

# ==============================================================================
# --- 策略基类 ---
# ==============================================================================
class TradingStrategy:
    """
    事件驱动型交易策略的基类 (V3)。
    """
    def __init__(self, engine: Any, symbol_list: List[str], params: Optional[Dict[str, Any]] = None):
        self.engine = engine
        self.symbol_list = symbol_list
        self.parameters = params if params is not None else {}
        self.strategy_name = self.__class__.__name__
        logger.info(f"策略 {self.strategy_name} 初始化...")
        self.on_init()

    def on_init(self):
        pass

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        raise NotImplementedError("子类必须实现 on_bar 方法。")

    def _create_signal(self, **kwargs) -> Optional[Dict]:
        """
        创建一个标准化的交易信号字典。
        """
        size_abs = kwargs.get('size_abs')
        if size_abs is None or pd.isna(size_abs) or not isinstance(size_abs, (int, float, np.number)) or size_abs <= 1e-9:
            return None

        signal = {
            'symbol': kwargs.get('symbol'),
            'action': kwargs.get('action'),
            'price': float(kwargs.get('price')),
            'size': float(size_abs),
            'strategy': self.strategy_name,
            'timestamp': self.engine.current_dt,
            'signal_type': kwargs.get('signal_type'),
            'stop_loss_price': float(kwargs.get('stop_loss_price')) if kwargs.get('stop_loss_price') is not None and pd.notna(kwargs.get('stop_loss_price')) else None,
            'take_profit_price': float(kwargs.get('take_profit_price')) if kwargs.get('take_profit_price') is not None and pd.notna(kwargs.get('take_profit_price')) else None,
        }
        return signal

# ==============================================================================
# --- 策略1：MeanReversionStrategy (已修复并适配新框架) ---
# ==============================================================================
class MeanReversionStrategy(TradingStrategy):
    """
    均值回归策略 (修复版)。
    在布林带下轨和RSI超卖时买入。离场由统一的SL/TP机制处理。
    """
    def on_init(self):
        super().on_init()
        self.bbands_period = int(self.parameters.get('bbands_period', 20))
        self.bbands_std_dev = float(self.parameters.get('bbands_std_dev', 2.0))
        self.rsi_period = int(self.parameters.get('rsi_period', 14))
        self.rsi_oversold = float(self.parameters.get('rsi_oversold', 30.0))
        
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 1.5))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 2.5))
        
        self.bb_lower_key = f"BBands_{self.bbands_period}_{str(self.bbands_std_dev).replace('.', '_')}_BB_Lower"
        self.rsi_key = f"RSI_{self.rsi_period}"
        self.atr_key = 'ATR_14'

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol]
            price = data.get('CLOSE')
            bb_lower = data.get(self.bb_lower_key)
            rsi = data.get(self.rsi_key)

            if pd.isna(price) or pd.isna(bb_lower) or pd.isna(rsi): continue

            if price < bb_lower and rsi < self.rsi_oversold:
                atr = data.get(self.atr_key)
                if pd.isna(atr) or atr <= 0: continue

                stop_loss_price = price - atr * self.atr_sl_multiple
                take_profit_price = price + atr * self.atr_tp_multiple

                portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
                size = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)

                signal = self._create_signal(
                    symbol=symbol, action='buy', price=price, size_abs=size,
                    signal_type='entry_mean_reversion',
                    stop_loss_price=stop_loss_price, take_profit_price=take_profit_price
                )
                if signal:
                    signals.append(signal)
                    logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} 买入信号. RSI={rsi:.2f}, Price<BB_Lower.")
        return signals

# ==============================================================================
# --- 策略2：TrendFollowingStrategy (已修复并适配新框架) ---
# ==============================================================================
class TrendFollowingStrategy(TradingStrategy):
    """
    趋势跟踪策略 (修复版)。
    """
    def on_init(self):
        super().on_init()
        self.sma_short_period = int(self.parameters.get('sma_short_period', 20))
        self.sma_long_period = int(self.parameters.get('sma_long_period', 60))
        self.adx_period = int(self.parameters.get('adx_period', 14))
        self.adx_threshold = float(self.parameters.get('adx_threshold', 25.0))
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 2.0))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 4.0))
        self.sma_short_key = f"SMA_{self.sma_short_period}"
        self.sma_long_key = f"SMA_{self.sma_long_period}"
        self.adx_key = f"ADX_{self.adx_period}"
        self.atr_key = 'ATR_14'

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue

            data = current_bar_data.loc[symbol]
            price, sma_short, sma_long, adx = data.get('CLOSE'), data.get(self.sma_short_key), data.get(self.sma_long_key), data.get(self.adx_key)

            if pd.isna(price) or pd.isna(sma_short) or pd.isna(sma_long) or pd.isna(adx): continue

            if sma_short > sma_long and adx > self.adx_threshold:
                atr = data.get(self.atr_key)
                if pd.isna(atr) or atr <= 0: continue

                stop_loss_price = price - atr * self.atr_sl_multiple
                take_profit_price = price + atr * self.atr_tp_multiple
                portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
                size = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)

                signal = self._create_signal(
                    symbol=symbol, action='buy', price=price, size_abs=size,
                    signal_type='entry_trend_following',
                    stop_loss_price=stop_loss_price, take_profit_price=take_profit_price
                )
                if signal:
                    signals.append(signal)
                    logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} 买入信号. SMA金叉, ADX={adx:.2f}.")
        return signals

# ==============================================================================
# --- 策略3：OptimizedTrendStrategyAI (已修复并适配新框架) ---
# ==============================================================================
class OptimizedTrendStrategyAI(TradingStrategy):
    """
    优化趋势AI策略 (加固版)。
    """
    _model = None
    _scaler = None
    _feature_list = None

    def on_init(self):
        super().on_init()
        self.model_path_prefix = self.parameters.get('model_path_prefix')
        self.model_confidence_threshold = float(self.parameters.get('model_confidence_threshold', 0.6))
        self.adx_key = self.parameters.get('adx_key', 'ADX_14')
        self.adx_threshold = float(self.parameters.get('adx_threshold', 20.0))
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.015))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 1.8))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 3.5))
        self.atr_key = 'ATR_14'
        self._load_model_assets()
        logger.info(f"{self.strategy_name}: 初始化完成。AI模型: {'已加载' if self._model else '未加载'}.")

    def _load_model_assets(self):
        if not self.model_path_prefix:
            logger.warning(f"[{self.strategy_name}] 未提供 'model_path_prefix' 参数，AI功能禁用。")
            return
        try:
            self._model = joblib.load(os.path.join(MODEL_DIR, f"{self.model_path_prefix}_model.joblib"))
            self._scaler = joblib.load(os.path.join(MODEL_DIR, f"{self.model_path_prefix}_scaler.joblib"))
            with open(os.path.join(MODEL_DIR, f"{self.model_path_prefix}_features.json"), 'r') as f:
                self._feature_list = json.load(f)
            logger.info(f"[{self.strategy_name}] AI模型及相关文件加载成功 for '{self.model_path_prefix}'.")
        except Exception as e:
            logger.error(f"[{self.strategy_name}] 加载AI模型时出错: {e}", exc_info=True)
            self._model = None

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index or abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol]
            prediction, confidence = self._get_ai_prediction(data)
            adx = data.get(self.adx_key, 0)

            if prediction == 1 and confidence >= self.model_confidence_threshold and adx > self.adx_threshold:
                price, atr = data.get('CLOSE'), data.get(self.atr_key)
                if pd.isna(price) or pd.isna(atr) or price <= 0 or atr <= 0: continue
                stop_loss_price = price - atr * self.atr_sl_multiple
                take_profit_price = price + atr * self.atr_tp_multiple
                portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
                size = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, self.risk_per_trade_pct, stop_loss_price)
                signal = self._create_signal(symbol=symbol, action='buy', price=price, size_abs=size, signal_type='entry_trend_ai', stop_loss_price=stop_loss_price, take_profit_price=take_profit_price)
                if signal:
                    signals.append(signal)
                    logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} AI买入信号. Conf={confidence:.2f}, ADX={adx:.2f}.")
        return signals

    def _get_ai_prediction(self, data: pd.Series) -> Tuple[Optional[int], float]:
        if not all([self._model, self._scaler, self._feature_list]): return None, 0.0
        try:
            features_df = data[self._feature_list].to_frame().T
            if features_df.isnull().values.any(): return None, 0.0
            scaled_features = self._scaler.transform(features_df)
            probabilities = self._model.predict_proba(scaled_features)[0]
            return (1, probabilities[1]) if probabilities[1] >= self.model_confidence_threshold else (0, probabilities[1])
        except Exception:
            return None, 0.0

# ==============================================================================
# --- 策略4：AlphaXInspiredStrategy (作为标杆和最佳实践) ---
# ==============================================================================
class AlphaXInspiredStrategy(TradingStrategy):
    """
    AlphaX风格策略 (V3.0 - 增强版)
    新增功能：
    1. 趋势跟踪：在强上升趋势中增加仓位
    2. 动态止损：根据波动率调整止损距离
    3. 分批建仓：避免一次性满仓
    """
    def on_init(self):
        super().on_init()
        self.sma_short_key = self.parameters.get('sma_short_key', 'SMA_20')
        self.sma_long_key = self.parameters.get('sma_long_key', 'SMA_60')
        self.adx_key = self.parameters.get('adx_key', 'ADX_14')
        self.adx_threshold = float(self.parameters.get('adx_threshold', 25.0))
        self.rsi_key = self.parameters.get('rsi_key', 'RSI_14')
        self.rsi_oversold = float(self.parameters.get('rsi_oversold', 35.0))
        self.risk_per_trade_pct = float(self.parameters.get('risk_per_trade_pct', 0.01))
        self.atr_sl_multiple = float(self.parameters.get('atr_sl_multiple', 2.0))
        self.atr_tp_multiple = float(self.parameters.get('atr_tp_multiple', 4.0))
        self.atr_key = 'ATR_14'
        self.min_signal_interval_minutes = int(self.parameters.get('min_signal_interval_minutes', 120))

        # 新增：趋势跟踪参数
        self.trend_strength_threshold = float(self.parameters.get('trend_strength_threshold', 40.0))  # 强趋势ADX阈值
        self.trend_multiplier = float(self.parameters.get('trend_multiplier', 1.5))  # 强趋势时的仓位倍数

        # 新增：动态止损参数
        self.volatility_lookback = int(self.parameters.get('volatility_lookback', 20))  # 波动率回看期
        self.min_sl_multiple = float(self.parameters.get('min_sl_multiple', 1.5))  # 最小止损倍数
        self.max_sl_multiple = float(self.parameters.get('max_sl_multiple', 3.0))  # 最大止损倍数

        # 新增：分批建仓参数
        self.batch_count = int(self.parameters.get('batch_count', 3))  # 分批次数
        self.batch_interval_minutes = int(self.parameters.get('batch_interval_minutes', 30))  # 分批间隔
        self.max_position_pct = float(self.parameters.get('max_position_pct', 0.03))  # 最大总仓位比例

        # 状态跟踪
        self.last_signal_time: Dict[str, pd.Timestamp] = {}
        self.position_batches: Dict[str, List[Dict]] = {}  # 记录分批建仓信息
        self.dynamic_sl_history: Dict[str, List[float]] = {}  # 动态止损历史

        logger.info(f"{self.strategy_name}: 增强版初始化完成。趋势跟踪={self.trend_multiplier}x, 分批建仓={self.batch_count}批")

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue

            data = current_bar_data.loc[symbol]
            current_position = abs(self.engine.get_position_size(symbol))

            # 检查是否可以继续建仓
            if current_position > 1e-9:
                # 已有持仓，检查是否可以加仓
                if self._can_add_position(symbol, data):
                    add_signals = self._generate_add_position_signals(symbol, data)
                    signals.extend(add_signals)

                # 更新动态止损
                self._update_dynamic_stop_loss(symbol, data)
            else:
                # 无持仓，检查是否可以开仓
                if self._should_enter_position(symbol, data):
                    entry_signals = self._generate_entry_signals(symbol, data)
                    signals.extend(entry_signals)

        return signals

    def _should_enter_position(self, symbol: str, data: pd.Series) -> bool:
        """检查是否应该开仓"""
        # 检查信号间隔
        if symbol in self.last_signal_time:
            time_diff = (self.engine.current_dt - self.last_signal_time[symbol]).total_seconds() / 60
            if time_diff < self.min_signal_interval_minutes:
                return False

        # 检查基本条件
        return self._is_strong_uptrend(data) and self._is_buy_trigger(data)

    def _can_add_position(self, symbol: str, data: pd.Series) -> bool:
        """检查是否可以加仓"""
        if symbol not in self.position_batches:
            return False

        batches = self.position_batches[symbol]
        if len(batches) >= self.batch_count:
            return False

        # 检查加仓间隔
        last_batch_time = batches[-1]['timestamp']
        time_diff = (self.engine.current_dt - last_batch_time).total_seconds() / 60
        if time_diff < self.batch_interval_minutes:
            return False

        # 检查是否仍在强趋势中
        return self._is_very_strong_uptrend(data)

    def _is_strong_uptrend(self, data: pd.Series) -> bool:
        """检查是否为强上升趋势"""
        vals = [data.get(k) for k in [self.sma_short_key, self.sma_long_key, self.adx_key]]
        return not any(pd.isna(v) for v in vals) and vals[0] > vals[1] and vals[2] > self.adx_threshold

    def _is_very_strong_uptrend(self, data: pd.Series) -> bool:
        """检查是否为非常强的上升趋势（用于加仓判断）"""
        vals = [data.get(k) for k in [self.sma_short_key, self.sma_long_key, self.adx_key]]
        if any(pd.isna(v) for v in vals):
            return False

        # 更严格的趋势条件
        sma_short, sma_long, adx = vals
        return (sma_short > sma_long * 1.02 and  # 短期均线明显高于长期均线
                adx > self.trend_strength_threshold and  # ADX超过强趋势阈值
                data.get('CLOSE') > sma_short)  # 价格在短期均线之上

    def _is_buy_trigger(self, data: pd.Series) -> bool:
        """买入触发条件"""
        rsi = data.get(self.rsi_key)
        return not pd.isna(rsi) and rsi < self.rsi_oversold

    def _generate_entry_signals(self, symbol: str, data: pd.Series) -> List[Dict]:
        """生成首次开仓信号（分批建仓第一批）"""
        signals = []

        # 计算动态止损距离
        dynamic_sl_multiple = self._calculate_dynamic_stop_loss_multiple(data)

        # 计算基础仓位大小
        base_position_size = self._calculate_base_position_size(symbol, data, dynamic_sl_multiple)
        if base_position_size is None or base_position_size <= 1e-9:
            return signals

        # 检查是否为超强趋势，决定是否增加仓位
        is_very_strong = self._is_very_strong_uptrend(data)
        position_multiplier = self.trend_multiplier if is_very_strong else 1.0

        # 分批建仓：第一批使用基础仓位的1/batch_count
        first_batch_size = (base_position_size * position_multiplier) / self.batch_count

        entry_package = self._calculate_entry_package(symbol, data, first_batch_size, dynamic_sl_multiple)
        if entry_package:
            signal = self._create_signal(**entry_package)
            if signal:
                signals.append(signal)

                # 记录分批建仓信息
                if symbol not in self.position_batches:
                    self.position_batches[symbol] = []

                batch_info = {
                    'batch_number': 1,
                    'size': first_batch_size,
                    'price': data.get('CLOSE'),
                    'timestamp': self.engine.current_dt,
                    'is_strong_trend': is_very_strong
                }
                self.position_batches[symbol].append(batch_info)

                self.last_signal_time[symbol] = self.engine.current_dt

                trend_info = "超强趋势" if is_very_strong else "普通趋势"
                logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} 首次开仓(1/{self.batch_count}批), "
                          f"仓位={first_batch_size:.4f}, {trend_info}, 动态止损倍数={dynamic_sl_multiple:.2f}")

        return signals

    def _generate_add_position_signals(self, symbol: str, data: pd.Series) -> List[Dict]:
        """生成加仓信号"""
        signals = []

        if symbol not in self.position_batches:
            return signals

        batches = self.position_batches[symbol]
        next_batch_number = len(batches) + 1

        if next_batch_number > self.batch_count:
            return signals

        # 计算加仓大小（与第一批相同）
        first_batch = batches[0]
        add_size = first_batch['size']

        # 使用当前的动态止损
        dynamic_sl_multiple = self._calculate_dynamic_stop_loss_multiple(data)

        entry_package = self._calculate_entry_package(symbol, data, add_size, dynamic_sl_multiple)
        if entry_package:
            signal = self._create_signal(**entry_package)
            if signal:
                signals.append(signal)

                # 记录加仓信息
                batch_info = {
                    'batch_number': next_batch_number,
                    'size': add_size,
                    'price': data.get('CLOSE'),
                    'timestamp': self.engine.current_dt,
                    'is_strong_trend': True  # 能加仓说明趋势很强
                }
                self.position_batches[symbol].append(batch_info)

                logger.info(f"[{self.engine.current_dt}] {self.strategy_name}: {symbol} 加仓({next_batch_number}/{self.batch_count}批), "
                          f"仓位={add_size:.4f}, 动态止损倍数={dynamic_sl_multiple:.2f}")

        return signals

    def _calculate_dynamic_stop_loss_multiple(self, data: pd.Series) -> float:
        """计算动态止损倍数（基于波动率）"""
        try:
            # 获取ATR值作为波动率指标
            current_atr = data.get(self.atr_key)
            if pd.isna(current_atr) or current_atr <= 0:
                return self.atr_sl_multiple

            # 获取历史ATR数据进行比较（这里简化处理，实际应该从历史数据获取）
            # 假设当前ATR相对于"正常"ATR的比例来调整止损

            # 简化版本：基于RSI来调整止损距离
            rsi = data.get(self.rsi_key)
            if pd.isna(rsi):
                return self.atr_sl_multiple

            # RSI越低（超卖越严重），止损距离越大（给更多空间）
            # RSI越高，止损距离越小（更严格的止损）
            if rsi < 20:  # 极度超卖
                multiplier = self.max_sl_multiple
            elif rsi < 30:  # 超卖
                multiplier = self.atr_sl_multiple * 1.2
            elif rsi > 70:  # 超买
                multiplier = self.min_sl_multiple
            else:  # 正常范围
                multiplier = self.atr_sl_multiple

            # 确保在合理范围内
            return max(self.min_sl_multiple, min(self.max_sl_multiple, multiplier))

        except Exception as e:
            logger.warning(f"计算动态止损倍数时出错: {e}")
            return self.atr_sl_multiple

    def _calculate_base_position_size(self, symbol: str, data: pd.Series, sl_multiple: float) -> Optional[float]:
        """计算基础仓位大小"""
        try:
            price = data.get('CLOSE')
            atr = data.get(self.atr_key)

            if pd.isna(price) or pd.isna(atr) or price <= 0 or atr <= 0:
                return None

            portfolio_value = self.engine.get_portfolio_value(data.to_frame().T)['total']
            stop_loss_price = price - atr * sl_multiple

            # 使用风险管理器计算仓位，但限制最大仓位
            max_risk_amount = portfolio_value * self.max_position_pct
            normal_risk_amount = portfolio_value * self.risk_per_trade_pct

            # 选择较小的风险金额
            risk_amount = min(max_risk_amount, normal_risk_amount)

            size_abs = self.engine.risk_manager.calculate_trade_size(portfolio_value, price, risk_amount/portfolio_value, stop_loss_price)

            return size_abs

        except Exception as e:
            logger.warning(f"计算基础仓位大小时出错: {e}")
            return None

    def _calculate_entry_package(self, symbol: str, data: pd.Series, size_abs: float, sl_multiple: float) -> Optional[Dict]:
        """计算入场包（使用动态止损）"""
        try:
            price = data.get('CLOSE')
            atr = data.get(self.atr_key)

            if pd.isna(price) or pd.isna(atr) or price <= 0 or atr <= 0:
                return None

            # 使用动态止损倍数
            stop_loss_price = price - atr * sl_multiple
            take_profit_price = price + atr * self.atr_tp_multiple

            return {
                'symbol': symbol,
                'action': 'buy',
                'price': price,
                'size_abs': size_abs,
                'signal_type': 'entry_alphax_enhanced',
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price
            }

        except Exception as e:
            logger.warning(f"计算入场包时出错: {e}")
            return None

    def _update_dynamic_stop_loss(self, symbol: str, data: pd.Series):
        """更新动态止损（持仓期间调整止损）"""
        try:
            current_position = self.engine.get_position_size(symbol)
            if abs(current_position) <= 1e-9:
                return

            price = data.get('CLOSE')
            atr = data.get(self.atr_key)

            if pd.isna(price) or pd.isna(atr) or price <= 0 or atr <= 0:
                return

            # 计算新的动态止损价格
            new_sl_multiple = self._calculate_dynamic_stop_loss_multiple(data)
            new_stop_loss = price - atr * new_sl_multiple

            # 记录止损历史
            if symbol not in self.dynamic_sl_history:
                self.dynamic_sl_history[symbol] = []

            self.dynamic_sl_history[symbol].append(new_stop_loss)

            # 只保留最近的止损历史
            if len(self.dynamic_sl_history[symbol]) > 10:
                self.dynamic_sl_history[symbol] = self.dynamic_sl_history[symbol][-10:]

            # 这里可以添加实际更新止损订单的逻辑
            # 注意：只能向有利方向调整止损（对多头来说，只能上调止损）

        except Exception as e:
            logger.warning(f"更新动态止损时出错: {e}")

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息（用于监控和调试）"""
        info = {
            'strategy_name': self.strategy_name,
            'version': '3.0 - 增强版',
            'features': [
                '趋势跟踪：强趋势时增加仓位',
                '动态止损：基于波动率调整止损距离',
                '分批建仓：避免一次性满仓'
            ],
            'parameters': {
                'trend_strength_threshold': self.trend_strength_threshold,
                'trend_multiplier': self.trend_multiplier,
                'batch_count': self.batch_count,
                'batch_interval_minutes': self.batch_interval_minutes,
                'max_position_pct': self.max_position_pct,
                'min_sl_multiple': self.min_sl_multiple,
                'max_sl_multiple': self.max_sl_multiple
            },
            'current_batches': {symbol: len(batches) for symbol, batches in self.position_batches.items()},
            'last_signals': {symbol: time.isoformat() for symbol, time in self.last_signal_time.items()}
        }
        return info

    def reset_position_tracking(self, symbol: str):
        """重置仓位跟踪（当仓位清零时调用）"""
        if symbol in self.position_batches:
            del self.position_batches[symbol]
        if symbol in self.dynamic_sl_history:
            del self.dynamic_sl_history[symbol]
        logger.info(f"{self.strategy_name}: 重置 {symbol} 的仓位跟踪信息")

# --- 策略字典 ---
STRATEGIES = {
    'MeanReversion': MeanReversionStrategy,
    'TrendFollowing': TrendFollowingStrategy,
    'OptimizedTrendStrategyAI': OptimizedTrendStrategyAI,
    'AlphaXInspiredStrategy': AlphaXInspiredStrategy,
    'OptimizedAlphaXStrategy': None,  # 将在导入后更新
}

# 优化策略导入
from .optimized_alphax_strategy import OptimizedAlphaXStrategy

# 更新策略字典
STRATEGIES['OptimizedAlphaXStrategy'] = OptimizedAlphaXStrategy

# 更新策略映射字典
STRATEGY_MAP = {
    'AlphaXInspiredStrategy': AlphaXInspiredStrategy,
    'TrendFollowingStrategy': TrendFollowingStrategy,
    'MeanReversionStrategy': MeanReversionStrategy,
    'OptimizedAlphaXStrategy': OptimizedAlphaXStrategy,  # 新增优化策略
}


# 增强策略V2导入
from .enhanced_alphax_strategy_v2 import EnhancedAlphaXStrategyV2

# 更新策略字典
STRATEGIES['EnhancedAlphaXStrategyV2'] = EnhancedAlphaXStrategyV2


# AI优化策略导入
from .ai_optimized_alphax_strategy import AIOptimizedAlphaXStrategy

# 更新策略字典
STRATEGIES['AIOptimizedAlphaXStrategy'] = AIOptimizedAlphaXStrategy


# 简化版AI策略导入
from .simplified_ai_alphax_strategy import SimplifiedAIAlphaXStrategy

# 更新策略字典
STRATEGIES['SimplifiedAIAlphaXStrategy'] = SimplifiedAIAlphaXStrategy


# 优化均值回归策略导入
from .optimized_mean_reversion_strategy import OptimizedMeanReversionStrategy

# 更新策略字典
STRATEGIES['OptimizedMeanReversionStrategy'] = OptimizedMeanReversionStrategy


# 平衡均值回归策略导入
from .balanced_mean_reversion_strategy import BalancedMeanReversionStrategy

# 更新策略字典
STRATEGIES['BalancedMeanReversionStrategy'] = BalancedMeanReversionStrategy


# 简化均值回归策略导入
from .simple_mean_reversion_strategy import SimpleMeanReversionStrategy

# 更新策略字典
STRATEGIES['SimpleMeanReversionStrategy'] = SimpleMeanReversionStrategy


# 增强均值回归策略导入
from .enhanced_mean_reversion_strategy import EnhancedMeanReversionStrategy

# 更新策略字典
STRATEGIES['EnhancedMeanReversionStrategy'] = EnhancedMeanReversionStrategy


# 增强版AlphaX策略V3导入
from .enhanced_alphax_strategy_v3 import EnhancedAlphaXStrategyV3

# 更新策略字典
STRATEGIES['EnhancedAlphaXStrategyV3'] = EnhancedAlphaXStrategyV3


# 激进均值回归策略导入
from .aggressive_mean_reversion_strategy import AggressiveMeanReversionStrategy

# 更新策略字典
STRATEGIES['AggressiveMeanReversionStrategy'] = AggressiveMeanReversionStrategy


# 盈利均值回归策略导入
from .profitable_mean_reversion_strategy import ProfitableMeanReversionStrategy

# 更新策略字典
STRATEGIES['ProfitableMeanReversionStrategy'] = ProfitableMeanReversionStrategy


# 平衡盈利策略导入
from .balanced_profitable_strategy import BalancedProfitableStrategy

# 更新策略字典
STRATEGIES['BalancedProfitableStrategy'] = BalancedProfitableStrategy


# 实用均值回归策略导入
from .practical_mean_reversion_strategy import PracticalMeanReversionStrategy

# 更新策略字典
STRATEGIES['PracticalMeanReversionStrategy'] = PracticalMeanReversionStrategy


# 最终盈利策略导入
from .final_profitable_strategy import FinalProfitableStrategy
from .improved_trend_following import ImprovedTrendFollowingStrategy

# 更新策略字典
STRATEGIES['FinalProfitableStrategy'] = FinalProfitableStrategy


# 月月盈利策略导入
from .monthly_profitable_strategy import MonthlyProfitableStrategy

# 更新策略字典
STRATEGIES['MonthlyProfitableStrategy'] = MonthlyProfitableStrategy


# 超保守策略导入
from .ultra_conservative_strategy import UltraConservativeStrategy

# 更新策略字典
STRATEGIES['UltraConservativeStrategy'] = UltraConservativeStrategy


# 修复版均值回归策略导入
from .fixed_mean_reversion_strategy import FixedMeanReversionStrategy

# 更新策略字典
STRATEGIES['FixedMeanReversionStrategy'] = FixedMeanReversionStrategy
