# -*- coding: utf-8 -*-
"""
P02简化均值回归策略
基于用户记忆中提到的"P02简化均值回归策略"进行优化
专注于高频均值回归交易，目标：年化收益≥15%, 月度胜率≥60%
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class SimplifiedMeanReversionStrategy:
    """P02简化均值回归策略 - 专注盈利的简化版本"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 简化但有效的参数设计
        self.strategy_params = {
            # 核心均值回归参数
            'lookback_period': 20,         # 均值回归周期
            'entry_threshold': 2.0,        # 入场阈值(标准差倍数)
            'exit_threshold': 0.5,         # 出场阈值(标准差倍数)
            
            # 仓位管理
            'base_position': 0.3,          # 基础仓位30%
            'max_position': 0.6,           # 最大仓位60%
            'position_scaling': True,      # 启用仓位缩放
            
            # 风险控制
            'stop_loss': 0.015,            # 止损1.5%
            'take_profit': 0.045,          # 止盈4.5% (3:1盈亏比)
            'max_holding_hours': 12,       # 最大持仓12小时
            
            # 交易频率控制
            'min_interval_minutes': 30,    # 最小交易间隔30分钟
            'max_daily_trades': 8,         # 每日最多8次交易
            
            # 市场过滤
            'volume_filter': True,         # 启用成交量过滤
            'volatility_filter': True,     # 启用波动率过滤
            'trend_filter': False,         # 关闭趋势过滤(均值回归不需要)
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_mean_reversion_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算均值回归指标"""
        try:
            df = data.copy()
            
            # 核心均值回归指标
            lookback = self.strategy_params['lookback_period']
            
            # 移动平均和标准差
            df['MA'] = df['CLOSE'].rolling(lookback).mean()
            df['STD'] = df['CLOSE'].rolling(lookback).std()
            
            # 价格偏离度 (Z-Score)
            df['Z_Score'] = (df['CLOSE'] - df['MA']) / df['STD']
            
            # 布林带
            df['BB_Upper'] = df['MA'] + (df['STD'] * 2)
            df['BB_Lower'] = df['MA'] - (df['STD'] * 2)
            df['BB_Position'] = (df['CLOSE'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
            
            # 价格动量 (短期)
            df['Price_Change_5'] = df['CLOSE'].pct_change(5)
            df['Price_Change_10'] = df['CLOSE'].pct_change(10)
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            # RSI (用于确认超买超卖)
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            return df
            
        except Exception as e:
            logger.warning(f"计算均值回归指标失败: {e}")
            return data
    
    def generate_mean_reversion_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成均值回归交易信号"""
        try:
            # 初始化信号
            df['Signal'] = 0
            df['Signal_Strength'] = 0
            
            entry_threshold = self.strategy_params['entry_threshold']
            exit_threshold = self.strategy_params['exit_threshold']
            
            # 买入信号：价格显著低于均值
            buy_condition = (
                (df['Z_Score'] < -entry_threshold) &  # 价格显著偏离均值
                (df['RSI'] < 35) &                    # RSI确认超卖
                (df['Price_Change_5'] > -0.01) &      # 开始止跌
                (df['Volume_Ratio'] > 0.8)            # 成交量支持
            )
            
            # 强买入信号：更极端的偏离
            strong_buy_condition = (
                (df['Z_Score'] < -entry_threshold * 1.5) &
                (df['RSI'] < 30) &
                (df['BB_Position'] < 0.1) &
                (df['Volume_Ratio'] > 1.2)
            )
            
            # 卖出信号：价格回归均值
            sell_condition = (
                (df['Z_Score'] > -exit_threshold) |   # 价格回归
                (df['RSI'] > 65) |                    # RSI超买
                (df['BB_Position'] > 0.8)             # 接近布林带上轨
            )
            
            # 设置信号
            df.loc[buy_condition, 'Signal'] = 1
            df.loc[strong_buy_condition, 'Signal'] = 2  # 强信号
            df.loc[sell_condition, 'Signal'] = -1
            
            # 计算信号强度
            df['Signal_Strength'] = abs(df['Z_Score'])
            
            return df
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return df
    
    def simulate_mean_reversion_strategy(self, data: pd.DataFrame) -> dict:
        """模拟简化均值回归策略"""
        try:
            print("🔄 模拟P02简化均值回归策略...")
            
            # 计算技术指标
            data = self.calculate_mean_reversion_indicators(data)
            data = self.generate_mean_reversion_signals(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            # 交易控制
            last_trade_time = None
            daily_trades = {}
            entry_time = None
            entry_price = 0
            stop_loss_price = 0
            take_profit_price = 0
            
            for i in range(30, len(data)):  # 从第30个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']
                
                # 更新权益曲线
                current_equity = cash + position * current_price
                equity_curve.append(current_equity)
                
                # 检查数据有效性
                if pd.isna(current_price) or pd.isna(current_data.get('Signal')):
                    continue
                
                # 检查每日交易限制
                current_date = current_time.date()
                if current_date not in daily_trades:
                    daily_trades[current_date] = 0
                
                if daily_trades[current_date] >= self.strategy_params['max_daily_trades']:
                    continue
                
                # 检查交易间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_interval_minutes']:
                        continue
                
                # 无持仓时检查入场
                if position == 0:
                    signal = current_data['Signal']
                    
                    if signal > 0:  # 买入信号
                        # 计算仓位大小
                        if signal == 2:  # 强信号
                            position_size = self.strategy_params['max_position']
                        else:  # 普通信号
                            position_size = self.strategy_params['base_position']
                        
                        # 根据信号强度调整仓位
                        if self.strategy_params['position_scaling']:
                            signal_strength = current_data.get('Signal_Strength', 1)
                            position_size *= min(signal_strength / 2, 1.5)  # 最多1.5倍
                            position_size = min(position_size, self.strategy_params['max_position'])
                        
                        # 计算股数
                        position_value = cash * position_size
                        shares = position_value / current_price
                        
                        if shares > 0:
                            position = shares
                            cash -= shares * current_price * 1.0005  # 手续费
                            entry_time = current_time
                            entry_price = current_price
                            
                            # 设置止损止盈
                            stop_loss_price = current_price * (1 - self.strategy_params['stop_loss'])
                            take_profit_price = current_price * (1 + self.strategy_params['take_profit'])
                            
                            trades.append({
                                'time': current_time,
                                'action': 'buy',
                                'price': current_price,
                                'shares': shares,
                                'signal': signal,
                                'signal_strength': current_data.get('Signal_Strength', 0),
                                'z_score': current_data.get('Z_Score', 0),
                                'rsi': current_data.get('RSI', 50),
                                'position_size': position_size
                            })
                            
                            last_trade_time = current_time
                            daily_trades[current_date] += 1
                
                # 有持仓时检查出场
                elif position > 0:
                    signal = current_data['Signal']
                    
                    # 计算持仓时间
                    holding_hours = (current_time - entry_time).total_seconds() / 3600
                    
                    # 出场条件
                    should_exit = False
                    exit_reason = ""
                    
                    # 止损
                    if current_price <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                    # 止盈
                    elif current_price >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                    # 信号转向
                    elif signal == -1:
                        should_exit = True
                        exit_reason = "信号转向"
                    # 超时
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "超时平仓"
                    # 均值回归完成
                    elif current_data.get('Z_Score', 0) > 0.5:
                        should_exit = True
                        exit_reason = "均值回归"
                    
                    if should_exit:
                        cash += position * current_price * 0.9995  # 扣除手续费
                        
                        pnl = (current_price - entry_price) * position
                        pnl_pct = pnl / (entry_price * position) * 100
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': exit_reason,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'holding_hours': holding_hours
                        })
                        
                        position = 0
                        entry_time = None
                        last_trade_time = current_time
                        daily_trades[current_date] += 1
            
            # 期末平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                pnl = (final_price - entry_price) * position
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': pnl
                })
                position = 0
            
            final_equity = cash
            
            # 计算统计指标
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ P02简化均值回归策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"P02简化均值回归策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P02简化均值回归策略测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    strategy = SimplifiedMeanReversionStrategy()
    
    print("\n💡 P02简化均值回归策略特点:")
    print("=" * 60)
    print("  1. 专注均值回归交易 (高胜率)")
    print("  2. Z-Score + RSI双重确认")
    print("  3. 3:1盈亏比设计")
    print("  4. 严格风险控制")
    print("  5. 高频交易机会")
    print("=" * 60)
    
    # 测试2024年4月
    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_mean_reversion_strategy(data)
        
        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1
            
            print(f"\n📊 P02简化均值回归策略测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"交易次数: {result['total_trades']}")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            
            # 客户目标达成检查
            print(f"\n🎯 客户目标达成检查:")
            print("-" * 40)
            annual_ok = annual_return >= 0.15
            sharpe_ok = result['sharpe_ratio'] >= 2.0
            drawdown_ok = result['max_drawdown'] <= 0.15
            win_rate_ok = result['win_rate'] >= 0.6
            
            print(f"年化收益率: {annual_return*100:+.2f}% {'✅' if annual_ok else '❌'} (目标: ≥15%)")
            print(f"夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'} (目标: ≥2.0)")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}% {'✅' if drawdown_ok else '❌'} (目标: ≤15%)")
            print(f"胜率: {result['win_rate']*100:.1f}% {'✅' if win_rate_ok else '❌'} (目标: ≥60%)")
            
            targets_met = sum([annual_ok, sharpe_ok, drawdown_ok, win_rate_ok])
            
            print(f"\n🏆 P02简化均值回归策略评价:")
            if targets_met >= 3:
                print("🎉 优秀表现! 基本达到客户目标!")
            elif targets_met == 2:
                print("✅ 良好表现! 接近客户目标!")
            elif targets_met == 1:
                print("⚠️ 一般表现! 需要进一步优化!")
            else:
                print("❌ 表现不佳! 需要重新设计!")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
