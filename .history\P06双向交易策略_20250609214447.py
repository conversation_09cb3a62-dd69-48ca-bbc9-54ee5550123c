# -*- coding: utf-8 -*-
"""
P06双向交易策略
模拟大机构的双向交易能力：上涨做多，下跌做空
这是关键突破：让我们能在任何市场环境下都有盈利机会
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class BidirectionalTradingStrategy:
    """P06双向交易策略 - 模拟大机构的做多做空能力"""

    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"

        # 双向交易策略参数
        self.strategy_params = {
            # 技术指标参数
            'ma_short': 12,                # 短期均线
            'ma_long': 26,                 # 长期均线
            'rsi_period': 14,              # RSI周期
            'macd_signal': 9,              # MACD信号线

            # 做多信号阈值
            'long_rsi_min': 35,            # 做多RSI最小值
            'long_rsi_max': 65,            # 做多RSI最大值
            'long_momentum_min': 0.002,    # 做多最小动量

            # 做空信号阈值
            'short_rsi_min': 35,           # 做空RSI最小值
            'short_rsi_max': 65,           # 做空RSI最大值
            'short_momentum_max': -0.002,  # 做空最大动量(负值)

            # 仓位管理
            'position_size': 0.25,         # 单向仓位25%
            'max_total_exposure': 0.5,     # 最大总敞口50%
            'allow_hedging': True,         # 允许对冲(同时持有多空)

            # 风险控制
            'stop_loss_long': 0.025,       # 做多止损2.5%
            'take_profit_long': 0.05,      # 做多止盈5%
            'stop_loss_short': 0.025,      # 做空止损2.5%
            'take_profit_short': 0.05,     # 做空止盈5%

            # 交易控制
            'min_signal_gap': 30,          # 最小信号间隔30分钟
            'max_holding_hours': 12,       # 最大持仓12小时
            'force_close_opposite': True,  # 反向信号时强制平仓
        }

    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")

            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)

            all_data = []
            current_date = start_dt

            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month

                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)

                if os.path.exists(filepath):
                    print(f"  加载: {filename}")

                    df = pd.read_csv(filepath)

                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])

                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)

                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH',
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)

                    df = df[(df.index >= start_date) & (df.index <= end_date)]

                    if not df.empty:
                        all_data.append(df)

                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)

            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")

            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]

            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data

        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise

    def calculate_bidirectional_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算双向交易指标"""
        try:
            df = data.copy()

            # 移动平均线
            df['MA_Short'] = df['CLOSE'].ewm(span=self.strategy_params['ma_short']).mean()
            df['MA_Long'] = df['CLOSE'].ewm(span=self.strategy_params['ma_long']).mean()

            # MACD指标
            df['MACD'] = df['MA_Short'] - df['MA_Long']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['macd_signal']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']

            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # 价格动量
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)

            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']

            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()

            # 趋势强度
            df['Trend_Strength'] = abs(df['MACD']) / df['CLOSE']

            return df

        except Exception as e:
            logger.warning(f"计算双向交易指标失败: {e}")
            return data

    def generate_bidirectional_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成双向交易信号"""
        try:
            # 初始化信号
            df['Long_Signal'] = 0    # 做多信号
            df['Short_Signal'] = 0   # 做空信号
            df['Signal_Strength'] = 0

            # 做多信号条件
            long_conditions = (
                (df['MACD'] > df['MACD_Signal']) &  # MACD金叉
                (df['MACD_Histogram'] > 0) &        # 柱状图为正
                (df['MA_Short'] > df['MA_Long']) &  # 短期均线在长期均线上方
                (df['RSI'] >= self.strategy_params['long_rsi_min']) &
                (df['RSI'] <= self.strategy_params['long_rsi_max']) &
                (df['Momentum_5'] > self.strategy_params['long_momentum_min']) &
                (df['Volume_Ratio'] > 0.8)          # 成交量支持
            )

            # 做空信号条件
            short_conditions = (
                (df['MACD'] < df['MACD_Signal']) &  # MACD死叉
                (df['MACD_Histogram'] < 0) &        # 柱状图为负
                (df['MA_Short'] < df['MA_Long']) &  # 短期均线在长期均线下方
                (df['RSI'] >= self.strategy_params['short_rsi_min']) &
                (df['RSI'] <= self.strategy_params['short_rsi_max']) &
                (df['Momentum_5'] < self.strategy_params['short_momentum_max']) &
                (df['Volume_Ratio'] > 0.8)          # 成交量支持
            )

            # 设置信号
            df.loc[long_conditions, 'Long_Signal'] = 1
            df.loc[short_conditions, 'Short_Signal'] = 1

            # 计算信号强度
            df['Signal_Strength'] = (
                abs(df['MACD_Histogram']) * 0.4 +
                df['Trend_Strength'] * 100 * 0.3 +
                abs(df['Momentum_5']) * 100 * 0.2 +
                (df['Volume_Ratio'] - 1) * 0.1
            )

            return df

        except Exception as e:
            logger.error(f"生成双向交易信号失败: {e}")
            return df

    def simulate_bidirectional_strategy(self, data: pd.DataFrame) -> dict:
        """模拟双向交易策略"""
        try:
            print("🔄 模拟P06双向交易策略...")

            # 计算技术指标
            data = self.calculate_bidirectional_indicators(data)
            data = self.generate_bidirectional_signals(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            long_position = 0      # 多头仓位
            short_position = 0     # 空头仓位
            trades = []
            equity_curve = [initial_capital]

            # 交易控制变量
            last_trade_time = None
            long_entry_time = None
            short_entry_time = None
            long_entry_price = 0
            short_entry_price = 0

            for i in range(50, len(data)):  # 从第50个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']

                # 计算当前权益 (多头盈亏 + 空头盈亏)
                long_value = long_position * current_price if long_position > 0 else 0
                short_value = short_position * (2 * short_entry_price - current_price) if short_position > 0 else 0
                current_equity = cash + long_value + short_value
                equity_curve.append(current_equity)

                # 检查数据有效性
                if pd.isna(current_price):
                    continue

                # 检查信号间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_signal_gap']:
                        continue

                # 获取信号
                long_signal = current_data.get('Long_Signal', 0)
                short_signal = current_data.get('Short_Signal', 0)
                signal_strength = current_data.get('Signal_Strength', 0)

                # 检查做多信号
                if long_signal == 1 and signal_strength > 0.5:
                    # 如果有空头仓位且允许强制平仓
                    if short_position > 0 and self.strategy_params['force_close_opposite']:
                        # 平空头仓位
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': '反向信号平仓'
                        })

                        short_position = 0
                        short_entry_time = None

                    # 开多头仓位
                    if long_position == 0:
                        position_value = cash * self.strategy_params['position_size']
                        shares = position_value / current_price

                        if shares > 0:
                            long_position = shares
                            cash -= shares * current_price * 1.0005  # 手续费
                            long_entry_time = current_time
                            long_entry_price = current_price

                            trades.append({
                                'time': current_time,
                                'action': 'buy_long',
                                'price': current_price,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'stop_loss': current_price * (1 - self.strategy_params['stop_loss_long']),
                                'take_profit': current_price * (1 + self.strategy_params['take_profit_long'])
                            })

                            last_trade_time = current_time

                # 检查做空信号
                elif short_signal == 1 and signal_strength > 0.5:
                    # 如果有多头仓位且允许强制平仓
                    if long_position > 0 and self.strategy_params['force_close_opposite']:
                        # 平多头仓位
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': '反向信号平仓'
                        })

                        long_position = 0
                        long_entry_time = None

                    # 开空头仓位
                    if short_position == 0:
                        position_value = cash * self.strategy_params['position_size']
                        shares = position_value / current_price

                        if shares > 0:
                            short_position = shares
                            cash -= shares * current_price * 1.0005  # 保证金和手续费
                            short_entry_time = current_time
                            short_entry_price = current_price

                            trades.append({
                                'time': current_time,
                                'action': 'sell_short',
                                'price': current_price,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'stop_loss': current_price * (1 + self.strategy_params['stop_loss_short']),
                                'take_profit': current_price * (1 - self.strategy_params['take_profit_short'])
                            })

                            last_trade_time = current_time

                # 检查多头出场条件
                if long_position > 0:
                    should_exit_long = False
                    exit_reason = ""

                    stop_loss = long_entry_price * (1 - self.strategy_params['stop_loss_long'])
                    take_profit = long_entry_price * (1 + self.strategy_params['take_profit_long'])

                    if current_price <= stop_loss:
                        should_exit_long = True
                        exit_reason = "多头止损"
                    elif current_price >= take_profit:
                        should_exit_long = True
                        exit_reason = "多头止盈"
                    elif long_entry_time and (current_time - long_entry_time).total_seconds() / 3600 > self.strategy_params['max_holding_hours']:
                        should_exit_long = True
                        exit_reason = "多头超时"

                    if should_exit_long:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': exit_reason
                        })

                        long_position = 0
                        long_entry_time = None
                        last_trade_time = current_time

                # 检查空头出场条件
                if short_position > 0:
                    should_exit_short = False
                    exit_reason = ""

                    stop_loss = short_entry_price * (1 + self.strategy_params['stop_loss_short'])
                    take_profit = short_entry_price * (1 - self.strategy_params['take_profit_short'])

                    if current_price >= stop_loss:
                        should_exit_short = True
                        exit_reason = "空头止损"
                    elif current_price <= take_profit:
                        should_exit_short = True
                        exit_reason = "空头止盈"
                    elif short_entry_time and (current_time - short_entry_time).total_seconds() / 3600 > self.strategy_params['max_holding_hours']:
                        should_exit_short = True
                        exit_reason = "空头超时"

                    if should_exit_short:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': exit_reason
                        })

                        short_position = 0
                        short_entry_time = None
                        last_trade_time = current_time

            # 期末平仓
            final_price = data['CLOSE'].iloc[-1]

            if long_position > 0:
                long_pnl = (final_price - long_entry_price) * long_position
                cash += long_position * final_price * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell_long',
                    'price': final_price,
                    'shares': long_position,
                    'pnl': long_pnl,
                    'reason': '期末平仓'
                })
                long_position = 0

            if short_position > 0:
                short_pnl = short_position * (short_entry_price - final_price)
                cash += short_position * short_entry_price + short_pnl * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'cover_short',
                    'price': final_price,
                    'shares': short_position,
                    'pnl': short_pnl,
                    'reason': '期末平仓'
                })
                short_position = 0

            final_equity = cash

            # 计算统计指标
            long_trades = [t for t in trades if t['action'] in ['buy_long', 'sell_long']]
            short_trades = [t for t in trades if t['action'] in ['sell_short', 'cover_short']]

            total_trades = len([t for t in trades if t['action'] in ['buy_long', 'sell_short']])

            # 计算盈亏
            profitable_trades = len([t for t in trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in trades if t.get('pnl', 0) < 0])

            win_rate = profitable_trades / (profitable_trades + losing_trades) if (profitable_trades + losing_trades) > 0 else 0

            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in trades if t.get('pnl', 0) < 0]

            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital

            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            # 统计多空交易
            long_count = len([t for t in trades if t['action'] == 'buy_long'])
            short_count = len([t for t in trades if t['action'] == 'sell_short'])

            print(f"✅ P06双向交易策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   总交易次数: {total_trades} (多头:{long_count}, 空头:{short_count})")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")

            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'long_trades': long_count,
                'short_trades': short_count,
                'winning_trades': profitable_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }

        except Exception as e:
            logger.error(f"P06双向交易策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P06双向交易策略测试...")

    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return

    strategy = BidirectionalTradingStrategy()

    print("\n🎯 P06双向交易策略特点:")
    print("=" * 60)
    print("  1. 🔥 做多做空双向交易")
    print("  2. 📈 上涨趋势做多获利")
    print("  3. 📉 下跌趋势做空获利")
    print("  4. ⚖️ 可同时持有多空仓位")
    print("  5. 🛡️ 反向信号自动平仓")
    print("=" * 60)

    # 测试2024年4月
    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_bidirectional_strategy(data)

        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1

            print(f"\n📊 P06双向交易策略测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"总交易次数: {result['total_trades']}")
            print(f"多头交易: {result['long_trades']}次")
            print(f"空头交易: {result['short_trades']}次")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")

            # 双向交易优势分析
            print(f"\n🎯 双向交易优势分析:")
            print("-" * 40)
            has_both_directions = result['long_trades'] > 0 and result['short_trades'] > 0
            is_profitable = result['total_return'] > 0
            good_winrate = result['win_rate'] >= 0.45
            reasonable_drawdown = result['max_drawdown'] <= 0.20

            print(f"双向交易能力: {'✅' if has_both_directions else '❌'} (多:{result['long_trades']}, 空:{result['short_trades']})")
            print(f"整体盈利: {result['total_return']*100:+.2f}% {'✅' if is_profitable else '❌'}")
            print(f"胜率表现: {result['win_rate']*100:.1f}% {'✅' if good_winrate else '❌'}")
            print(f"回撤控制: {result['max_drawdown']*100:.2f}% {'✅' if reasonable_drawdown else '❌'}")

            targets_met = sum([has_both_directions, is_profitable, good_winrate, reasonable_drawdown])

            print(f"\n🏆 P06双向交易策略评价:")
            if targets_met >= 3:
                print("🎉 双向交易成功! 策略具备机构级交易能力!")
            elif targets_met == 2:
                print("✅ 双向交易有效! 策略显著改进!")
            elif targets_met == 1:
                print("⚠️ 部分改进! 双向交易有潜力!")
            else:
                print("❌ 需要优化! 双向交易逻辑需调整!")

            # 与单向策略对比
            print(f"\n📈 与单向策略对比:")
            print(f"  传统单向策略: 只能在上涨时盈利")
            print(f"  双向交易策略: 上涨做多 + 下跌做空")
            print(f"  理论优势: 任何市场环境都有盈利机会")
            print(f"  实际表现: {result['total_return']*100:+.2f}% (本月)")

    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()