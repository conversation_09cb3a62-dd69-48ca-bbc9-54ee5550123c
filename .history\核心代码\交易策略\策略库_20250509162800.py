# -*- coding: utf-8 -*-
# 在 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass, field
import importlib

# 检测backtesting库是否可用
try:
    import backtesting
    backtesting_available = True
except ImportError:
    backtesting_available = False

# --- 保持 BacktestResult 和 validate_input ---
@dataclass
class BacktestResult: # ... (保持不变) ...
    pass
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool: # ... (保持不变) ...
    pass

logger = logging.getLogger(__name__)

# --- 修改后的策略基类 ---
class TradingStrategy:
    """
    交易策略基类 (适配模拟事件驱动回测和backtesting)
    """
    parameters: Dict[str, Any] = field(default_factory=dict)
    stop_loss_pct = 0.03  # 默认值
    take_profit_pct = 0.08 # 默认值

    def __init__(self, engine: Any = None, symbol_list: List[str] = None, params: Optional[Dict] = None,
                 broker=None, data=None):
        """
        初始化策略实例。

        Args:
            engine: 回测/交易引擎实例，用于访问数据、下单等（事件驱动模式）。
            symbol_list: 此策略实例负责的标的列表 (可以只有一个)。
            params: 策略参数。
            broker: backtesting模式使用的broker实例。
            data: backtesting模式使用的数据。
        """
        # --- 参数处理逻辑 ---
        # --- 参数处理逻辑 ---
        # 传入的 params 优先
        final_params = {}
        
        # 1. 先用类定义的属性作为基础默认值
        cls = self.__class__
        potential_class_params = [p for p in dir(cls) if not p.startswith('_') and not callable(getattr(cls, p)) and isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_class_params:
            final_params[param_name] = getattr(cls, param_name)
            
        # 2. 如果外部传入了 params (构造函数参数)，则用它们覆盖/添加值
        if params: # params 是构造函数接收的字典
            for key, value in params.items():
                final_params[key] = value # 传入的参数会覆盖类定义的默认值
                
        self.parameters = final_params # self.parameters 现在是合并后的结果
        
        # 将参数设为实例属性 (方便访问)
        # 确保在设置属性前 self.parameters 是最终版本
        for key, value in self.parameters.items(): 
            setattr(self, key, value)

        # --- 初始化模式处理 ---
        if backtesting_available and broker is not None and data is not None:
            # Backtesting模式
            super().__init__(broker, data, self.parameters)
            logger.info(f"策略 {self.__class__.__name__} 以backtesting模式初始化 with data: {data.shape}")
        elif engine is not None and symbol_list is not None:
            # 事件驱动模式
            self.engine = engine
            self.symbol_list = symbol_list
            logger.info(f"策略 {self.__class__.__name__} 以事件驱动模式初始化 for symbols: {symbol_list}, Params: {self.parameters}")
        else:
            raise ValueError("必须提供 engine/symbol_list（事件驱动模式）或 broker/data（backtesting模式）")

        self.on_init() # 调用子类的初始化逻辑

    def on_init(self):
        """子类可以覆盖此方法进行初始化，例如加载历史数据或设置内部状态"""
        pass

    def on_bar(self, current_bar_data: pd.DataFrame):
        """
        处理新的分钟线 Bar 数据。
        这是事件驱动策略的核心逻辑入口。

        Args:
            current_bar_data (pd.DataFrame): 当前时间点所有 relevant 标的的最新 Bar 数据 (MultiIndex: Symbol, Datetime)。
                                             通常只包含最新的一个时间点。需要包含 OHLCV 和预计算的因子列。
        """
        raise NotImplementedError("子类必须实现 on_bar 方法")

    def buy(self, symbol: str, price: float, volume: float, sl_pct: Optional[float]=None, tp_pct: Optional[float]=None):
        """通过引擎发送买入订单"""
        if not hasattr(self, 'engine'):
            raise RuntimeError("buy() 方法只能在事件驱动模式下使用")
        stop_loss = price * (1 - (sl_pct or self.stop_loss_pct)) if sl_pct or self.stop_loss_pct else None
        take_profit = price * (1 + (tp_pct or self.take_profit_pct)) if tp_pct or self.take_profit_pct else None
        self.engine.send_order(symbol, 'buy', price, volume, stop_loss=stop_loss, take_profit=take_profit)

    def sell(self, symbol: str, price: float, volume: float, sl_pct: Optional[float]=None, tp_pct: Optional[float]=None):
        """通过引擎发送卖出订单 (平多或开空)"""
        if not hasattr(self, 'engine'):
            raise RuntimeError("sell() 方法只能在事件驱动模式下使用")
        # 注意：空头止损止盈方向相反
        stop_loss = price * (1 + (sl_pct or self.stop_loss_pct)) if sl_pct or self.stop_loss_pct else None
        take_profit = price * (1 - (tp_pct or self.take_profit_pct)) if tp_pct or self.take_profit_pct else None
        self.engine.send_order(symbol, 'sell', price, volume, stop_loss=stop_loss, take_profit=take_profit)

    def close_position(self, symbol: str, price: float):
        """通过引擎发送平仓订单"""
        if not hasattr(self, 'engine'):
            raise RuntimeError("close_position() 方法只能在事件驱动模式下使用")
        current_pos = self.engine.get_position_size(symbol)
        if abs(current_pos) > 1e-9:
            action = 'buy' if current_pos < 0 else 'sell'
            self.engine.send_order(symbol, action, price, abs(current_pos))

    def get_current_position(self, symbol: str) -> float:
         """获取当前持仓数量"""
         if not hasattr(self, 'engine'):
            raise RuntimeError("get_current_position() 方法只能在事件驱动模式下使用")
         return self.engine.get_position_size(symbol)

    def get_available_cash(self) -> float:
        """获取可用资金"""
        if not hasattr(self, 'engine'):
            raise RuntimeError("get_available_cash() 方法只能在事件驱动模式下使用")
        return self.engine.get_available_cash()


# --- 具体策略实现 (修改为使用 on_bar) ---

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略 (适配 on_bar)"""
    # 参数定义因子列名
    bbands_prefix = 'Bollinger Bands' # 与 calculate_factors 输出匹配
    rsi_factor = 'RSI'              # 与 calculate_factors 输出匹配
    # 策略逻辑参数
    rsi_ob = 75
    rsi_os = 25
    exit_on_middle = True # 是否在中轨平仓
    min_trade_interval = 60  # 最小交易间隔时间（分钟）
    min_volume = 1000  # 最小交易量（手）

    def on_init(self):
        # 从 self.parameters 获取参数 (这些已经合并了类定义和传入的params)
        self.bbands_prefix = self.parameters.get('bbands_prefix', 'BBands_10_1.5') # Default from user feedback
        self.rsi_factor = self.parameters.get('rsi_factor', 'RSI_7')       # Default from user feedback
        
        self.rsi_ob = self.parameters.get('rsi_ob', 68)
        self.rsi_os = self.parameters.get('rsi_os', 32)
        
        self.stop_loss_pct = self.parameters.get('stop_loss_pct', 0.015)
        self.take_profit_pct = self.parameters.get('take_profit_pct', 0.03)
        self.exit_on_middle = self.parameters.get('exit_on_middle', True)
        self.min_volume = self.parameters.get('min_volume', 100)
        
        # 交易间隔从分钟数转换为timedelta对象
        min_trade_interval_minutes = self.parameters.get('min_trade_interval', 5)
        self.min_trade_interval = pd.Timedelta(minutes=min_trade_interval_minutes)
        
        # 预加载列名 (与 calculate_factors 输出匹配)
        self.bb_lower_col = f"{self.bbands_prefix}_LowerBand"
        self.bb_upper_col = f"{self.bbands_prefix}_UpperBand"
        self.bb_middle_col = f"{self.bbands_prefix}_MiddleBand"
        
        self.required_cols = ['CLOSE', self.bb_lower_col, self.bb_upper_col, self.rsi_factor]
        if self.exit_on_middle:
            self.required_cols.append(self.bb_middle_col)
        
        # 确保所有必需列唯一
        self.required_cols = sorted(list(set(self.required_cols)))
            
        logger.info(f"{self.__class__.__name__}: 初始化完成。参数: {self.parameters}")
        logger.info(f"{self.__class__.__name__}: 策略将查找的必需列: {self.required_cols}")

    # Removed _calculate_dynamic_bbands_period and _update_dynamic_rsi_thresholds as dynamic rules are simplified for now

    def on_bar(self, current_bar_data: pd.DataFrame):
        """处理每个新的分钟线 Bar"""
        # current_bar_data 的 index 是 MultiIndex (Symbol, Datetime)
        # 假设它只包含当前时间点的数据

        # 检查所需列是否存在于传入的数据中 (只检查一次或在 init 中检查)
        if not hasattr(self, '_checked_cols'):
             if not all(c in current_bar_data.columns for c in self.required_cols):
                  logger.error(f"{self.__class__.__name__} on_bar 缺少必需列: {self.required_cols}. 可用: {current_bar_data.columns.tolist()}")
                  self._checked_cols = False # 标记检查失败
