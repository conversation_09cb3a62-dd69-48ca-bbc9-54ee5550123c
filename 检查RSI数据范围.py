# -*- coding: utf-8 -*-
"""
检查RSI数据范围，了解实际的超卖超买水平
"""
import pandas as pd
import numpy as np
import os

def check_rsi_data_range():
    """检查RSI数据范围"""
    
    print("=== 检查RSI数据范围 ===")
    
    # 加载数据
    data_path = r"D:\市场数据\现金\BTCUSDT\BTCUSDT-1m-2024-04.csv"
    
    print(f"正在检查数据文件: {data_path}")
    
    # 读取数据
    df = pd.read_csv(data_path)
    print(f"数据形状: {df.shape}")
    
    # 查找RSI列
    rsi_columns = [col for col in df.columns if 'RSI' in col]
    print(f"\nRSI相关列: {rsi_columns}")
    
    if rsi_columns:
        rsi_col = rsi_columns[0]  # 使用第一个RSI列
        rsi_data = df[rsi_col].dropna()
        
        print(f"\n{rsi_col} 数据统计:")
        print(f"  总数据点: {len(rsi_data)}")
        print(f"  最小值: {rsi_data.min():.2f}")
        print(f"  最大值: {rsi_data.max():.2f}")
        print(f"  平均值: {rsi_data.mean():.2f}")
        print(f"  中位数: {rsi_data.median():.2f}")
        print(f"  标准差: {rsi_data.std():.2f}")
        
        # 分位数分析
        percentiles = [5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 95]
        print(f"\n分位数分析:")
        for p in percentiles:
            value = np.percentile(rsi_data, p)
            print(f"  {p}%分位数: {value:.2f}")
        
        # 超卖超买分析
        print(f"\n超卖超买分析:")
        oversold_30 = (rsi_data <= 30).sum()
        oversold_25 = (rsi_data <= 25).sum()
        oversold_20 = (rsi_data <= 20).sum()
        overbought_70 = (rsi_data >= 70).sum()
        overbought_75 = (rsi_data >= 75).sum()
        overbought_80 = (rsi_data >= 80).sum()
        
        total_points = len(rsi_data)
        print(f"  RSI ≤ 30: {oversold_30} 次 ({oversold_30/total_points*100:.2f}%)")
        print(f"  RSI ≤ 25: {oversold_25} 次 ({oversold_25/total_points*100:.2f}%)")
        print(f"  RSI ≤ 20: {oversold_20} 次 ({oversold_20/total_points*100:.2f}%)")
        print(f"  RSI ≥ 70: {overbought_70} 次 ({overbought_70/total_points*100:.2f}%)")
        print(f"  RSI ≥ 75: {overbought_75} 次 ({overbought_75/total_points*100:.2f}%)")
        print(f"  RSI ≥ 80: {overbought_80} 次 ({overbought_80/total_points*100:.2f}%)")
        
        # 建议的RSI阈值
        print(f"\n建议的RSI阈值:")
        if oversold_30 > 0:
            print(f"  ✅ RSI ≤ 30 可用 ({oversold_30} 次机会)")
        if oversold_25 > 0:
            print(f"  ✅ RSI ≤ 25 可用 ({oversold_25} 次机会)")
        if oversold_20 > 0:
            print(f"  ✅ RSI ≤ 20 可用 ({oversold_20} 次机会)")
        else:
            print(f"  ❌ RSI ≤ 20 太严格，没有机会")
        
        # 检查价格和SMA关系
        print(f"\n检查价格和SMA关系:")
        if 'CLOSE' in df.columns and 'SMA_20' in df.columns:
            close_data = df['CLOSE']
            sma_data = df['SMA_20']
            
            # 计算价格偏离
            valid_data = df[['CLOSE', 'SMA_20']].dropna()
            if len(valid_data) > 0:
                deviation = (valid_data['SMA_20'] - valid_data['CLOSE']) / valid_data['SMA_20']
                
                print(f"  价格偏离SMA_20统计:")
                print(f"    最大偏离: {deviation.max()*100:.2f}%")
                print(f"    最小偏离: {deviation.min()*100:.2f}%")
                print(f"    平均偏离: {deviation.mean()*100:.2f}%")
                
                # 偏离度分析
                dev_1pct = (deviation >= 0.01).sum()
                dev_15pct = (deviation >= 0.015).sum()
                dev_2pct = (deviation >= 0.02).sum()
                dev_25pct = (deviation >= 0.025).sum()
                
                total = len(deviation)
                print(f"  价格偏离分析:")
                print(f"    偏离 ≥ 1.0%: {dev_1pct} 次 ({dev_1pct/total*100:.2f}%)")
                print(f"    偏离 ≥ 1.5%: {dev_15pct} 次 ({dev_15pct/total*100:.2f}%)")
                print(f"    偏离 ≥ 2.0%: {dev_2pct} 次 ({dev_2pct/total*100:.2f}%)")
                print(f"    偏离 ≥ 2.5%: {dev_25pct} 次 ({dev_25pct/total*100:.2f}%)")
        
        return {
            'rsi_min': rsi_data.min(),
            'rsi_max': rsi_data.max(),
            'rsi_mean': rsi_data.mean(),
            'oversold_30_count': oversold_30,
            'oversold_25_count': oversold_25,
            'oversold_20_count': oversold_20
        }
    else:
        print("❌ 没有找到RSI数据")
        return None

def suggest_optimal_parameters():
    """建议最优参数"""
    
    print(f"\n=== 建议最优参数 ===")
    
    suggestions = [
        "基于数据分析的参数建议：",
        "",
        "1. RSI阈值建议：",
        "   • 如果RSI≤30有足够机会，使用30",
        "   • 如果RSI≤35有更多机会，使用35",
        "   • 避免使用过于严格的阈值（如20）",
        "",
        "2. 价格偏离建议：",
        "   • 如果1.0%偏离有足够机会，使用1.0%",
        "   • 如果1.5%偏离有适中机会，使用1.5%",
        "   • 避免使用过于严格的偏离度（如2.5%）",
        "",
        "3. 策略优化方向：",
        "   • 使用实际数据中常见的RSI和偏离度水平",
        "   • 平衡信号频率和质量",
        "   • 考虑市场环境的实际特征"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

if __name__ == '__main__':
    print("检查RSI数据范围")
    print("=" * 50)
    
    # 检查RSI数据
    rsi_stats = check_rsi_data_range()
    
    # 建议参数
    suggest_optimal_parameters()
    
    print("\n" + "=" * 50)
    if rsi_stats:
        print("✅ 数据分析完成，请根据实际情况调整策略参数")
    else:
        print("❌ 数据分析失败，请检查数据文件")
    
    print(f"\n📄 分析完成时间：{pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
