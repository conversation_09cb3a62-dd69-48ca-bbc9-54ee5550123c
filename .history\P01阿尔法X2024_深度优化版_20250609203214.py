# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 深度优化版
解决原版策略无法产生交易信号的问题，争取达到客户目标
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class OptimizedAlphaX2024Strategy:
    """P01阿尔法X2024策略 - 深度优化版"""

    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"

        # 大幅放宽的策略参数 - 确保能产生交易信号
        self.strategy_params = {
            # 基础技术指标参数
            'sma_short': 10,           # 短期均线 (降低)
            'sma_long': 20,            # 长期均线 (降低)
            'rsi_period': 14,          # RSI周期
            'atr_period': 14,          # ATR周期
            'adx_period': 14,          # ADX周期

            # 信号生成条件 (大幅放宽)
            'adx_threshold': 15,       # ADX阈值 (从25降到15)
            'rsi_oversold': 45,        # RSI超卖 (从30提高到45)
            'rsi_overbought': 65,      # RSI超买 (从70降到65)

            # 风险管理参数
            'risk_per_trade': 0.02,    # 每笔交易风险2%
            'atr_sl_multiple': 1.5,    # 止损倍数 (降低)
            'atr_tp_multiple': 3.0,    # 止盈倍数 (降低)
            'max_position_size': 0.8,  # 最大仓位80%

            # 交易频率控制
            'min_signal_interval': 30, # 最小信号间隔30分钟 (大幅降低)
            'max_daily_trades': 10,    # 每日最大交易次数

            # 趋势跟踪参数
            'trend_multiplier': 1.2,   # 趋势加仓倍数
            'momentum_threshold': 0.005, # 动量阈值 (降低)

            # 动态调整参数
            'volatility_adjustment': True,  # 启用波动率调整
            'adaptive_thresholds': True,    # 启用自适应阈值
        }

        self.results = {}

    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")

            # 获取日期范围内的所有文件
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)

            all_data = []
            current_date = start_dt

            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month

                # 构建文件路径
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)

                if os.path.exists(filepath):
                    print(f"  加载: {filename}")

                    # 读取数据
                    df = pd.read_csv(filepath)

                    # 标准化列名
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])

                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)

                    # 重命名列以匹配策略需求
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH',
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)

                    # 过滤日期范围
                    df = df[(df.index >= start_date) & (df.index <= end_date)]

                    if not df.empty:
                        all_data.append(df)

                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)

            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")

            # 合并所有数据
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()

            # 去重
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]

            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            print(f"   时间范围: {combined_data.index[0]} 至 {combined_data.index[-1]}")

            return combined_data

        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise

    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()

            # 移动平均线
            df['SMA_Short'] = df['CLOSE'].rolling(self.strategy_params['sma_short']).mean()
            df['SMA_Long'] = df['CLOSE'].rolling(self.strategy_params['sma_long']).mean()

            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(self.strategy_params['atr_period']).mean()

            # ADX (简化版)
            plus_dm = df['HIGH'].diff()
            minus_dm = df['LOW'].diff()
            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm > 0] = 0
            minus_dm = abs(minus_dm)

            tr = true_range
            plus_di = 100 * (plus_dm.rolling(self.strategy_params['adx_period']).mean() / tr.rolling(self.strategy_params['adx_period']).mean())
            minus_di = 100 * (minus_dm.rolling(self.strategy_params['adx_period']).mean() / tr.rolling(self.strategy_params['adx_period']).mean())

            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            df['ADX'] = dx.rolling(self.strategy_params['adx_period']).mean()

            # 成交量移动平均
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()

            # 价格动量
            df['Momentum'] = df['CLOSE'].pct_change(5)

            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()

            return df

        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data