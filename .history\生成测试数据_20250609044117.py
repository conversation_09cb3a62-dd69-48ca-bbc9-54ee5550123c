# -*- coding: utf-8 -*-
"""
生成测试数据用于回测引擎
"""
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta

def generate_minute_data(symbol='BTCUSDT', start_date='2023-01-01', end_date='2023-03-31'):
    """生成分钟级测试数据"""
    # 生成分钟级时间序列
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    dates = pd.date_range(start=start, end=end, freq='min')
    
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)
    
    # 生成价格数据 (模拟BTC价格走势)
    n_points = len(dates)
    
    # 基础价格趋势
    base_price = 20000  # BTC基础价格
    trend = np.linspace(0, 5000, n_points)  # 上升趋势
    
    # 添加随机波动
    noise = np.random.normal(0, 100, n_points)
    
    # 添加周期性波动
    cycle = 500 * np.sin(np.linspace(0, 20*np.pi, n_points))
    
    # 合成收盘价
    close_prices = base_price + trend + noise + cycle
    
    # 生成其他价格数据
    # 开盘价：基于前一个收盘价加小幅随机变动
    open_prices = np.zeros_like(close_prices)
    open_prices[0] = close_prices[0]
    for i in range(1, len(close_prices)):
        open_prices[i] = close_prices[i-1] + np.random.normal(0, 10)
    
    # 高价和低价
    high_low_spread = np.random.uniform(10, 100, n_points)
    high_prices = np.maximum(open_prices, close_prices) + high_low_spread/2
    low_prices = np.minimum(open_prices, close_prices) - high_low_spread/2
    
    # 成交量
    volumes = np.random.uniform(100, 1000, n_points)
    
    # 创建DataFrame，添加时间戳毫秒列
    timestamps_ms = [int(dt.timestamp() * 1000) for dt in dates]

    df = pd.DataFrame({
        'Timestamp_ms': timestamps_ms,
        'Open': open_prices,
        'High': high_prices,
        'Low': low_prices,
        'Close': close_prices,
        'Volume': volumes
    }, index=dates)

    return df

def save_monthly_data(symbol='BTCUSDT', start_date='2023-01-01', end_date='2023-03-31', output_dir='数据/BTCUSDT'):
    """按月保存数据文件"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成完整数据
    full_data = generate_minute_data(symbol, start_date, end_date)
    
    # 按月分割数据
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    
    current_date = start.replace(day=1)  # 月初
    
    while current_date <= end:
        # 计算当月的结束日期
        if current_date.month == 12:
            next_month = current_date.replace(year=current_date.year + 1, month=1)
        else:
            next_month = current_date.replace(month=current_date.month + 1)
        
        month_end = next_month - timedelta(days=1)
        
        # 提取当月数据
        month_data = full_data[current_date:month_end]
        
        if not month_data.empty:
            # 生成文件名
            filename = f"{symbol}-1m-{current_date.strftime('%Y-%m')}.csv"
            filepath = os.path.join(output_dir, filename)
            
            # 保存数据，不包含索引和表头，只保存数据列
            month_data.to_csv(filepath, header=False, index=False)
            print(f"已保存: {filepath} ({len(month_data)} 条记录)")
        
        # 移动到下一个月
        current_date = next_month

if __name__ == '__main__':
    print("开始生成BTCUSDT测试数据...")

    # 生成BTCUSDT的测试数据，时间范围到2025-04-30之前
    save_monthly_data('BTCUSDT', '2025-01-01', '2025-04-29')

    print("BTCUSDT测试数据生成完成！")
