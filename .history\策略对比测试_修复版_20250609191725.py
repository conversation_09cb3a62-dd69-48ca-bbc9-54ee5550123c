# -*- coding: utf-8 -*-
"""
策略对比测试 - 修复版
使用更宽松的参数，确保策略能够产生交易信号
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import json
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s]: %(message)s'
)

logger = logging.getLogger(__name__)

def get_relaxed_strategy_params(strategy_name):
    """获取更宽松的策略参数，确保能产生交易信号"""
    
    # 基础参数（更宽松）
    base_params = {
        'risk_per_trade_pct': 0.02,  # 增加风险比例
        'atr_sl_multiple': 1.5,      # 减少止损倍数
        'atr_tp_multiple': 3.0,      # 减少止盈倍数
        'min_signal_interval_minutes': 60  # 减少信号间隔
    }
    
    # 策略特定参数（更宽松的条件）
    strategy_specific_params = {
        'MeanReversion': {
            'rsi_oversold': 40,      # 放宽RSI条件
            'rsi_overbought': 60,
            'bb_period': 15,         # 缩短布林带周期
            'bb_std': 1.5           # 减少标准差倍数
        },
        'TrendFollowing': {
            'sma_short': 10,         # 缩短均线周期
            'sma_long': 30,
            'rsi_threshold': 45      # 放宽RSI阈值
        },
        'AlphaXInspiredStrategy': {
            'adx_threshold': 20.0,   # 降低ADX阈值
            'rsi_oversold': 45.0     # 放宽RSI超卖条件
        },
        'EnhancedAlphaXStrategyV3': {
            'trend_multiplier': 1.3,
            'batch_count': 2,        # 减少分批次数
            'batch_interval_minutes': 20,
            'max_position_pct': 0.04,
            'min_sl_multiple': 1.2,
            'max_sl_multiple': 2.5,
            'trend_strength_threshold': 25.0,  # 降低趋势强度阈值
            'adx_threshold': 20.0,
            'rsi_oversold': 45.0
        },
        'OptimizedAlphaXStrategy': {
            'adx_threshold': 20.0,
            'rsi_oversold': 40.0
        },
        'EnhancedMeanReversionStrategy': {
            'rsi_oversold': 35,      # 放宽条件
            'rsi_overbought': 65,
            'bb_period': 15,
            'volatility_threshold': 0.015
        },
        'AggressiveMeanReversionStrategy': {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'bb_std': 2.0
        },
        'ProfitableMeanReversionStrategy': {
            'rsi_oversold': 35,
            'bb_period': 12,
            'profit_target': 0.015
        },
        'SimplifiedAIAlphaXStrategy': {
            'score_threshold': 0.5,  # 降低评分阈值
            'adx_threshold': 15.0,
            'rsi_oversold': 45.0,
            'rsi_overbought': 55.0
        }
    }
    
    # 合并参数
    params = base_params.copy()
    if strategy_name in strategy_specific_params:
        params.update(strategy_specific_params[strategy_name])
    
    return params

def run_single_strategy_test(strategy_name, strategy_class):
    """运行单个策略测试"""
    
    logger.info(f"开始测试策略: {strategy_name}")
    
    try:
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config
        
        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-15'  # 缩短测试期间以便快速验证
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 获取宽松的策略参数
        strategy_params = get_relaxed_strategy_params(strategy_name)
        
        logger.info(f"{strategy_name}: 使用宽松参数进行测试")
        
        start_time = time.time()
        
        # 创建回测引擎
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
        
        # 运行回测
        results = backtester.run_backtest(config.start_date, config.end_date)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        if results and isinstance(results, dict):
            # 从回测结果字典获取信息
            initial_value = config.initial_cash
            final_value = results.get('最终权益', initial_value)
            total_return = (final_value - initial_value) / initial_value * 100

            # 从结果字典获取交易统计
            total_trades = results.get('总交易次数', 0)
            winning_trades = results.get('盈利次数', 0)
            losing_trades = results.get('亏损次数', 0)
            win_rate = results.get('胜率', 0.0)
            sharpe_ratio = results.get('夏普比率', 0.0)
            max_drawdown = results.get('最大回撤率', 0.0)

            strategy_result = {
                'strategy_name': strategy_name,
                'initial_cash': initial_value,
                'final_value': final_value,
                'total_return_pct': total_return,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate * 100 if win_rate <= 1 else win_rate,  # 确保胜率是百分比
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown * 100 if max_drawdown <= 1 else max_drawdown,  # 确保回撤是百分比
                'execution_time': execution_time,
                'status': 'success',
                'error': None,
                'parameters': strategy_params
            }

            logger.info(f"{strategy_name}: 测试完成 - 收益率: {total_return:.2f}%, 交易次数: {total_trades}")

        else:
            # 如果没有有效结果，返回默认值
            strategy_result = {
                'strategy_name': strategy_name,
                'initial_cash': config.initial_cash,
                'final_value': config.initial_cash,
                'total_return_pct': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'execution_time': execution_time,
                'status': 'success',
                'error': 'No valid results returned',
                'parameters': strategy_params
            }

            logger.warning(f"{strategy_name}: 未返回有效结果")
        
        return strategy_result
        
    except Exception as e:
        logger.error(f"{strategy_name}: 测试出错 - {e}")
        return {
            'strategy_name': strategy_name,
            'status': 'error',
            'error': str(e),
            'execution_time': 0,
            'parameters': get_relaxed_strategy_params(strategy_name)
        }

def run_focused_strategy_comparison():
    """运行重点策略对比测试"""
    
    print("🚀 策略对比测试 - 修复版")
    print("=" * 60)
    print("测试数据: 2025年4月前半月BTCUSDT")
    print("参数设置: 宽松模式（确保产生交易信号）")
    print("=" * 60)
    
    # 重点测试的策略
    focus_strategies = [
        'TrendFollowing',
        'AlphaXInspiredStrategy', 
        'EnhancedAlphaXStrategyV3',
        'SimplifiedAIAlphaXStrategy',
        'PracticalMeanReversionStrategy',
        'FinalProfitableStrategy'
    ]
    
    try:
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 获取策略类
        available_strategies = {}
        for strategy_name in focus_strategies:
            if strategy_name in STRATEGIES and STRATEGIES[strategy_name] is not None:
                available_strategies[strategy_name] = STRATEGIES[strategy_name]
        
        if not available_strategies:
            print("❌ 未找到可用策略")
            return
        
        print(f"📊 测试 {len(available_strategies)} 个重点策略:")
        for i, strategy_name in enumerate(available_strategies.keys(), 1):
            print(f"  {i}. {strategy_name}")
        
        print("\n⏳ 开始执行测试...")
        
        # 存储结果
        all_results = []
        successful_results = []
        
        # 逐个测试策略
        for i, (strategy_name, strategy_class) in enumerate(available_strategies.items(), 1):
            print(f"\n[{i}/{len(available_strategies)}] 测试策略: {strategy_name}")
            print("-" * 50)
            
            result = run_single_strategy_test(strategy_name, strategy_class)
            all_results.append(result)
            
            if result['status'] == 'success':
                successful_results.append(result)
                print(f"✅ 成功 - 收益率: {result.get('total_return_pct', 0):.2f}%, 交易: {result.get('total_trades', 0)}次")
            else:
                print(f"❌ 失败 - {result.get('error', '未知错误')}")
        
        # 生成报告
        generate_focused_report(all_results, successful_results)
        
        return all_results
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def generate_focused_report(all_results, successful_results):
    """生成重点策略对比报告"""
    
    print("\n" + "=" * 60)
    print("📊 重点策略对比报告")
    print("=" * 60)
    
    # 基本统计
    total_strategies = len(all_results)
    successful_strategies = len(successful_results)
    
    print(f"测试策略数量: {total_strategies}")
    print(f"成功执行: {successful_strategies}")
    print(f"成功率: {successful_strategies/total_strategies*100:.1f}%")
    
    if not successful_results:
        print("\n❌ 没有成功的策略结果")
        return
    
    # 按收益率排序
    successful_results.sort(key=lambda x: x.get('total_return_pct', 0), reverse=True)
    
    print(f"\n🏆 策略性能排行榜:")
    print("-" * 60)
    print(f"{'排名':<4} {'策略名称':<25} {'收益率':<10} {'交易次数':<8}")
    print("-" * 60)
    
    for i, result in enumerate(successful_results, 1):
        return_pct = result.get('total_return_pct', 0)
        trades = result.get('total_trades', 0)
        print(f"{i:<4} {result['strategy_name']:<25} {return_pct:>7.2f}% {trades:>6}")
    
    # 最佳策略详细信息
    if successful_results:
        best_strategy = successful_results[0]
        print(f"\n🥇 最佳策略: {best_strategy['strategy_name']}")
        print("-" * 30)
        print(f"总收益率: {best_strategy.get('total_return_pct', 0):.2f}%")
        print(f"最终价值: {best_strategy.get('final_value', 0):,.2f}")
        print(f"总交易次数: {best_strategy.get('total_trades', 0)}")
        print(f"胜率: {best_strategy.get('win_rate', 0):.1f}%")
        print(f"执行时间: {best_strategy.get('execution_time', 0):.2f}秒")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"重点策略对比结果_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 详细结果已保存: {results_file}")

if __name__ == "__main__":
    try:
        results = run_focused_strategy_comparison()
        print("\n🎉 重点策略对比测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
