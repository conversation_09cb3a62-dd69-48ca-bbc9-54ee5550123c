# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

# ==============================================================================
# --- 单一技术指标计算函数 ---
# 统一使用 float 类型，由 pandas/numpy 进行高性能计算
# ==============================================================================

def calculate_sma(prices: pd.Series, window: int) -> Optional[pd.Series]:
    if not isinstance(prices, pd.Series) or prices.empty or window <= 0 or len(prices.dropna()) < window:
        return pd.Series(np.nan, index=prices.index)
    return prices.rolling(window=window, min_periods=window).mean()

def calculate_ema(prices: pd.Series, window: int) -> Optional[pd.Series]:
    if not isinstance(prices, pd.Series) or prices.empty or window <= 0 or len(prices.dropna()) < window:
        return pd.Series(np.nan, index=prices.index)
    return prices.ewm(span=window, adjust=False, min_periods=window).mean()

def calculate_rsi(prices: pd.Series, window: int = 14) -> Optional[pd.Series]:
    if not isinstance(prices, pd.Series) or prices.empty or window <= 0 or len(prices.dropna()) <= window:
        return pd.Series(np.nan, index=prices.index)
    delta = prices.diff()
    gain = delta.where(delta > 0, 0.0)
    loss = -delta.where(delta < 0, 0.0)
    avg_gain = gain.ewm(com=window - 1, min_periods=window).mean()
    avg_loss = loss.ewm(com=window - 1, min_periods=window).mean()
    rs = avg_gain / avg_loss.replace(0, 1e-10)
    rsi = 100.0 - (100.0 / (1.0 + rs))
    return rsi

def calculate_bollinger_bands(prices: pd.Series, window: int = 20, num_std_dev: float = 2.0) -> Optional[pd.DataFrame]:
    if not isinstance(prices, pd.Series) or prices.empty or window <= 0 or len(prices.dropna()) < window:
        return None
    sma = calculate_sma(prices, window)
    std_dev = prices.rolling(window=window, min_periods=window).std()
    if sma is None or std_dev is None: return None
    upper_band = sma + (std_dev * num_std_dev)
    lower_band = sma - (std_dev * num_std_dev)
    return pd.DataFrame({'BB_Middle': sma, 'BB_Upper': upper_band, 'BB_Lower': lower_band}, index=prices.index)

def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> Optional[pd.Series]:
    if not all(isinstance(s, pd.Series) for s in [high, low, close]) or not (len(high) == len(low) == len(close)):
        return None
    if high.empty or window <= 0 or len(close.dropna()) <= window:
        return pd.Series(np.nan, index=close.index)
    high_low = high - low
    high_close_prev = abs(high - close.shift(1))
    low_close_prev = abs(low - close.shift(1))
    tr = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1, skipna=False)
    atr = tr.ewm(alpha=1/window, adjust=False, min_periods=window).mean()
    return atr

def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> Optional[pd.Series]:
    if not all(isinstance(s, pd.Series) for s in [high, low, close]) or not (len(high) == len(low) == len(close)):
        return None
    if high.empty or window <= 0 or len(close.dropna()) <= window * 2: # ADX needs more data
        return pd.Series(np.nan, index=close.index)

    up_move = high.diff()
    down_move = -low.diff()
    plus_dm = pd.Series(np.where((up_move > down_move) & (up_move > 0), up_move, 0), index=high.index)
    minus_dm = pd.Series(np.where((down_move > up_move) & (down_move > 0), down_move, 0), index=low.index)
    
    tr = pd.concat([high - low, abs(high - close.shift(1)), abs(low - close.shift(1))], axis=1).max(axis=1)
    
    atr = tr.ewm(alpha=1/window, adjust=False, min_periods=window).mean()
    plus_di = 100 * (plus_dm.ewm(alpha=1/window, adjust=False).mean() / atr.replace(0, np.nan))
    minus_di = 100 * (minus_dm.ewm(alpha=1/window, adjust=False).mean() / atr.replace(0, np.nan))
    
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di).replace(0, np.nan)
    adx = dx.ewm(alpha=1/window, adjust=False, min_periods=window).mean()
    return adx

def calculate_macd(prices: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Optional[pd.DataFrame]:
    """计算MACD指标"""
    if not isinstance(prices, pd.Series) or prices.empty or len(prices.dropna()) < slow_period:
        return None

    ema_fast = calculate_ema(prices, fast_period)
    ema_slow = calculate_ema(prices, slow_period)
    if ema_fast is None or ema_slow is None:
        return None

    macd_line = ema_fast - ema_slow
    signal_line = calculate_ema(macd_line, signal_period)
    histogram = macd_line - signal_line

    return pd.DataFrame({
        'MACD_Line': macd_line,
        'Signal_Line': signal_line,
        'Histogram': histogram
    }, index=prices.index)

def calculate_volatility(prices: pd.Series, window: int = 20) -> Optional[pd.Series]:
    """计算价格波动率"""
    if not isinstance(prices, pd.Series) or prices.empty or window <= 0 or len(prices.dropna()) < window:
        return pd.Series(np.nan, index=prices.index)

    returns = prices.pct_change()
    volatility = returns.rolling(window=window, min_periods=window).std() * np.sqrt(252)  # 年化波动率
    return volatility

def calculate_momentum(prices: pd.Series, window: int = 10) -> Optional[pd.Series]:
    """计算价格动量"""
    if not isinstance(prices, pd.Series) or prices.empty or window <= 0 or len(prices.dropna()) < window:
        return pd.Series(np.nan, index=prices.index)

    momentum = prices / prices.shift(window) - 1
    return momentum

def calculate_cci(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 20) -> Optional[pd.Series]:
    """计算商品通道指数(CCI)"""
    if not all(isinstance(s, pd.Series) for s in [high, low, close]) or not (len(high) == len(low) == len(close)):
        return None
    if high.empty or window <= 0 or len(close.dropna()) < window:
        return pd.Series(np.nan, index=close.index)

    typical_price = (high + low + close) / 3
    sma_tp = typical_price.rolling(window=window, min_periods=window).mean()
    mad = typical_price.rolling(window=window, min_periods=window).apply(lambda x: np.mean(np.abs(x - x.mean())), raw=True)
    cci = (typical_price - sma_tp) / (0.015 * mad)
    return cci

def calculate_donchian(high: pd.Series, low: pd.Series, window: int = 20) -> Optional[pd.DataFrame]:
    """计算唐奇安通道"""
    if not all(isinstance(s, pd.Series) for s in [high, low]) or not (len(high) == len(low)):
        return None
    if high.empty or window <= 0 or len(high.dropna()) < window:
        return None

    upper_channel = high.rolling(window=window, min_periods=window).max()
    lower_channel = low.rolling(window=window, min_periods=window).min()
    middle_channel = (upper_channel + lower_channel) / 2

    return pd.DataFrame({
        'Donchian_Upper': upper_channel,
        'Donchian_Middle': middle_channel,
        'Donchian_Lower': lower_channel
    }, index=high.index)

def calculate_keltner(high: pd.Series, low: pd.Series, close: pd.Series,
                     ema_window: int = 20, atr_window: int = 14, atr_multiplier: float = 2.0) -> Optional[pd.DataFrame]:
    """计算肯特纳通道"""
    if not all(isinstance(s, pd.Series) for s in [high, low, close]) or not (len(high) == len(low) == len(close)):
        return None
    if high.empty or ema_window <= 0 or len(close.dropna()) < max(ema_window, atr_window):
        return None

    ema = calculate_ema(close, ema_window)
    atr = calculate_atr(high, low, close, atr_window)
    if ema is None or atr is None:
        return None

    upper_channel = ema + (atr * atr_multiplier)
    lower_channel = ema - (atr * atr_multiplier)

    return pd.DataFrame({
        'Keltner_Upper': upper_channel,
        'Keltner_Middle': ema,
        'Keltner_Lower': lower_channel
    }, index=close.index)

# --- 因子计算主函数 ---

FACTOR_FUNCTIONS = {
    'SMA': calculate_sma,
    'EMA': calculate_ema,
    'RSI': calculate_rsi,
    'BBands': calculate_bollinger_bands,
    'ATR': calculate_atr,
    'ADX': calculate_adx,
    'MACD': calculate_macd,
    'Volatility': calculate_volatility,
    'Momentum': calculate_momentum,
    'CCI': calculate_cci,
    'Donchian': calculate_donchian,
    'Keltner': calculate_keltner,
}

def calculate_factors(data: pd.DataFrame, factor_configs: Dict[str, Dict[str, Any]]) -> pd.DataFrame:
    """
    一站式计算所有技术指标和机器学习特征。
    """
    if not isinstance(data, pd.DataFrame) or data.empty:
        logger.warning("calculate_factors: 输入数据为空或无效。")
        return data
    if not factor_configs:
        logger.info("calculate_factors: 未提供因子配置，返回原始数据。")
        return data

    output_df = data.copy()
    
    # --- 1. 统一列名 (健壮性) ---
    # 强制将 OHLCV 列名转为大写，以供所有因子函数使用
    rename_map = {col.lower(): col.upper() for col in ['Open', 'High', 'Low', 'Close', 'Volume']}
    output_df.rename(columns=rename_map, inplace=True)
    required_ohlcv = ['OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOLUME']
    if not all(col in output_df.columns for col in required_ohlcv):
        logger.error(f"数据缺少核心OHLCV列，无法计算因子。需要: {required_ohlcv}")
        return output_df

    # --- 2. 计算配置中的技术指标 ---
    for factor_key_name, config in factor_configs.items():
        func_name = config.get('function')
        params = config.get('params', {})
        
        if func_name not in FACTOR_FUNCTIONS:
            logger.warning(f"因子函数 '{func_name}' 未定义，跳过 '{factor_key_name}'")
            continue

        calculator = FACTOR_FUNCTIONS[func_name]
        logger.debug(f"计算因子: {factor_key_name} (函数: {func_name}, 参数: {params})")

        try:
            result = None
            if func_name in ['ATR', 'ADX']:
                result = calculator(output_df['HIGH'], output_df['LOW'], output_df['CLOSE'], **params)
            elif func_name in ['SMA', 'EMA', 'RSI', 'BBands']:
                result = calculator(output_df['CLOSE'], **params)
            
            if result is not None:
                if isinstance(result, pd.Series):
                    output_df[factor_key_name] = result
                elif isinstance(result, pd.DataFrame):
                    # 为多列输出的因子（如BBands）添加前缀
                    result.columns = [f"{factor_key_name}_{sub_col}" for sub_col in result.columns]
                    output_df = pd.concat([output_df, result], axis=1)
        except Exception as e:
            logger.error(f"计算因子 '{factor_key_name}' 时出错: {e}", exc_info=False)

    # --- 3. 计算衍生特征 (原特征工程部分) ---
    logger.debug("开始计算衍生机器学习特征...")
    
    # 价格和成交量的变化率
    for period in [1, 3, 5, 10]:
        output_df[f'CLOSE_pct_change_{period}'] = output_df['CLOSE'].pct_change(periods=period)
        output_df[f'VOLUME_pct_change_{period}'] = output_df['VOLUME'].pct_change(periods=period)

    # 价格与均线的相对位置
    for window in [20, 60]:
        sma_key = f'SMA_{window}'
        if sma_key in output_df:
            output_df[f'CLOSE_vs_{sma_key}'] = (output_df['CLOSE'] - output_df[sma_key]) / output_df[sma_key].replace(0, np.nan)

    # 均线交叉信号
    if 'SMA_20' in output_df and 'SMA_60' in output_df:
        output_df['SMA_cross_signal'] = np.sign(output_df['SMA_20'] - output_df['SMA_60']).diff().fillna(0)

    # 时间特征
    if isinstance(output_df.index, pd.DatetimeIndex):
        output_df['hour_of_day'] = output_df.index.hour
        output_df['day_of_week'] = output_df.index.dayofweek
    
    logger.debug("衍生特征计算完成。")
    return output_df

# --- 示例用法 ---
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    dates = pd.date_range(start='2023-01-01', periods=100, freq='min')
    prices_close = 100 + np.random.randn(100).cumsum()
    sample_data = pd.DataFrame({
        'open': prices_close - np.random.rand(100) * 0.1,
        'high': prices_close + np.random.rand(100) * 0.2,
        'low': prices_close - np.random.rand(100) * 0.2,
        'close': prices_close,
        'volume': np.random.randint(10000, 50000, 100).astype(float)
    }, index=dates)

    factors_to_calc = {
        'SMA_20': {'function': 'SMA', 'params': {'window': 20}},
        'SMA_60': {'function': 'SMA', 'params': {'window': 60}},
        'RSI_14': {'function': 'RSI', 'params': {'window': 14}},
        'BBands_20_2.0': {'function': 'BBands', 'params': {'window': 20, 'num_std_dev': 2.0}},
        'ATR_14': {'function': 'ATR', 'params': {'window': 14}},
        'ADX_14': {'function': 'ADX', 'params': {'window': 14}},
    }

    data_with_factors = calculate_factors(sample_data, factors_to_calc)
    
    print("\n因子和特征计算结果 (后5行):")
    print(data_with_factors.tail().to_string())
    print("\n所有列名:")
    print(data_with_factors.columns.tolist())