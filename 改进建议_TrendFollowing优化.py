# -*- coding: utf-8 -*-
"""
TrendFollowing策略改进建议
基于回测结果的具体优化方案
"""

# 1. 信号过滤优化
IMPROVED_SIGNAL_FILTERS = {
    # 增加趋势确认条件
    'adx_threshold': 30,  # 提高到30，确保真正的趋势
    'adx_rising_periods': 3,  # ADX必须连续3期上升
    
    # 价格动量确认
    'price_momentum_periods': 5,  # 5期价格动量
    'min_momentum_threshold': 0.002,  # 最小动量阈值0.2%
    
    # 成交量确认
    'volume_ma_periods': 20,
    'volume_threshold': 1.5,  # 成交量必须超过20期均值1.5倍
    
    # 波动率过滤
    'min_volatility': 0.01,  # 最小波动率1%
    'max_volatility': 0.05,  # 最大波动率5%
}

# 2. 止损止盈优化
IMPROVED_RISK_MANAGEMENT = {
    # 动态止损
    'initial_sl_atr_multiple': 2.5,  # 初始止损2.5倍ATR
    'trailing_sl_atr_multiple': 1.8,  # 跟踪止损1.8倍ATR
    'breakeven_trigger': 1.0,  # 盈利1倍ATR时移动到盈亏平衡
    
    # 分级止盈
    'tp_level_1': 2.0,  # 第一目标2倍ATR，平仓50%
    'tp_level_2': 4.0,  # 第二目标4倍ATR，平仓30%
    'tp_level_3': 6.0,  # 第三目标6倍ATR，平仓20%
    
    # 时间止损
    'max_holding_hours': 48,  # 最大持仓48小时
}

# 3. 仓位管理优化
IMPROVED_POSITION_SIZING = {
    # 基于波动率的仓位
    'base_risk_per_trade': 0.01,  # 基础风险1%
    'volatility_adjustment': True,  # 根据波动率调整
    'max_position_size': 0.15,  # 最大仓位15%
    
    # 连续亏损保护
    'max_consecutive_losses': 3,
    'loss_reduction_factor': 0.5,  # 连续亏损后减半仓位
    
    # 市场状态调整
    'trend_strength_multiplier': {
        'strong_trend': 1.5,    # 强趋势增加50%仓位
        'weak_trend': 0.7,      # 弱趋势减少30%仓位
        'sideways': 0.3,        # 震荡市减少70%仓位
    }
}

# 4. 市场环境识别
MARKET_REGIME_DETECTION = {
    # 趋势强度分级
    'adx_levels': {
        'strong_trend': 35,     # ADX>35为强趋势
        'moderate_trend': 25,   # ADX 25-35为中等趋势
        'weak_trend': 20,       # ADX 20-25为弱趋势
        'sideways': 20,         # ADX<20为震荡
    },
    
    # 波动率环境
    'volatility_percentile_lookback': 60,  # 60天波动率分位数
    'high_vol_threshold': 0.8,  # 高波动率阈值
    'low_vol_threshold': 0.2,   # 低波动率阈值
}

# 5. 具体改进策略参数
OPTIMIZED_STRATEGY_PARAMS = {
    # 技术指标参数
    'sma_short': 12,           # 短期均线12
    'sma_long': 26,            # 长期均线26
    'rsi_period': 14,
    'rsi_overbought': 75,      # 提高超买线
    'rsi_oversold': 25,        # 降低超卖线
    'atr_period': 14,
    'adx_period': 14,
    
    # 信号确认
    'signal_confirmation_bars': 2,  # 信号确认2根K线
    'min_signal_interval_minutes': 120,  # 最小信号间隔2小时
    
    # 风险管理
    'risk_per_trade_pct': 0.008,  # 降低单笔风险到0.8%
    'atr_sl_multiple': 2.5,       # 增加止损距离
    'atr_tp_multiple': 5.0,       # 提高止盈目标
    
    # 过滤条件
    'enable_volume_filter': True,
    'enable_volatility_filter': True,
    'enable_momentum_filter': True,
}

def calculate_strategy_improvements():
    """计算策略改进后的预期效果"""
    
    current_metrics = {
        'win_rate': 0.125,
        'profit_loss_ratio': 1.96,
        'total_return': -0.0387,
        'max_drawdown': 0.0513,
        'sharpe_ratio': -7.85
    }
    
    # 预期改进效果
    expected_improvements = {
        'win_rate': 0.35,          # 提高到35%
        'profit_loss_ratio': 2.5,  # 提高到2.5:1
        'total_return': 0.08,       # 目标8%收益
        'max_drawdown': 0.08,       # 控制回撤在8%以内
        'sharpe_ratio': 1.2,        # 目标夏普比率1.2
    }
    
    print("📊 策略改进预期效果:")
    print("=" * 50)
    
    for metric, current_value in current_metrics.items():
        expected_value = expected_improvements[metric]
        improvement = ((expected_value - current_value) / abs(current_value)) * 100
        
        print(f"{metric}:")
        print(f"  当前: {current_value:.3f}")
        print(f"  目标: {expected_value:.3f}")
        print(f"  改进: {improvement:+.1f}%")
        print()

# 6. 实施优先级
IMPLEMENTATION_PRIORITY = [
    {
        'priority': 1,
        'task': '增加信号过滤条件',
        'description': '添加ADX上升、成交量确认、动量过滤',
        'expected_impact': '减少假信号，提高胜率'
    },
    {
        'priority': 2,
        'task': '优化止损止盈机制',
        'description': '实施动态止损和分级止盈',
        'expected_impact': '提高盈亏比，减少过早止损'
    },
    {
        'priority': 3,
        'task': '改进仓位管理',
        'description': '基于波动率和连续亏损调整仓位',
        'expected_impact': '降低风险，提高风险调整收益'
    },
    {
        'priority': 4,
        'task': '市场环境识别',
        'description': '根据市场状态调整策略参数',
        'expected_impact': '提高策略适应性'
    }
]

if __name__ == "__main__":
    print("🔧 TrendFollowing策略改进建议")
    print("=" * 60)
    
    print("\n📋 实施优先级:")
    for item in IMPLEMENTATION_PRIORITY:
        print(f"{item['priority']}. {item['task']}")
        print(f"   描述: {item['description']}")
        print(f"   预期效果: {item['expected_impact']}")
        print()
    
    calculate_strategy_improvements()
    
    print("\n💡 关键改进要点:")
    print("1. 提高信号质量，减少假突破")
    print("2. 优化风险管理，提高盈亏比")
    print("3. 根据市场环境调整参数")
    print("4. 实施更智能的仓位管理")
