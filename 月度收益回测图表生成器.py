# -*- coding: utf-8 -*-
"""
月度收益回测图表生成器
专门为客户展示每月盈利情况的高质量图表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
from datetime import datetime, timedelta
import logging

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

# 设置seaborn样式
sns.set_style("whitegrid")
sns.set_palette("husl")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class MonthlyProfitChartGenerator:
    """月度收益图表生成器"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
    def load_data_and_simulate(self, start_date: str, end_date: str) -> dict:
        """加载数据并运行策略模拟"""
        try:
            print(f"📊 加载数据并模拟策略: {start_date} 至 {end_date}")
            
            # 这里使用我们之前的最终修复版策略
            from P01阿尔法X2024_最终修复版 import FinalAlphaX2024Strategy
            
            strategy = FinalAlphaX2024Strategy()
            data = strategy.load_historical_data(start_date, end_date)
            result = strategy.simulate_final_strategy(data)
            
            if not result:
                raise ValueError("策略模拟失败")
            
            # 处理权益曲线数据
            equity_curve = result['equity_curve']
            trades = result['trades']
            
            # 创建时间序列
            time_index = data.index[:len(equity_curve)]
            equity_df = pd.DataFrame({
                'timestamp': time_index,
                'equity': equity_curve
            })
            equity_df.set_index('timestamp', inplace=True)
            
            # 计算每日收益率
            equity_df['daily_return'] = equity_df['equity'].pct_change()
            equity_df['cumulative_return'] = (equity_df['equity'] / equity_df['equity'].iloc[0] - 1) * 100
            
            return {
                'equity_df': equity_df,
                'trades': trades,
                'result': result,
                'market_data': data
            }
            
        except Exception as e:
            logger.error(f"数据加载和模拟失败: {e}")
            raise
    
    def calculate_monthly_profits(self, equity_df: pd.DataFrame, initial_capital: float = 100000) -> pd.DataFrame:
        """计算每月收益"""
        try:
            # 按月分组
            monthly_data = equity_df.resample('M').agg({
                'equity': ['first', 'last', 'min', 'max'],
                'daily_return': ['sum', 'std', 'count']
            }).round(2)
            
            # 扁平化列名
            monthly_data.columns = ['月初资金', '月末资金', '月内最低', '月内最高', '月收益率', '月波动率', '交易天数']
            
            # 计算月度收益
            monthly_data['月收益金额'] = monthly_data['月末资金'] - monthly_data['月初资金']
            monthly_data['月收益率'] = (monthly_data['月末资金'] / monthly_data['月初资金'] - 1) * 100
            monthly_data['累计收益金额'] = monthly_data['月末资金'] - initial_capital
            monthly_data['累计收益率'] = (monthly_data['月末资金'] / initial_capital - 1) * 100
            
            # 添加月份标签
            monthly_data['月份'] = monthly_data.index.strftime('%Y年%m月')
            
            # 盈亏标记
            monthly_data['盈亏状态'] = monthly_data['月收益金额'].apply(lambda x: '盈利' if x > 0 else '亏损' if x < 0 else '持平')
            
            return monthly_data
            
        except Exception as e:
            logger.error(f"计算月度收益失败: {e}")
            raise
    
    def create_comprehensive_chart(self, simulation_data: dict, save_path: str = None) -> str:
        """创建综合月度收益图表"""
        try:
            equity_df = simulation_data['equity_df']
            trades = simulation_data['trades']
            result = simulation_data['result']
            market_data = simulation_data['market_data']
            
            # 计算月度收益
            monthly_profits = self.calculate_monthly_profits(equity_df, result['initial_capital'])
            
            # 创建图表
            fig = plt.figure(figsize=(20, 16))
            fig.suptitle('P01阿尔法X2024策略 - 月度收益分析报告', fontsize=24, fontweight='bold', y=0.98)
            
            # 创建网格布局
            gs = fig.add_gridspec(4, 3, height_ratios=[2, 1.5, 1, 1], width_ratios=[2, 1, 1], 
                                hspace=0.3, wspace=0.3)
            
            # 1. 主图：权益曲线 + 月度收益标注
            ax1 = fig.add_subplot(gs[0, :])
            
            # 绘制权益曲线
            ax1.plot(equity_df.index, equity_df['equity'], linewidth=3, color='#2E86AB', label='策略权益曲线')
            ax1.axhline(y=result['initial_capital'], color='gray', linestyle='--', alpha=0.7, label='初始资金')
            
            # 标注每月收益
            for idx, (date, row) in enumerate(monthly_profits.iterrows()):
                color = '#00C851' if row['月收益金额'] > 0 else '#FF4444' if row['月收益金额'] < 0 else '#FFBB33'
                
                # 在月末位置添加收益标注
                ax1.annotate(f"{row['月份']}\n{row['月收益金额']:+,.0f}元\n({row['月收益率']:+.2f}%)",
                           xy=(date, row['月末资金']), 
                           xytext=(10, 20 if row['月收益金额'] > 0 else -40),
                           textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.5', facecolor=color, alpha=0.8),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0', color=color),
                           fontsize=10, fontweight='bold', color='white')
            
            # 标注交易点
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            for trade in buy_trades:
                trade_time = trade['time']
                if trade_time in equity_df.index:
                    equity_at_trade = equity_df.loc[trade_time, 'equity']
                    ax1.scatter(trade_time, equity_at_trade, color='green', s=100, marker='^', 
                              label='买入' if trade == buy_trades[0] else "", zorder=5)
            
            for trade in sell_trades:
                trade_time = trade['time']
                if trade_time in equity_df.index:
                    equity_at_trade = equity_df.loc[trade_time, 'equity']
                    ax1.scatter(trade_time, equity_at_trade, color='red', s=100, marker='v', 
                              label='卖出' if trade == sell_trades[0] else "", zorder=5)
            
            ax1.set_title('策略权益曲线与月度收益', fontsize=18, fontweight='bold', pad=20)
            ax1.set_ylabel('资金金额 (元)', fontsize=14)
            ax1.legend(loc='upper left', fontsize=12)
            ax1.grid(True, alpha=0.3)
            ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
            
            # 2. 月度收益柱状图
            ax2 = fig.add_subplot(gs[1, :])
            
            colors = ['#00C851' if x > 0 else '#FF4444' if x < 0 else '#FFBB33' for x in monthly_profits['月收益金额']]
            bars = ax2.bar(range(len(monthly_profits)), monthly_profits['月收益金额'], 
                          color=colors, alpha=0.8, edgecolor='black', linewidth=1)
            
            # 在柱子上添加数值标签
            for i, (bar, value) in enumerate(zip(bars, monthly_profits['月收益金额'])):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + (50 if height > 0 else -150),
                        f'{value:+,.0f}元\n({monthly_profits.iloc[i]["月收益率"]:+.2f}%)',
                        ha='center', va='bottom' if height > 0 else 'top', 
                        fontweight='bold', fontsize=11)
            
            ax2.set_title('每月收益金额', fontsize=16, fontweight='bold')
            ax2.set_ylabel('收益金额 (元)', fontsize=12)
            ax2.set_xticks(range(len(monthly_profits)))
            ax2.set_xticklabels(monthly_profits['月份'], rotation=45)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8)
            ax2.grid(True, alpha=0.3)
            ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
            
            # 3. 月度收益率饼图
            ax3 = fig.add_subplot(gs[2, 0])
            
            profit_months = len(monthly_profits[monthly_profits['月收益金额'] > 0])
            loss_months = len(monthly_profits[monthly_profits['月收益金额'] < 0])
            break_even_months = len(monthly_profits[monthly_profits['月收益金额'] == 0])
            
            sizes = [profit_months, loss_months, break_even_months]
            labels = [f'盈利月份\n{profit_months}个月', f'亏损月份\n{loss_months}个月', f'持平月份\n{break_even_months}个月']
            colors_pie = ['#00C851', '#FF4444', '#FFBB33']
            
            # 只显示非零的部分
            non_zero_sizes = [(size, label, color) for size, label, color in zip(sizes, labels, colors_pie) if size > 0]
            if non_zero_sizes:
                sizes_nz, labels_nz, colors_nz = zip(*non_zero_sizes)
                wedges, texts, autotexts = ax3.pie(sizes_nz, labels=labels_nz, colors=colors_nz, autopct='%1.1f%%',
                                                  startangle=90, textprops={'fontsize': 10, 'fontweight': 'bold'})
            
            ax3.set_title('月度盈亏分布', fontsize=14, fontweight='bold')
            
            # 4. 关键指标表格
            ax4 = fig.add_subplot(gs[2, 1:])
            ax4.axis('off')
            
            # 计算关键指标
            total_months = len(monthly_profits)
            total_profit = monthly_profits['月收益金额'].sum()
            avg_monthly_profit = monthly_profits['月收益金额'].mean()
            best_month = monthly_profits.loc[monthly_profits['月收益金额'].idxmax()]
            worst_month = monthly_profits.loc[monthly_profits['月收益金额'].idxmin()]
            
            # 年化收益率
            if total_months > 0:
                final_equity = monthly_profits['月末资金'].iloc[-1]
                annual_return = (final_equity / result['initial_capital']) ** (12 / total_months) - 1
            else:
                annual_return = 0
            
            # 创建指标表格
            metrics_data = [
                ['总测试月数', f'{total_months}个月'],
                ['累计收益', f'{total_profit:+,.0f}元'],
                ['平均月收益', f'{avg_monthly_profit:+,.0f}元'],
                ['年化收益率', f'{annual_return*100:+.2f}%'],
                ['盈利月份比例', f'{profit_months/total_months*100:.1f}%'],
                ['最佳月份', f'{best_month["月份"]} (+{best_month["月收益金额"]:,.0f}元)'],
                ['最差月份', f'{worst_month["月份"]} ({worst_month["月收益金额"]:+,.0f}元)'],
                ['最大回撤', f'{result["max_drawdown"]*100:.2f}%'],
                ['夏普比率', f'{result["sharpe_ratio"]:.2f}'],
                ['总交易次数', f'{result["total_trades"]}次']
            ]
            
            # 绘制表格
            table = ax4.table(cellText=metrics_data,
                            colLabels=['关键指标', '数值'],
                            cellLoc='center',
                            loc='center',
                            colWidths=[0.4, 0.6])
            
            table.auto_set_font_size(False)
            table.set_fontsize(12)
            table.scale(1, 2)
            
            # 设置表格样式
            for i in range(len(metrics_data) + 1):
                for j in range(2):
                    cell = table[(i, j)]
                    if i == 0:  # 表头
                        cell.set_facecolor('#4472C4')
                        cell.set_text_props(weight='bold', color='white')
                    else:
                        if j == 0:  # 指标名称列
                            cell.set_facecolor('#F2F2F2')
                            cell.set_text_props(weight='bold')
                        else:  # 数值列
                            cell.set_facecolor('white')
                            # 根据数值类型设置颜色
                            text = metrics_data[i-1][1]
                            if '+' in text and '元' in text:
                                cell.set_text_props(color='#00C851', weight='bold')
                            elif '-' in text and '元' in text:
                                cell.set_text_props(color='#FF4444', weight='bold')
            
            ax4.set_title('策略关键指标', fontsize=14, fontweight='bold')
            
            # 5. 月度收益详细表格
            ax5 = fig.add_subplot(gs[3, :])
            ax5.axis('off')
            
            # 准备表格数据
            table_data = []
            for _, row in monthly_profits.iterrows():
                table_data.append([
                    row['月份'],
                    f'{row["月收益金额"]:+,.0f}',
                    f'{row["月收益率"]:+.2f}%',
                    f'{row["累计收益金额"]:+,.0f}',
                    f'{row["累计收益率"]:+.2f}%',
                    row['盈亏状态']
                ])
            
            # 创建详细表格
            detail_table = ax5.table(cellText=table_data,
                                   colLabels=['月份', '月收益金额', '月收益率', '累计收益金额', '累计收益率', '盈亏状态'],
                                   cellLoc='center',
                                   loc='center',
                                   colWidths=[0.15, 0.18, 0.15, 0.18, 0.15, 0.12])
            
            detail_table.auto_set_font_size(False)
            detail_table.set_fontsize(10)
            detail_table.scale(1, 1.8)
            
            # 设置详细表格样式
            for i in range(len(table_data) + 1):
                for j in range(6):
                    cell = detail_table[(i, j)]
                    if i == 0:  # 表头
                        cell.set_facecolor('#4472C4')
                        cell.set_text_props(weight='bold', color='white')
                    else:
                        # 根据盈亏状态设置行颜色
                        status = table_data[i-1][5]
                        if status == '盈利':
                            cell.set_facecolor('#E8F5E8')
                            if j in [1, 2]:  # 收益相关列
                                cell.set_text_props(color='#00C851', weight='bold')
                        elif status == '亏损':
                            cell.set_facecolor('#FFE8E8')
                            if j in [1, 2]:  # 收益相关列
                                cell.set_text_props(color='#FF4444', weight='bold')
                        else:
                            cell.set_facecolor('#FFF8E8')
            
            ax5.set_title('月度收益详细数据', fontsize=14, fontweight='bold')
            
            # 保存图表
            if not save_path:
                save_path = f"P01阿尔法X2024_月度收益分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"📊 月度收益图表已保存: {save_path}")
            
            # 显示图表
            plt.show()
            
            return save_path
            
        except Exception as e:
            logger.error(f"创建图表失败: {e}")
            raise

def main():
    """主函数"""
    print("📊 启动月度收益回测图表生成器...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    try:
        # 创建图表生成器
        chart_generator = MonthlyProfitChartGenerator()
        
        # 模拟策略并生成数据
        print("🔄 运行策略模拟...")
        simulation_data = chart_generator.load_data_and_simulate('2024-04-01', '2024-04-30')
        
        # 生成月度收益图表
        print("🎨 生成月度收益图表...")
        chart_path = chart_generator.create_comprehensive_chart(simulation_data)
        
        print(f"✅ 月度收益分析完成!")
        print(f"📈 图表文件: {chart_path}")
        
        # 打印月度收益摘要
        monthly_profits = chart_generator.calculate_monthly_profits(
            simulation_data['equity_df'], 
            simulation_data['result']['initial_capital']
        )
        
        print(f"\n💰 月度收益摘要:")
        print("-" * 60)
        for _, row in monthly_profits.iterrows():
            status_emoji = "💚" if row['月收益金额'] > 0 else "❤️" if row['月收益金额'] < 0 else "💛"
            print(f"{status_emoji} {row['月份']}: {row['月收益金额']:+,.0f}元 ({row['月收益率']:+.2f}%)")
        
        total_profit = monthly_profits['月收益金额'].sum()
        profit_months = len(monthly_profits[monthly_profits['月收益金额'] > 0])
        total_months = len(monthly_profits)
        
        print(f"\n🎯 总结:")
        print(f"   累计收益: {total_profit:+,.0f}元")
        print(f"   盈利月份: {profit_months}/{total_months} ({profit_months/total_months*100:.1f}%)")
        
    except Exception as e:
        logger.error(f"生成月度收益图表失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
