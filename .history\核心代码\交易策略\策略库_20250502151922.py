# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

class TradingStrategy(BASE_STRATEGY):  # 确保继承自 backtesting.Strategy 或其替身
    """交易策略基类"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功
    performance_stats = {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_pnl': 0.0,
        'max_win': 0.0,
        'max_loss': 0.0,
        'long_trades': 0,
        'short_trades': 0
    }

    def update_performance_stats(self, pnl: float, trade_type: str):
        """
        更新绩效统计数据
        
        Args:
            pnl: 盈亏比例
            trade_type: 交易类型 ('long' 或 'short')
        """
        self.performance_stats['total_trades'] += 1
        self.performance_stats['total_pnl'] += pnl
        
        if trade_type == 'long':
            self.performance_stats['long_trades'] += 1
        else:
            self.performance_stats['short_trades'] += 1
            
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            if pnl > self.performance_stats['max_win']:
                self.performance_stats['max_win'] = pnl
        else:
            self.performance_stats['losing_trades'] += 1
            if pnl < self.performance_stats['max_loss']:
                self.performance_stats['max_loss'] = pnl
                
        logger.info(f"更新绩效统计: 总交易数={self.performance_stats['total_trades']}, 总盈亏={self.performance_stats['total_pnl']:.2%}")

    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 调用 backtesting.Strategy 的父类 __init__
        #    必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        #    父类 __init__ 会处理将 params 设置到实例属性
        if backtesting_available and isinstance(self, BacktestStrategy):
             super().__init__(broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) 调用子类的 init() - backtesting 库会自动调用
        #    但如果我们在基类 __init__ 中做了很多事，确保不要阻止它。
        #    实际上，backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init().


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass

class TrendFollowingStrategy(TradingStrategy):
    """趋势跟踪策略"""
    def __init__(self, broker, data, params=None):
        super().__init__(broker, data, params)
        self.window = params.get('window', 20)

    def init(self):
        self.sma = self.I(self.data.Close.rolling(self.window).mean)

    def next(self):
        if self.position:
            return

        current_price = self.data.Close[-1]
        if current_price > self.sma[-1]:
            self.buy()
        elif current_price < self.sma[-1]:
            self.sell()

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略"""
    def __init__(self, broker, data, params=None):
        super().__init__(broker, data, params)
        self.window = params.get('window', 10)
        self.threshold = params.get('threshold', 1.0)

    def init(self):
        self.sma = self.I(self.data.Close.rolling(self.window).mean)
        self.std = self.I(self.data.Close.rolling(self.window).std)

    def next(self):
        if self.position:
            return

        current_price = self.data.Close[-1]
        upper_band = self.sma[-1] + self.threshold * self.std[-1]
        lower_band = self.sma[-1] - self.threshold * self.std[-1]

        if current_price > upper_band:
            self.sell()
        elif current_price < lower_band:
            self.buy()

# 策略注册表
STRATEGIES = {
    "MeanReversion": MeanReversionStrategy,
    "TrendFollowing": TrendFollowingStrategy
}
