# -*- coding: utf-8 -*-
"""
修复版MeanReversionStrategy - 使用现有数据，确保能产生交易信号
核心修复：
1. 使用SMA代替布林带
2. 使用价格偏离代替RSI
3. 简化逻辑，确保信号生成
4. 保持原版的交易频率特征
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class FixedMeanReversionStrategy:
    """修复版均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'FixedMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 使用现有数据的参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        
        # 均值回归参数（模拟原版的敏感性）
        self.deviation_threshold = all_params.get('deviation_threshold', 0.005)  # 0.5%偏离
        self.volatility_threshold = all_params.get('volatility_threshold', 0.003)  # 0.3%波动率
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.01)  # 1%风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 1.5)  # 较低的盈亏比，模拟原版
        
        # 交易频率控制（模拟原版的高频特征）
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 15)  # 15分钟间隔
        self.max_daily_trades = all_params.get('max_daily_trades', 20)  # 每日最多20次
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.total_signals_generated = 0
        
        print(f"策略 FixedMeanReversionStrategy 初始化...")
        print(f"FixedMeanReversionStrategy: 修复版均值回归，偏离阈值={self.deviation_threshold*100}%, "
              f"盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, 信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_mean_reversion_signal(self, data: Dict[str, Any]) -> str:
        """检查均值回归信号"""
        price = data.get('CLOSE', 0)
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return 'none'
        
        # 计算价格偏离
        deviation = (price - sma_short) / sma_short
        
        # 计算当前波动率
        volatility = (high - low) / price if price > 0 else 0
        
        # 均值回归信号逻辑（模拟布林带效果）
        if deviation < -self.deviation_threshold and volatility > self.volatility_threshold:
            return 'buy'  # 价格低于均线且有足够波动率，买入
        elif deviation > self.deviation_threshold and volatility > self.volatility_threshold:
            return 'sell'  # 价格高于均线且有足够波动率，卖出
        
        return 'none'
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 移除持仓检查，允许多次交易
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 修复版均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'HIGH', 'LOW']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查SMA数据
        if self.sma_short_key not in data:
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取基础数据
        price = data.get('CLOSE')
        high = data.get('HIGH')
        low = data.get('LOW')
        sma_short = data.get(self.sma_short_key)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, high, low, sma_short]):
            return signals
        
        # 计算ATR替代
        atr = (high - low) if (high - low) > 0 else price * 0.02
        
        # 检查均值回归信号
        signal_type = self.check_mean_reversion_signal(data)
        
        if signal_type in ['buy', 'sell']:
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            if signal_type == 'buy':
                stop_loss = price - (atr * self.atr_sl_multiple)
                take_profit = price + (atr * self.atr_tp_multiple)
                action = 'buy'
            else:  # sell
                stop_loss = price + (atr * self.atr_sl_multiple)
                take_profit = price - (atr * self.atr_tp_multiple)
                action = 'sell'
            
            # 计算价格偏离度
            deviation = (price - sma_short) / sma_short * 100
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': action,
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'FixedMeanReversionStrategy',
                'signal_type': f'fixed_mean_reversion_{signal_type}',
                'reason': f'修复版均值回归: {signal_type}, 偏离={deviation:.2f}%, 价格={price:.2f}vs SMA={sma_short:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            self.total_signals_generated += 1
            
            print(f"[{current_time}] FixedMeanReversionStrategy: {signal_type}信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, 价格={price:.2f}vs SMA={sma_short:.2f}, "
                  f"数量={position_size:.4f}, 日内第{self.daily_trade_count}次, 信号#{self.total_signals_generated}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'FixedMeanReversionStrategy',
            'version': '1.0',
            'description': '修复版均值回归策略，使用现有数据确保信号生成',
            'fixes': [
                '使用SMA代替布林带',
                '使用价格偏离代替RSI',
                '简化逻辑确保信号生成',
                '保持原版交易频率特征'
            ],
            'parameters': {
                'deviation_threshold': self.deviation_threshold,
                'volatility_threshold': self.volatility_threshold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'total_signals_generated': self.total_signals_generated,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
