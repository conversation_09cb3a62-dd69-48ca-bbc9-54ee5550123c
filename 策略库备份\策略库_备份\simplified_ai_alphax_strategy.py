# -*- coding: utf-8 -*-
"""
简化版AI优化AlphaX策略 - 使用现有技术指标作为特征
由于原始AI模型特征缺失，改为使用传统技术指标的智能组合
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class SimplifiedAIAlphaXStrategy:
    """简化版AI优化AlphaX策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'SimplifiedAIAlphaXStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 智能评分参数
        self.confidence_threshold = all_params.get('confidence_threshold', 0.6)
        
        # 传统技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.adx_threshold = all_params.get('adx_threshold', 15)  # 降低要求
        self.rsi_oversold = all_params.get('rsi_oversold', 35)   # 放宽条件
        self.rsi_overbought = all_params.get('rsi_overbought', 65) # 放宽条件
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.015)
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 4.0)
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 60)  # 缩短间隔
        
        # 新增参数
        self.max_daily_trades = all_params.get('max_daily_trades', 4)
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        print(f"策略 SimplifiedAIAlphaXStrategy 初始化...")
        print(f"SimplifiedAIAlphaXStrategy: 智能评分阈值={self.confidence_threshold}, "
              f"ADX阈值={self.adx_threshold}, RSI范围=[{self.rsi_oversold}, {self.rsi_overbought}]")
    
    def calculate_smart_score(self, data: Dict[str, Any]) -> float:
        """计算智能评分，模拟AI预测"""
        try:
            score = 0.0
            max_score = 0.0
            
            # 1. 趋势评分 (权重: 30%)
            sma_short = data.get(self.sma_short_key, 0)
            sma_long = data.get(self.sma_long_key, 0)
            if sma_short > 0 and sma_long > 0:
                if sma_short > sma_long:
                    score += 0.3  # 上升趋势
                max_score += 0.3
            
            # 2. 动量评分 (权重: 25%)
            rsi = data.get('RSI_14', 50)
            if not pd.isna(rsi):
                if rsi <= self.rsi_oversold:
                    score += 0.25  # 超卖，买入机会
                elif rsi >= self.rsi_overbought:
                    score += 0.1   # 超买，谨慎
                else:
                    score += 0.15  # 中性
                max_score += 0.25
            
            # 3. 趋势强度评分 (权重: 20%)
            adx = data.get('ADX_14', 0)
            if not pd.isna(adx) and adx > 0:
                if adx >= 25:
                    score += 0.2   # 强趋势
                elif adx >= 15:
                    score += 0.15  # 中等趋势
                else:
                    score += 0.05  # 弱趋势
                max_score += 0.2
            
            # 4. 波动性评分 (权重: 15%)
            atr = data.get('ATR_14', 0)
            price = data.get('CLOSE', 0)
            if atr > 0 and price > 0:
                volatility_ratio = atr / price
                if 0.01 <= volatility_ratio <= 0.03:  # 适中波动
                    score += 0.15
                elif volatility_ratio < 0.01:  # 低波动
                    score += 0.1
                else:  # 高波动
                    score += 0.05
                max_score += 0.15
            
            # 5. 成交量评分 (权重: 10%)
            volume = data.get('VOLUME', 0)
            if volume > 0:
                # 简单的成交量评分
                score += 0.1
                max_score += 0.1
            
            # 计算最终评分 (0-1之间)
            final_score = score / max_score if max_score > 0 else 0
            
            return final_score
            
        except Exception as e:
            logger.error(f"计算智能评分错误: {e}")
            return 0.0
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 使用智能评分"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'ADX_14', 'RSI_14', 'ATR_14']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 计算智能评分
        smart_score = self.calculate_smart_score(data)
        
        # 如果评分不够高，不生成信号
        if smart_score < self.confidence_threshold:
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        adx = data.get('ADX_14')
        rsi = data.get('RSI_14')
        atr = data.get('ATR_14')
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, adx, rsi, atr]):
            return signals
        
        # 基本条件检查
        basic_conditions = [
            adx >= self.adx_threshold,  # 最低趋势要求
            smart_score >= self.confidence_threshold,  # 智能评分要求
        ]
        
        if all(basic_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'SimplifiedAIAlphaXStrategy',
                'signal_type': 'smart_entry',
                'reason': f'智能买入信号: 评分={smart_score:.3f}, RSI={rsi:.2f}, ADX={adx:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] SimplifiedAIAlphaXStrategy: 智能买入信号 BTCUSDT, "
                  f"评分={smart_score:.3f}, RSI={rsi:.2f}, ADX={adx:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'SimplifiedAIAlphaXStrategy',
            'version': '1.0',
            'description': '简化版AI优化AlphaX策略，使用智能评分系统',
            'scoring_system': {
                'trend_weight': 0.3,
                'momentum_weight': 0.25,
                'strength_weight': 0.2,
                'volatility_weight': 0.15,
                'volume_weight': 0.1
            },
            'parameters': {
                'confidence_threshold': self.confidence_threshold,
                'adx_threshold': self.adx_threshold,
                'rsi_oversold': self.rsi_oversold,
                'rsi_overbought': self.rsi_overbought,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes
            },
            'status': {
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
