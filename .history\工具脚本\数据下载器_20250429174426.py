# -*- coding: utf-8 -*-
# (代码与上一个回答中的 `量化交易系统/工具脚本/数据下载器.py` 相同，无需修改)
import yfinance as yf
import pandas as pd
import os
import argparse
import logging
import time
from typing import List
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO,format='%(asctime)s-%(levelname)s-%(message)s')
DATA_DIR=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),'数据'); os.makedirs(DATA_DIR,exist_ok=True)

def download_stock_data(tickers:List[str],start_date:str,end_date:str,output_dir:str=DATA_DIR):
    logging.info(f"下载数据: Tickers={tickers}, Start={start_date}, End={end_date}")
    try: data=yf.download(tickers,start=start_date,end=end_date,progress=True,auto_adjust=False) # Fetch raw OHLCV
    except Exception as e: logging.error(f"下载错误: {e}",exc_info=True); return
    if data.empty: logging.warning(f"无数据 for {tickers} in {start_date}-{end_date}"); return

    if isinstance(data.columns,pd.MultiIndex): # Multi tickers
        for ticker in tickers:
            try: ticker_data=data.loc[:,(slice(None),ticker)]; ticker_data.columns=ticker_data.columns.droplevel(1);
            except KeyError: logging.warning(f"MultiIndex 中找不到 Ticker: {ticker}"); continue
            if ticker_data.empty or ticker_data['Close'].isnull().all(): logging.warning(f"无有效数据 {ticker}"); continue
            ticker_data['Symbol']=ticker.upper();
            if 'Adj Close' in ticker_data.columns and not ticker_data['Close'].isnull().all() and ticker_data['Close'].iloc[-1]!=0: # Approx Adj
                 adjf=ticker_data['Adj Close']/ticker_data['Close']; ticker_data['Open']*=adjf; ticker_data['High']*=adjf; ticker_data['Low']*=adjf; ticker_data['Close']=ticker_data['Adj Close']
            ticker_data=ticker_data[['Symbol','Open','High','Low','Close','Volume']].dropna(subset=['Close'])
            fname=f"{ticker.upper()}_{start_date}_to_{end_date}.csv"; fpath=os.path.join(output_dir,fname); ticker_data.to_csv(fpath); logging.info(f"保存 {ticker} 到 {fpath} ({len(ticker_data)}条)")
    elif len(tickers)==1: # Single ticker
        ticker=tickers[0].upper(); data['Symbol']=ticker
        if data.empty or data['Close'].isnull().all(): logging.warning(f"无有效数据 {ticker}"); return
        if 'Adj Close' in data.columns and not data['Close'].isnull().all() and data['Close'].iloc[-1]!=0: # Approx Adj
             adjf=data['Adj Close']/data['Close']; data['Open']*=adjf; data['High']*=adjf; data['Low']*=adjf; data['Close']=data['Adj Close']
        data=data[['Symbol','Open','High','Low','Close','Volume']].dropna(subset=['Close'])
        fname=f"{ticker}_{start_date}_to_{end_date}.csv"; fpath=os.path.join(output_dir,fname); data.to_csv(fpath); logging.info(f"保存 {ticker} 到 {fpath} ({len(data)}条)")
    else: logging.warning("未知下载格式")

if __name__=='__main__':
    parser=argparse.ArgumentParser(description='下载股票历史数据'); parser.add_argument('-t','--tickers',required=True,nargs='+',help='股票代码列表'); parser.add_argument('-s','--start',required=True,help='开始日期(YYYY-MM-DD)'); parser.add_argument('-e','--end',required=True,help='结束日期(YYYY-MM-DD)'); parser.add_argument('-o','--output',default=DATA_DIR,help=f'输出目录 (默认:{DATA_DIR})'); args=parser.parse_args();
    try: pd.to_datetime(args.start); pd.to_datetime(args.end)
    except ValueError: print("日期格式错误"); exit(1)
    os.makedirs(args.output, exist_ok=True); download_stock_data(args.tickers,args.start,args.end,args.output)
