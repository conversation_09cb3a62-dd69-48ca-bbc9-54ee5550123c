# -*- coding: utf-8 -*-
"""
超保守盈利策略 - 基于SimpleMeanReversionStrategy的成功经验
核心理念：
1. 极度保守的入场条件
2. 宁缺毋滥，确保每笔交易都有高胜率
3. 基于已验证成功的SimpleMeanReversionStrategy
4. 目标：月月盈利，即使交易次数很少
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class UltraConservativeStrategy:
    """超保守盈利策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'UltraConservativeStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 极度保守的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.025)  # 2.5%偏离（更严格）
        
        # 极度保守的风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.003)  # 0.3%风险（更低）
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.5)  # 更大止损空间
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 5.0)  # 更高止盈目标
        
        # 极度保守的交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 1440)  # 24小时间隔
        self.max_monthly_trades = all_params.get('max_monthly_trades', 5)  # 每月最多5次
        self.max_daily_trades = all_params.get('max_daily_trades', 1)  # 每日最多1次
        
        # 极度严格的质量控制参数
        self.min_volatility_pct = all_params.get('min_volatility_pct', 0.015)  # 最小波动率1.5%
        self.volume_threshold = all_params.get('volume_threshold', 1.5)  # 更高成交量要求
        self.trend_strength_threshold = all_params.get('trend_strength_threshold', 0.995)  # 极严格趋势要求
        self.price_position_threshold = all_params.get('price_position_threshold', 0.2)  # 更严格价格位置
        
        # 新增超保守过滤条件
        self.min_price_level = all_params.get('min_price_level', 60000)  # 最低价格水平
        self.max_recent_volatility = all_params.get('max_recent_volatility', 0.05)  # 最大近期波动率
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.monthly_trade_count = 0
        self.last_trade_date = None
        self.last_trade_month = None
        self.total_signals_generated = 0
        
        print(f"策略 UltraConservativeStrategy 初始化...")
        print(f"UltraConservativeStrategy: 超保守盈利目标，价格偏离={self.price_deviation_pct*100}%, "
              f"盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, 每月最多{self.max_monthly_trades}次")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_monthly_trade_limit(self, current_time: datetime) -> bool:
        """检查每月交易次数限制"""
        current_month = (current_time.year, current_time.month)
        
        if self.last_trade_month != current_month:
            self.monthly_trade_count = 0
            self.last_trade_month = current_month
        
        return self.monthly_trade_count < self.max_monthly_trades
    
    def check_extreme_price_deviation(self, data: Dict[str, Any]) -> bool:
        """检查极端价格偏离机会"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 价格必须极度显著低于短期均线
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            return deviation >= self.price_deviation_pct
        
        return False
    
    def check_ultra_safe_trend(self, data: Dict[str, Any]) -> bool:
        """检查超安全趋势环境"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return False  # 必须有完整数据
        
        # 短期均线必须非常接近长期均线（极稳定环境）
        trend_ratio = sma_short / sma_long
        return trend_ratio >= self.trend_strength_threshold
    
    def check_premium_volatility(self, data: Dict[str, Any]) -> bool:
        """检查优质波动率"""
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        close = data.get('CLOSE', 0)
        
        if high <= 0 or low <= 0 or close <= 0:
            return False
        
        # 当日波动率必须足够大但不能太大
        volatility = (high - low) / close
        return self.min_volatility_pct <= volatility <= self.max_recent_volatility
    
    def check_strong_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查强成交量确认"""
        current_volume = data.get('VOLUME', 0)
        
        if current_volume <= 0:
            return False  # 必须有成交量数据
        
        return current_volume >= self.volume_threshold
    
    def check_perfect_entry_timing(self, data: Dict[str, Any]) -> bool:
        """检查完美入场时机"""
        price = data.get('CLOSE', 0)
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        
        if price <= 0 or high <= 0 or low <= 0:
            return False
        
        # 价格必须非常接近当日低点
        if high <= low:
            return False
        
        price_position = (price - low) / (high - low)
        return price_position <= self.price_position_threshold
    
    def check_market_safety(self, data: Dict[str, Any]) -> bool:
        """检查市场安全性"""
        price = data.get('CLOSE', 0)
        
        # 价格不能太低（避免极端市场）
        if price < self.min_price_level:
            return False
        
        return True
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 移除持仓检查，允许多次交易
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 超保守盈利"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'HIGH', 'LOW', 'VOLUME']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查SMA数据
        if self.sma_short_key not in data or self.sma_long_key not in data:
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        if not self.check_monthly_trade_limit(current_time):
            return signals
        
        # 获取基础数据
        price = data.get('CLOSE')
        high = data.get('HIGH')
        low = data.get('LOW')
        volume = data.get('VOLUME')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, high, low, sma_short, sma_long]):
            return signals
        
        # 计算ATR
        atr = (high - low) if (high - low) > 0 else price * 0.02
        
        # 超保守的买入条件（极度严格）
        ultra_conservative_conditions = [
            self.check_extreme_price_deviation(data),  # 极端价格偏离
            self.check_ultra_safe_trend(data),  # 超安全趋势环境
            self.check_premium_volatility(data),  # 优质波动率
            self.check_strong_volume_confirmation(data),  # 强成交量确认
            self.check_perfect_entry_timing(data),  # 完美入场时机
            self.check_market_safety(data),  # 市场安全性
        ]
        
        if all(ultra_conservative_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100
            
            # 计算预期盈亏比
            profit_distance = take_profit - price
            loss_distance = price - stop_loss
            actual_profit_loss_ratio = profit_distance / loss_distance if loss_distance > 0 else 0
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'UltraConservativeStrategy',
                'signal_type': 'ultra_conservative_mean_reversion',
                'reason': f'超保守信号: 偏离={deviation:.2f}%, 盈亏比={actual_profit_loss_ratio:.2f}:1, 价格={price:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            self.monthly_trade_count += 1
            self.total_signals_generated += 1
            
            print(f"[{current_time}] UltraConservativeStrategy: 超保守买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, 盈亏比={actual_profit_loss_ratio:.2f}:1, "
                  f"数量={position_size:.4f}, 月度第{self.monthly_trade_count}次, 信号#{self.total_signals_generated}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'UltraConservativeStrategy',
            'version': '1.0',
            'description': '超保守盈利策略，基于SimpleMeanReversionStrategy的成功经验',
            'design_principles': [
                '极度保守的入场条件',
                '宁缺毋滥的交易理念',
                '基于成功经验的参数设置',
                '确保每笔交易高胜率'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_monthly_trades': self.max_monthly_trades,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'total_signals_generated': self.total_signals_generated,
                'monthly_trade_count': self.monthly_trade_count,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
