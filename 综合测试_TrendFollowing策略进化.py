# -*- coding: utf-8 -*-
"""
TrendFollowing策略进化综合测试
对比原版、改进版、多时间框架版、ML增强版的性能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import logging
import time
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

def comprehensive_strategy_comparison():
    """综合策略对比测试"""
    
    print("🚀 TrendFollowing策略进化综合测试")
    print("=" * 80)
    print("测试策略:")
    print("  1. TrendFollowing (原版)")
    print("  2. ImprovedTrendFollowingStrategy (改进版)")
    print("  3. MultiTimeframeTrendFollowingStrategy (多时间框架版)")
    print("  4. MLEnhancedTrendFollowingStrategy (ML增强版)")
    print("=" * 80)
    
    try:
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-10'  # 10天测试期
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 定义测试策略
        strategies_to_test = [
            {
                'name': 'TrendFollowing (原版)',
                'class': STRATEGIES.get('TrendFollowing'),
                'params': {
                    'sma_short': 10,
                    'sma_long': 30,
                    'rsi_threshold': 45,
                    'risk_per_trade_pct': 0.02,
                    'atr_sl_multiple': 1.5,
                    'atr_tp_multiple': 3.0,
                    'min_signal_interval_minutes': 30
                },
                'color': '#FF6B6B'
            },
            {
                'name': 'ImprovedTrendFollowing (改进版)',
                'class': STRATEGIES.get('ImprovedTrendFollowingStrategy'),
                'params': {
                    'sma_short': 12,
                    'sma_long': 26,
                    'adx_threshold': 30,
                    'adx_rising_periods': 3,
                    'min_momentum_threshold': 0.002,
                    'volume_threshold': 1.5,
                    'risk_per_trade_pct': 0.008,
                    'initial_sl_atr_multiple': 2.5,
                    'tp_level_1': 2.0,
                    'min_signal_interval_minutes': 120
                },
                'color': '#4ECDC4'
            },
            {
                'name': 'MultiTimeframe (多时间框架)',
                'class': STRATEGIES.get('MultiTimeframeTrendFollowingStrategy'),
                'params': {
                    'primary_timeframe': '1H',
                    'signal_timeframe': '15T',
                    'trend_timeframe': '4H',
                    'trend_adx_threshold': 25,
                    'primary_adx_threshold': 30,
                    'signal_adx_threshold': 35,
                    'require_trend_alignment': True,
                    'min_trend_strength': 0.6,
                    'risk_per_trade_pct': 0.01
                },
                'color': '#45B7D1'
            },
            {
                'name': 'MLEnhanced (ML增强)',
                'class': STRATEGIES.get('MLEnhancedTrendFollowingStrategy'),
                'params': {
                    'use_ml_filter': True,
                    'ml_confidence_threshold': 0.6,
                    'feature_lookback': 20,
                    'base_risk_per_trade': 0.01,
                    'max_risk_per_trade': 0.02,
                    'min_risk_per_trade': 0.005
                },
                'color': '#96CEB4'
            }
        ]
        
        results_comparison = []
        equity_curves = {}
        
        for strategy_info in strategies_to_test:
            strategy_name = strategy_info['name']
            strategy_class = strategy_info['class']
            strategy_params = strategy_info['params']
            
            if not strategy_class:
                print(f"❌ 策略 {strategy_name} 不可用")
                continue
            
            print(f"\n[测试] {strategy_name}")
            print("-" * 50)
            
            start_time = time.time()
            
            try:
                # 创建回测引擎
                backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
                
                # 运行回测
                results = backtester.run_backtest(config.start_date, config.end_date)
                
                execution_time = time.time() - start_time
                
                if results:
                    # 提取关键指标
                    strategy_result = {
                        'strategy_name': strategy_name,
                        'total_return_pct': results.get('总收益率', 0) * 100,
                        'annual_return_pct': results.get('年化收益率', 0) * 100,
                        'total_trades': results.get('总交易次数', 0),
                        'winning_trades': results.get('盈利次数', 0),
                        'losing_trades': results.get('亏损次数', 0),
                        'win_rate': results.get('胜率', 0) * 100,
                        'profit_loss_ratio': results.get('盈亏比', 0),
                        'sharpe_ratio': results.get('夏普比率', 0),
                        'sortino_ratio': results.get('索提诺比率', 0),
                        'max_drawdown_pct': results.get('最大回撤率', 0) * 100,
                        'calmar_ratio': results.get('Calmar比率', 0),
                        'volatility_pct': results.get('年化波动率', 0) * 100,
                        'execution_time': execution_time,
                        'color': strategy_info['color']
                    }
                    
                    results_comparison.append(strategy_result)
                    
                    # 获取权益曲线
                    if hasattr(backtester, 'portfolio') and hasattr(backtester.portfolio, 'equity_curve'):
                        equity_curves[strategy_name] = backtester.portfolio.equity_curve.copy()
                    
                    print(f"✅ 完成 - 收益率: {strategy_result['total_return_pct']:.2f}%")
                    print(f"   交易次数: {strategy_result['total_trades']}")
                    print(f"   胜率: {strategy_result['win_rate']:.1f}%")
                    print(f"   夏普比率: {strategy_result['sharpe_ratio']:.2f}")
                    print(f"   最大回撤: {strategy_result['max_drawdown_pct']:.2f}%")
                    print(f"   执行时间: {execution_time:.1f}秒")
                
                else:
                    print(f"❌ 回测失败")
                    
            except Exception as e:
                print(f"❌ 策略测试失败: {e}")
                logger.error(f"策略 {strategy_name} 测试失败: {e}")
                continue
        
        # 生成综合对比报告
        if len(results_comparison) >= 2:
            generate_comparison_report(results_comparison)
            
            # 生成可视化图表
            if equity_curves:
                create_comparison_charts(results_comparison, equity_curves)
        
        return results_comparison
        
    except Exception as e:
        logger.error(f"综合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_comparison_report(results: list):
    """生成对比报告"""
    print(f"\n📊 策略进化对比报告")
    print("=" * 100)
    
    # 创建DataFrame
    df = pd.DataFrame(results)
    
    # 显示详细对比表格
    metrics_to_show = [
        ('策略名称', 'strategy_name'),
        ('总收益率(%)', 'total_return_pct'),
        ('年化收益率(%)', 'annual_return_pct'),
        ('交易次数', 'total_trades'),
        ('胜率(%)', 'win_rate'),
        ('盈亏比', 'profit_loss_ratio'),
        ('夏普比率', 'sharpe_ratio'),
        ('索提诺比率', 'sortino_ratio'),
        ('最大回撤(%)', 'max_drawdown_pct'),
        ('年化波动率(%)', 'volatility_pct'),
        ('Calmar比率', 'calmar_ratio')
    ]
    
    print(f"{'指标':<25}", end='')
    for result in results:
        print(f"{result['strategy_name'][:15]:<18}", end='')
    print()
    print("-" * 100)
    
    for metric_name, metric_key in metrics_to_show[1:]:  # 跳过策略名称
        print(f"{metric_name:<25}", end='')
        for result in results:
            value = result[metric_key]
            if isinstance(value, float):
                if 'ratio' in metric_key.lower() or metric_key == 'profit_loss_ratio':
                    print(f"{value:<18.2f}", end='')
                else:
                    print(f"{value:<18.2f}", end='')
            else:
                print(f"{value:<18}", end='')
        print()
    
    # 排名分析
    print(f"\n🏆 各指标排名:")
    print("-" * 50)
    
    ranking_metrics = [
        ('总收益率', 'total_return_pct', True),
        ('夏普比率', 'sharpe_ratio', True),
        ('胜率', 'win_rate', True),
        ('最大回撤', 'max_drawdown_pct', False),  # 越小越好
        ('交易次数', 'total_trades', None)  # 中性指标
    ]
    
    for metric_name, metric_key, higher_better in ranking_metrics:
        if higher_better is not None:
            sorted_results = sorted(results, key=lambda x: x[metric_key], reverse=higher_better)
            print(f"{metric_name}排名:")
            for i, result in enumerate(sorted_results):
                print(f"  {i+1}. {result['strategy_name']}: {result[metric_key]:.2f}")
        print()
    
    # 综合评分
    print(f"🎯 综合评分 (加权平均):")
    print("-" * 30)
    
    for result in results:
        # 综合评分公式
        score = (
            result['total_return_pct'] * 0.25 +
            min(result['sharpe_ratio'], 3) * 10 * 0.25 +  # 限制夏普比率影响
            result['win_rate'] * 0.2 +
            max(0, (10 - result['max_drawdown_pct'])) * 0.15 +  # 回撤惩罚
            min(result['profit_loss_ratio'], 3) * 10 * 0.15  # 限制盈亏比影响
        )
        
        print(f"{result['strategy_name']}: {score:.2f}")
    
    # 保存详细结果
    try:
        output_file = "策略优化/TrendFollowing策略进化对比.csv"
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n💾 详细对比结果已保存: {output_file}")
    except Exception as e:
        logger.warning(f"保存对比结果失败: {e}")

def create_comparison_charts(results: list, equity_curves: dict):
    """创建对比图表"""
    try:
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 权益曲线对比
        ax1 = plt.subplot(2, 3, 1)
        for strategy_name, equity_curve in equity_curves.items():
            if not equity_curve.empty:
                # 计算累计收益率
                returns = equity_curve.pct_change().fillna(0)
                cumulative_returns = (1 + returns).cumprod() - 1
                
                # 找到对应的颜色
                color = '#1f77b4'  # 默认颜色
                for result in results:
                    if result['strategy_name'] == strategy_name:
                        color = result['color']
                        break
                
                plt.plot(cumulative_returns.index, cumulative_returns.values * 100, 
                        label=strategy_name, linewidth=2, color=color)
        
        plt.title('权益曲线对比', fontsize=14, fontweight='bold')
        plt.xlabel('时间')
        plt.ylabel('累计收益率 (%)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 收益率对比
        ax2 = plt.subplot(2, 3, 2)
        strategy_names = [r['strategy_name'] for r in results]
        returns = [r['total_return_pct'] for r in results]
        colors = [r['color'] for r in results]
        
        bars = plt.bar(range(len(strategy_names)), returns, color=colors, alpha=0.7)
        plt.title('总收益率对比', fontsize=14, fontweight='bold')
        plt.ylabel('收益率 (%)')
        plt.xticks(range(len(strategy_names)), [name.split('(')[0] for name in strategy_names], rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, returns):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 3. 夏普比率对比
        ax3 = plt.subplot(2, 3, 3)
        sharpe_ratios = [r['sharpe_ratio'] for r in results]
        bars = plt.bar(range(len(strategy_names)), sharpe_ratios, color=colors, alpha=0.7)
        plt.title('夏普比率对比', fontsize=14, fontweight='bold')
        plt.ylabel('夏普比率')
        plt.xticks(range(len(strategy_names)), [name.split('(')[0] for name in strategy_names], rotation=45)
        
        for bar, value in zip(bars, sharpe_ratios):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05, 
                    f'{value:.2f}', ha='center', va='bottom')
        
        # 4. 胜率对比
        ax4 = plt.subplot(2, 3, 4)
        win_rates = [r['win_rate'] for r in results]
        bars = plt.bar(range(len(strategy_names)), win_rates, color=colors, alpha=0.7)
        plt.title('胜率对比', fontsize=14, fontweight='bold')
        plt.ylabel('胜率 (%)')
        plt.xticks(range(len(strategy_names)), [name.split('(')[0] for name in strategy_names], rotation=45)
        
        for bar, value in zip(bars, win_rates):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 5. 最大回撤对比
        ax5 = plt.subplot(2, 3, 5)
        max_drawdowns = [r['max_drawdown_pct'] for r in results]
        bars = plt.bar(range(len(strategy_names)), max_drawdowns, color=colors, alpha=0.7)
        plt.title('最大回撤对比', fontsize=14, fontweight='bold')
        plt.ylabel('最大回撤 (%)')
        plt.xticks(range(len(strategy_names)), [name.split('(')[0] for name in strategy_names], rotation=45)
        
        for bar, value in zip(bars, max_drawdowns):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 6. 雷达图 - 综合性能
        ax6 = plt.subplot(2, 3, 6, projection='polar')
        
        # 标准化指标 (0-1范围)
        metrics = ['收益率', '夏普比率', '胜率', '盈亏比', '回撤控制']
        
        for result in results:
            values = [
                max(0, min(1, result['total_return_pct'] / 10)),  # 收益率标准化
                max(0, min(1, result['sharpe_ratio'] / 3)),       # 夏普比率标准化
                result['win_rate'] / 100,                         # 胜率标准化
                max(0, min(1, result['profit_loss_ratio'] / 3)),  # 盈亏比标准化
                max(0, 1 - result['max_drawdown_pct'] / 20)       # 回撤控制标准化
            ]
            
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]
            
            ax6.plot(angles, values, 'o-', linewidth=2, label=result['strategy_name'], 
                    color=result['color'])
            ax6.fill(angles, values, alpha=0.25, color=result['color'])
        
        ax6.set_xticks(angles[:-1])
        ax6.set_xticklabels(metrics)
        ax6.set_ylim(0, 1)
        ax6.set_title('综合性能雷达图', fontsize=14, fontweight='bold', pad=20)
        ax6.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"D:/回测结果/TrendFollowing策略进化对比_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 对比图表已保存: {chart_file}")
        
        plt.show()
        
    except Exception as e:
        logger.error(f"创建对比图表失败: {e}")

def main():
    """主函数"""
    print("🚀 开始TrendFollowing策略进化综合测试...")
    
    start_time = time.time()
    results = comprehensive_strategy_comparison()
    end_time = time.time()
    
    if results:
        print(f"\n✅ 综合测试完成!")
        print(f"⏱️ 总耗时: {(end_time - start_time)/60:.1f}分钟")
        print(f"📊 测试策略数: {len(results)}")
        
        # 找出最佳策略
        best_strategy = max(results, key=lambda x: x['total_return_pct'])
        print(f"🏆 最佳收益策略: {best_strategy['strategy_name']} ({best_strategy['total_return_pct']:.2f}%)")
        
        best_sharpe = max(results, key=lambda x: x['sharpe_ratio'] if not np.isnan(x['sharpe_ratio']) else -999)
        print(f"📈 最佳夏普比率: {best_sharpe['strategy_name']} ({best_sharpe['sharpe_ratio']:.2f})")
        
    else:
        print("❌ 综合测试失败")

if __name__ == "__main__":
    main()
