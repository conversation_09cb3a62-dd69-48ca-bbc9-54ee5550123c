# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 2023-2024年两年压力测试
使用本地BTCUSDT历史数据进行全面验证
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
import time
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class AlphaX2024StressTest:
    """P01阿尔法X2024策略压力测试器"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        self.test_periods = [
            {
                'name': '2023年全年',
                'start': '2023-01-01',
                'end': '2023-12-31',
                'description': '2023年完整年度测试'
            },
            {
                'name': '2024年全年',
                'start': '2024-01-01', 
                'end': '2024-12-31',
                'description': '2024年完整年度测试'
            },
            {
                'name': '2023-2024两年',
                'start': '2023-01-01',
                'end': '2024-12-31', 
                'description': '完整两年压力测试'
            },
            {
                'name': '熊市期间',
                'start': '2023-01-01',
                'end': '2023-06-30',
                'description': '2023年上半年熊市测试'
            },
            {
                'name': '牛市期间',
                'start': '2023-10-01',
                'end': '2024-03-31',
                'description': '牛市上涨期测试'
            },
            {
                'name': '高波动期',
                'start': '2024-01-01',
                'end': '2024-04-30',
                'description': '2024年初高波动期测试'
            }
        ]
        
        self.strategy_params = {
            'sma_short': 12,
            'sma_long': 26,
            'rsi_period': 14,
            'atr_period': 14,
            'adx_period': 14,
            'adx_threshold': 25,
            'rsi_overbought': 70,
            'rsi_oversold': 30,
            'risk_per_trade_pct': 0.01,
            'atr_sl_multiple': 2.5,
            'atr_tp_multiple': 5.0,
            'min_signal_interval_minutes': 120,
            'max_position_size': 0.95,
            'trailing_stop_enabled': True,
            'dynamic_position_sizing': True
        }
        
        self.results = {}
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            # 获取日期范围内的所有文件
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                # 构建文件路径
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    # 读取数据
                    df = pd.read_csv(filepath)
                    
                    # 标准化列名
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 重命名列以匹配策略需求
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    # 过滤日期范围
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            # 合并所有数据
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            
            # 去重
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            print(f"   时间范围: {combined_data.index[0]} 至 {combined_data.index[-1]}")
            
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def run_backtest_for_period(self, period_info: dict) -> dict:
        """对指定期间运行回测"""
        try:
            print(f"\n🚀 开始回测: {period_info['name']}")
            print(f"   期间: {period_info['start']} 至 {period_info['end']}")
            print(f"   描述: {period_info['description']}")
            print("-" * 60)
            
            start_time = time.time()
            
            # 加载数据
            data = self.load_historical_data(period_info['start'], period_info['end'])
            
            if data.empty:
                return {'error': '数据为空'}
            
            # 导入回测引擎
            from 模拟回测引擎_分钟级 import MinuteEventBacktester
            from 配置.系统配置 import Config
            from 核心代码.交易策略.策略库 import STRATEGIES
            
            # 创建配置
            config = Config()
            config.start_date = period_info['start']
            config.end_date = period_info['end']
            config.initial_cash = 100000
            config.cost = 0.0005
            config.crypto_pairs = ['BTCUSDT']
            config.benchmark_symbol = 'BTCUSDT'
            
            # 获取策略类
            strategy_class = STRATEGIES.get('AlphaXInspiredStrategy')
            if not strategy_class:
                return {'error': '策略类未找到'}
            
            # 创建回测引擎
            backtester = MinuteEventBacktester(config, strategy_class, self.strategy_params)
            
            # 运行回测
            results = backtester.run_backtest(config.start_date, config.end_date)
            
            execution_time = time.time() - start_time
            
            if results:
                # 计算额外指标
                total_days = (pd.to_datetime(period_info['end']) - pd.to_datetime(period_info['start'])).days
                
                # 获取权益曲线
                equity_curve = None
                if hasattr(backtester, 'portfolio') and hasattr(backtester.portfolio, 'equity_curve'):
                    equity_curve = backtester.portfolio.equity_curve.copy()
                
                # 计算市场基准收益
                market_return = ((data['CLOSE'].iloc[-1] - data['CLOSE'].iloc[0]) / data['CLOSE'].iloc[0]) * 100
                
                result = {
                    'period_name': period_info['name'],
                    'start_date': period_info['start'],
                    'end_date': period_info['end'],
                    'total_days': total_days,
                    'execution_time': execution_time,
                    
                    # 基础指标
                    'total_return_pct': results.get('总收益率', 0) * 100,
                    'annual_return_pct': results.get('年化收益率', 0) * 100,
                    'max_drawdown_pct': results.get('最大回撤率', 0) * 100,
                    'sharpe_ratio': results.get('夏普比率', 0),
                    'sortino_ratio': results.get('索提诺比率', 0),
                    'calmar_ratio': results.get('Calmar比率', 0),
                    'volatility_pct': results.get('年化波动率', 0) * 100,
                    
                    # 交易指标
                    'total_trades': results.get('总交易次数', 0),
                    'winning_trades': results.get('盈利次数', 0),
                    'losing_trades': results.get('亏损次数', 0),
                    'win_rate': results.get('胜率', 0) * 100,
                    'profit_loss_ratio': results.get('盈亏比', 0),
                    
                    # 市场对比
                    'market_return_pct': market_return,
                    'excess_return_pct': (results.get('总收益率', 0) * 100) - market_return,
                    
                    # 权益曲线
                    'equity_curve': equity_curve,
                    
                    # 原始结果
                    'raw_results': results
                }
                
                print(f"✅ 回测完成!")
                print(f"   总收益率: {result['total_return_pct']:+.2f}%")
                print(f"   年化收益率: {result['annual_return_pct']:+.2f}%")
                print(f"   最大回撤: {result['max_drawdown_pct']:.2f}%")
                print(f"   夏普比率: {result['sharpe_ratio']:.2f}")
                print(f"   交易次数: {result['total_trades']}")
                print(f"   胜率: {result['win_rate']:.1f}%")
                print(f"   市场收益: {result['market_return_pct']:+.2f}%")
                print(f"   超额收益: {result['excess_return_pct']:+.2f}%")
                print(f"   执行时间: {execution_time:.1f}秒")
                
                return result
            else:
                return {'error': '回测失败'}
                
        except Exception as e:
            logger.error(f"回测期间 {period_info['name']} 失败: {e}")
            return {'error': str(e)}
    
    def run_comprehensive_stress_test(self):
        """运行全面压力测试"""
        print("🔥 P01阿尔法X2024策略 - 两年压力测试")
        print("=" * 80)
        print("测试数据: 本地BTCUSDT分钟级数据")
        print("测试期间: 2023年1月 至 2024年12月")
        print("测试目的: 验证策略在不同市场环境下的表现")
        print("=" * 80)
        
        # 运行所有测试期间
        for period in self.test_periods:
            result = self.run_backtest_for_period(period)
            self.results[period['name']] = result
        
        # 生成综合报告
        self.generate_comprehensive_report()
        
        # 生成可视化图表
        self.create_stress_test_charts()
        
        return self.results
    
    def generate_comprehensive_report(self):
        """生成综合压力测试报告"""
        print(f"\n📊 P01阿尔法X2024策略 - 两年压力测试报告")
        print("=" * 100)
        print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 过滤有效结果
        valid_results = {k: v for k, v in self.results.items() if 'error' not in v}
        
        if not valid_results:
            print("❌ 没有有效的测试结果")
            return
        
        # 创建结果表格
        print("📋 各期间详细表现:")
        print("-" * 100)
        
        header = f"{'期间':<15} {'总收益率':<10} {'年化收益率':<10} {'最大回撤':<8} {'夏普比率':<8} {'胜率':<8} {'交易次数':<8} {'超额收益':<10}"
        print(header)
        print("-" * 100)
        
        for period_name, result in valid_results.items():
            row = (f"{period_name:<15} "
                  f"{result['total_return_pct']:>8.2f}% "
                  f"{result['annual_return_pct']:>8.2f}% "
                  f"{result['max_drawdown_pct']:>6.2f}% "
                  f"{result['sharpe_ratio']:>6.2f} "
                  f"{result['win_rate']:>6.1f}% "
                  f"{result['total_trades']:>6d} "
                  f"{result['excess_return_pct']:>8.2f}%")
            print(row)
        
        print()
        
        # 统计分析
        print("📈 统计分析:")
        print("-" * 50)
        
        returns = [r['total_return_pct'] for r in valid_results.values()]
        annual_returns = [r['annual_return_pct'] for r in valid_results.values()]
        drawdowns = [r['max_drawdown_pct'] for r in valid_results.values()]
        sharpe_ratios = [r['sharpe_ratio'] for r in valid_results.values() if not np.isnan(r['sharpe_ratio'])]
        
        print(f"总收益率 - 平均: {np.mean(returns):+.2f}%, 最大: {np.max(returns):+.2f}%, 最小: {np.min(returns):+.2f}%")
        print(f"年化收益率 - 平均: {np.mean(annual_returns):+.2f}%, 最大: {np.max(annual_returns):+.2f}%, 最小: {np.min(annual_returns):+.2f}%")
        print(f"最大回撤 - 平均: {np.mean(drawdowns):.2f}%, 最大: {np.max(drawdowns):.2f}%, 最小: {np.min(drawdowns):.2f}%")
        if sharpe_ratios:
            print(f"夏普比率 - 平均: {np.mean(sharpe_ratios):.2f}, 最大: {np.max(sharpe_ratios):.2f}, 最小: {np.min(sharpe_ratios):.2f}")
        
        # 客户目标达成分析
        print(f"\n🎯 客户目标达成分析:")
        print("-" * 50)
        print("客户目标: 年化收益≥15%, 夏普比率≥2, 最大回撤≤15%")
        print()
        
        target_met_count = 0
        for period_name, result in valid_results.items():
            annual_ok = result['annual_return_pct'] >= 15
            sharpe_ok = result['sharpe_ratio'] >= 2 if not np.isnan(result['sharpe_ratio']) else False
            drawdown_ok = result['max_drawdown_pct'] <= 15
            
            all_targets_met = annual_ok and sharpe_ok and drawdown_ok
            if all_targets_met:
                target_met_count += 1
            
            status = "✅ 达标" if all_targets_met else "❌ 未达标"
            print(f"{period_name}: {status}")
            print(f"  年化收益: {result['annual_return_pct']:+.2f}% {'✅' if annual_ok else '❌'}")
            print(f"  夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'}")
            print(f"  最大回撤: {result['max_drawdown_pct']:.2f}% {'✅' if drawdown_ok else '❌'}")
            print()
        
        target_success_rate = target_met_count / len(valid_results) * 100
        print(f"🏆 目标达成率: {target_met_count}/{len(valid_results)} ({target_success_rate:.1f}%)")
        
        # 压力测试结论
        print(f"\n🔥 压力测试结论:")
        print("-" * 50)
        
        if target_success_rate >= 80:
            print("🏆 优秀: 策略在各种市场环境下表现稳定，强烈推荐使用")
        elif target_success_rate >= 60:
            print("✅ 良好: 策略表现总体良好，推荐使用")
        elif target_success_rate >= 40:
            print("⚠️ 一般: 策略表现一般，需要谨慎使用")
        else:
            print("❌ 较差: 策略表现不稳定，不推荐使用")
        
        # 保存详细报告
        self.save_detailed_report(valid_results)
    
    def create_stress_test_charts(self):
        """创建压力测试可视化图表"""
        try:
            print(f"\n📊 生成压力测试图表...")

            # 过滤有效结果
            valid_results = {k: v for k, v in self.results.items() if 'error' not in v}

            if not valid_results:
                print("❌ 没有有效数据生成图表")
                return

            # 设置图表样式
            plt.style.use('seaborn-v0_8')
            fig = plt.figure(figsize=(20, 16))

            # 1. 各期间收益率对比
            ax1 = plt.subplot(2, 3, 1)
            periods = list(valid_results.keys())
            returns = [valid_results[p]['total_return_pct'] for p in periods]
            colors = ['#2E8B57' if r > 0 else '#DC143C' for r in returns]

            bars = plt.bar(range(len(periods)), returns, color=colors, alpha=0.7)
            plt.title('各期间总收益率对比', fontsize=14, fontweight='bold')
            plt.ylabel('收益率 (%)')
            plt.xticks(range(len(periods)), [p.replace('年', '\n年') for p in periods], rotation=45)
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            plt.axhline(y=15, color='red', linestyle='--', alpha=0.5, label='年化15%目标线')

            # 添加数值标签
            for bar, value in zip(bars, returns):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        f'{value:.1f}%', ha='center', va='bottom')

            plt.legend()
            plt.grid(True, alpha=0.3)

            # 2. 年化收益率 vs 最大回撤散点图
            ax2 = plt.subplot(2, 3, 2)
            annual_returns = [valid_results[p]['annual_return_pct'] for p in periods]
            drawdowns = [valid_results[p]['max_drawdown_pct'] for p in periods]

            scatter = plt.scatter(drawdowns, annual_returns, s=100, alpha=0.7, c=range(len(periods)), cmap='viridis')
            plt.title('年化收益率 vs 最大回撤', fontsize=14, fontweight='bold')
            plt.xlabel('最大回撤 (%)')
            plt.ylabel('年化收益率 (%)')
            plt.axhline(y=15, color='red', linestyle='--', alpha=0.5, label='年化15%目标')
            plt.axvline(x=15, color='red', linestyle='--', alpha=0.5, label='回撤15%限制')

            # 添加期间标签
            for i, period in enumerate(periods):
                plt.annotate(period.replace('年', '年\n'), (drawdowns[i], annual_returns[i]),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)

            plt.legend()
            plt.grid(True, alpha=0.3)

            # 3. 夏普比率对比
            ax3 = plt.subplot(2, 3, 3)
            sharpe_ratios = [valid_results[p]['sharpe_ratio'] for p in periods]
            colors = ['#2E8B57' if s >= 2 else '#FFA500' if s >= 1 else '#DC143C' for s in sharpe_ratios]

            bars = plt.bar(range(len(periods)), sharpe_ratios, color=colors, alpha=0.7)
            plt.title('夏普比率对比', fontsize=14, fontweight='bold')
            plt.ylabel('夏普比率')
            plt.xticks(range(len(periods)), [p.replace('年', '\n年') for p in periods], rotation=45)
            plt.axhline(y=2, color='red', linestyle='--', alpha=0.5, label='夏普2.0目标线')

            # 添加数值标签
            for bar, value in zip(bars, sharpe_ratios):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        f'{value:.1f}', ha='center', va='bottom')

            plt.legend()
            plt.grid(True, alpha=0.3)

            # 4. 胜率 vs 盈亏比
            ax4 = plt.subplot(2, 3, 4)
            win_rates = [valid_results[p]['win_rate'] for p in periods]
            profit_loss_ratios = [valid_results[p]['profit_loss_ratio'] for p in periods if not np.isnan(valid_results[p]['profit_loss_ratio'])]

            if len(profit_loss_ratios) == len(win_rates):
                scatter = plt.scatter(win_rates, profit_loss_ratios, s=100, alpha=0.7, c=range(len(periods)), cmap='plasma')
                plt.title('胜率 vs 盈亏比', fontsize=14, fontweight='bold')
                plt.xlabel('胜率 (%)')
                plt.ylabel('盈亏比')

                # 添加期间标签
                for i, period in enumerate(periods):
                    if i < len(profit_loss_ratios):
                        plt.annotate(period.replace('年', '年\n'), (win_rates[i], profit_loss_ratios[i]),
                                   xytext=(5, 5), textcoords='offset points', fontsize=8)

            plt.grid(True, alpha=0.3)

            # 5. 交易次数对比
            ax5 = plt.subplot(2, 3, 5)
            trade_counts = [valid_results[p]['total_trades'] for p in periods]

            bars = plt.bar(range(len(periods)), trade_counts, color='skyblue', alpha=0.7)
            plt.title('交易次数对比', fontsize=14, fontweight='bold')
            plt.ylabel('交易次数')
            plt.xticks(range(len(periods)), [p.replace('年', '\n年') for p in periods], rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, trade_counts):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        f'{value}', ha='center', va='bottom')

            plt.grid(True, alpha=0.3)

            # 6. 客户目标达成情况
            ax6 = plt.subplot(2, 3, 6)

            target_scores = []
            for period in periods:
                result = valid_results[period]
                score = 0
                if result['annual_return_pct'] >= 15:
                    score += 1
                if result['sharpe_ratio'] >= 2:
                    score += 1
                if result['max_drawdown_pct'] <= 15:
                    score += 1
                target_scores.append(score)

            colors = ['#2E8B57' if s == 3 else '#FFA500' if s == 2 else '#DC143C' for s in target_scores]
            bars = plt.bar(range(len(periods)), target_scores, color=colors, alpha=0.7)
            plt.title('客户目标达成情况', fontsize=14, fontweight='bold')
            plt.ylabel('达成目标数 (满分3)')
            plt.xticks(range(len(periods)), [p.replace('年', '\n年') for p in periods], rotation=45)
            plt.ylim(0, 3)

            # 添加数值标签
            for bar, value in zip(bars, target_scores):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                        f'{value}/3', ha='center', va='bottom')

            plt.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            chart_filename = f"P01阿尔法X2024_压力测试图表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            print(f"📊 压力测试图表已保存: {chart_filename}")

            plt.show()

        except Exception as e:
            logger.error(f"生成图表失败: {e}")

    def save_detailed_report(self, valid_results: dict):
        """保存详细报告到文件"""
        try:
            # 创建DataFrame
            report_data = []
            for period_name, result in valid_results.items():
                report_data.append({
                    '测试期间': period_name,
                    '开始日期': result['start_date'],
                    '结束日期': result['end_date'],
                    '测试天数': result['total_days'],
                    '总收益率(%)': result['total_return_pct'],
                    '年化收益率(%)': result['annual_return_pct'],
                    '最大回撤(%)': result['max_drawdown_pct'],
                    '夏普比率': result['sharpe_ratio'],
                    '索提诺比率': result['sortino_ratio'],
                    'Calmar比率': result['calmar_ratio'],
                    '年化波动率(%)': result['volatility_pct'],
                    '总交易次数': result['total_trades'],
                    '盈利次数': result['winning_trades'],
                    '亏损次数': result['losing_trades'],
                    '胜率(%)': result['win_rate'],
                    '盈亏比': result['profit_loss_ratio'],
                    '市场收益率(%)': result['market_return_pct'],
                    '超额收益(%)': result['excess_return_pct'],
                    '执行时间(秒)': result['execution_time']
                })

            df = pd.DataFrame(report_data)

            # 保存到CSV
            filename = f"P01阿尔法X2024_两年压力测试报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"📄 详细报告已保存: {filename}")

        except Exception as e:
            logger.error(f"保存报告失败: {e}")

def main():
    """主函数"""
    print("🚀 启动P01阿尔法X2024策略两年压力测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        print("请确保本地BTCUSDT历史数据已正确放置")
        return
    
    # 创建测试器
    stress_tester = AlphaX2024StressTest()
    
    # 运行压力测试
    start_time = time.time()
    results = stress_tester.run_comprehensive_stress_test()
    end_time = time.time()
    
    print(f"\n🎉 压力测试完成!")
    print(f"⏱️ 总耗时: {(end_time - start_time)/60:.1f}分钟")
    print(f"📊 测试期间数: {len([r for r in results.values() if 'error' not in r])}")

if __name__ == "__main__":
    main()
