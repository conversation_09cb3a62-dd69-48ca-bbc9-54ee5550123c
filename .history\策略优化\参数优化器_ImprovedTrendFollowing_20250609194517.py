# -*- coding: utf-8 -*-
"""
ImprovedTrendFollowing策略参数优化器
使用网格搜索和贝叶斯优化寻找最优参数组合
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import logging
from itertools import product
import time
from typing import Dict, List, Tuple, Any
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self):
        self.optimization_results = []
        self.best_params = None
        self.best_score = -float('inf')
        
    def define_parameter_space(self) -> Dict[str, List]:
        """定义参数搜索空间"""
        return {
            # 技术指标参数
            'sma_short': [8, 10, 12, 15],
            'sma_long': [20, 26, 30, 35],
            'rsi_overbought': [70, 75, 80],
            'rsi_oversold': [20, 25, 30],
            
            # 信号确认参数
            'adx_threshold': [25, 30, 35, 40],
            'adx_rising_periods': [2, 3, 4],
            'min_momentum_threshold': [0.001, 0.002, 0.003],
            'volume_threshold': [1.2, 1.5, 2.0],
            
            # 风险管理参数
            'risk_per_trade_pct': [0.005, 0.008, 0.01, 0.015],
            'initial_sl_atr_multiple': [2.0, 2.5, 3.0],
            'trailing_sl_atr_multiple': [1.5, 1.8, 2.0],
            'tp_level_1': [1.5, 2.0, 2.5],
            'tp_level_2': [3.0, 4.0, 5.0],
            
            # 时间控制参数
            'min_signal_interval_minutes': [60, 120, 180],
            'max_holding_hours': [24, 48, 72]
        }
    
    def calculate_fitness_score(self, results: Dict) -> float:
        """计算适应度分数"""
        try:
            # 提取关键指标
            total_return = results.get('总收益率', 0)
            sharpe_ratio = results.get('夏普比率', 0)
            max_drawdown = results.get('最大回撤率', 1)
            win_rate = results.get('胜率', 0)
            profit_loss_ratio = results.get('盈亏比', 0)
            total_trades = results.get('总交易次数', 0)
            
            # 处理异常值
            if np.isnan(sharpe_ratio) or np.isinf(sharpe_ratio):
                sharpe_ratio = -10
            if np.isnan(profit_loss_ratio) or np.isinf(profit_loss_ratio):
                profit_loss_ratio = 0
            
            # 综合评分公式
            # 权重: 收益率30%, 夏普比率25%, 胜率20%, 盈亏比15%, 回撤控制10%
            score = (
                total_return * 0.30 +
                min(sharpe_ratio, 5) * 0.25 +  # 限制夏普比率最大值
                win_rate * 0.20 +
                min(profit_loss_ratio, 5) * 0.15 +
                max(0, (0.1 - max_drawdown)) * 0.10  # 回撤越小分数越高
            )
            
            # 交易次数惩罚 - 太少或太多都不好
            if total_trades < 5:
                score *= 0.5  # 交易次数太少
            elif total_trades > 50:
                score *= 0.8  # 交易次数太多
            
            return score
            
        except Exception as e:
            logger.warning(f"计算适应度分数失败: {e}")
            return -float('inf')
    
    def grid_search_optimization(self, max_combinations: int = 100) -> Dict:
        """网格搜索优化"""
        print("🔍 开始网格搜索参数优化...")
        print("=" * 60)
        
        try:
            from 模拟回测引擎_分钟级 import MinuteEventBacktester
            from 配置.系统配置 import Config
            from 核心代码.交易策略.策略库 import STRATEGIES
            
            # 创建配置
            config = Config()
            config.start_date = '2025-04-01'
            config.end_date = '2025-04-06'  # 较短期间用于快速优化
            config.initial_cash = 100000
            config.cost = 0.0005
            config.crypto_pairs = ['BTCUSDT']
            
            # 获取策略类
            strategy_class = STRATEGIES.get('ImprovedTrendFollowingStrategy')
            if not strategy_class:
                raise ValueError("ImprovedTrendFollowingStrategy策略不可用")
            
            # 定义参数空间
            param_space = self.define_parameter_space()
            
            # 生成参数组合
            param_names = list(param_space.keys())
            param_values = list(param_space.values())
            
            # 限制组合数量
            all_combinations = list(product(*param_values))
            if len(all_combinations) > max_combinations:
                # 随机采样
                import random
                random.seed(42)
                combinations = random.sample(all_combinations, max_combinations)
            else:
                combinations = all_combinations
            
            print(f"📊 总参数组合数: {len(combinations)}")
            print(f"⏱️ 预计耗时: {len(combinations) * 0.5:.1f}分钟")
            print()
            
            best_score = -float('inf')
            best_params = None
            results_list = []
            
            for i, param_combo in enumerate(combinations):
                try:
                    # 构建参数字典
                    params = dict(zip(param_names, param_combo))
                    
                    # 参数合理性检查
                    if params['sma_short'] >= params['sma_long']:
                        continue
                    if params['tp_level_1'] >= params['tp_level_2']:
                        continue
                    if params['initial_sl_atr_multiple'] <= params['trailing_sl_atr_multiple']:
                        continue
                    
                    print(f"[{i+1}/{len(combinations)}] 测试参数组合...")
                    
                    # 运行回测
                    backtester = MinuteEventBacktester(config, strategy_class, params)
                    results = backtester.run_backtest(config.start_date, config.end_date)
                    
                    if results:
                        # 计算适应度分数
                        score = self.calculate_fitness_score(results)
                        
                        # 记录结果
                        result_record = {
                            'params': params.copy(),
                            'score': score,
                            'total_return': results.get('总收益率', 0),
                            'sharpe_ratio': results.get('夏普比率', 0),
                            'max_drawdown': results.get('最大回撤率', 0),
                            'win_rate': results.get('胜率', 0),
                            'profit_loss_ratio': results.get('盈亏比', 0),
                            'total_trades': results.get('总交易次数', 0)
                        }
                        results_list.append(result_record)
                        
                        # 更新最佳结果
                        if score > best_score:
                            best_score = score
                            best_params = params.copy()
                            print(f"🎯 发现更优参数! 分数: {score:.4f}")
                            print(f"   收益率: {results.get('总收益率', 0)*100:.2f}%")
                            print(f"   夏普比率: {results.get('夏普比率', 0):.2f}")
                            print(f"   胜率: {results.get('胜率', 0)*100:.1f}%")
                        
                        if i % 10 == 0:
                            print(f"   当前分数: {score:.4f}")
                    
                except Exception as e:
                    logger.warning(f"参数组合 {i+1} 测试失败: {e}")
                    continue
            
            # 保存优化结果
            self.optimization_results = results_list
            self.best_params = best_params
            self.best_score = best_score
            
            # 生成优化报告
            self.generate_optimization_report()
            
            return {
                'best_params': best_params,
                'best_score': best_score,
                'total_tested': len(results_list),
                'results': results_list
            }
            
        except Exception as e:
            logger.error(f"网格搜索优化失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def generate_optimization_report(self):
        """生成优化报告"""
        if not self.optimization_results:
            return
        
        print("\n📊 参数优化报告")
        print("=" * 80)
        
        # 排序结果
        sorted_results = sorted(self.optimization_results, key=lambda x: x['score'], reverse=True)
        
        print(f"🏆 最优参数组合 (分数: {self.best_score:.4f}):")
        print("-" * 50)
        for param, value in self.best_params.items():
            print(f"  {param}: {value}")
        
        print(f"\n📈 最优结果:")
        best_result = sorted_results[0]
        print(f"  总收益率: {best_result['total_return']*100:.2f}%")
        print(f"  夏普比率: {best_result['sharpe_ratio']:.2f}")
        print(f"  最大回撤: {best_result['max_drawdown']*100:.2f}%")
        print(f"  胜率: {best_result['win_rate']*100:.1f}%")
        print(f"  盈亏比: {best_result['profit_loss_ratio']:.2f}")
        print(f"  交易次数: {best_result['total_trades']}")
        
        print(f"\n🔝 前5名参数组合:")
        print("-" * 50)
        for i, result in enumerate(sorted_results[:5]):
            print(f"{i+1}. 分数: {result['score']:.4f} | "
                  f"收益: {result['total_return']*100:.1f}% | "
                  f"夏普: {result['sharpe_ratio']:.2f} | "
                  f"胜率: {result['win_rate']*100:.1f}%")
        
        # 保存详细结果到文件
        try:
            results_df = pd.DataFrame(self.optimization_results)
            results_df = results_df.sort_values('score', ascending=False)
            
            # 展开参数列
            params_df = pd.json_normalize(results_df['params'])
            final_df = pd.concat([params_df, results_df.drop('params', axis=1)], axis=1)
            
            output_file = "策略优化/参数优化结果_ImprovedTrendFollowing.csv"
            final_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 详细优化结果已保存: {output_file}")
            
            # 保存最优参数
            best_params_file = "策略优化/最优参数_ImprovedTrendFollowing.json"
            with open(best_params_file, 'w', encoding='utf-8') as f:
                json.dump(self.best_params, f, indent=2, ensure_ascii=False)
            print(f"💾 最优参数已保存: {best_params_file}")
            
        except Exception as e:
            logger.warning(f"保存优化结果失败: {e}")

def main():
    """主函数"""
    print("🚀 ImprovedTrendFollowing策略参数优化")
    print("=" * 60)
    
    optimizer = ParameterOptimizer()
    
    # 执行网格搜索优化
    start_time = time.time()
    results = optimizer.grid_search_optimization(max_combinations=50)  # 限制50个组合用于演示
    end_time = time.time()
    
    if results:
        print(f"\n✅ 参数优化完成!")
        print(f"⏱️ 总耗时: {(end_time - start_time)/60:.1f}分钟")
        print(f"🎯 最优分数: {results['best_score']:.4f}")
        print(f"📊 测试组合数: {results['total_tested']}")
        
        return results['best_params']
    else:
        print("❌ 参数优化失败")
        return None

if __name__ == "__main__":
    best_params = main()
