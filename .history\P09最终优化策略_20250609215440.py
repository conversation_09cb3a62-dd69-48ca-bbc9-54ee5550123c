# -*- coding: utf-8 -*-
"""
P09最终优化策略
基于P08的成功基础，针对胜率和盈亏比进行最终优化
目标：在保持低风险的前提下，提高胜率和盈亏比
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class FinalOptimizedStrategy:
    """P09最终优化策略 - 胜率和盈亏比的最终优化"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 最终优化的策略参数
        self.strategy_params = {
            # 技术指标参数
            'ema_fast': 12,                # 快速EMA
            'ema_slow': 26,                # 慢速EMA
            'rsi_period': 14,              # RSI周期
            'macd_signal': 9,              # MACD信号线
            
            # 优化的信号阈值 (提高胜率)
            'long_rsi_min': 40,            # 做多RSI最小值
            'long_rsi_max': 70,            # 做多RSI最大值
            'long_momentum_min': 0.004,    # 做多最小动量 (提高)
            'long_volume_min': 1.1,        # 做多最小成交量比 (提高)
            
            'short_rsi_min': 30,           # 做空RSI最小值
            'short_rsi_max': 60,           # 做空RSI最大值
            'short_momentum_max': -0.004,  # 做空最大动量 (提高)
            'short_volume_min': 1.1,       # 做空最小成交量比 (提高)
            
            # 信号质量控制
            'min_signal_strength': 1.0,    # 最小信号强度
            'signal_confirmation_bars': 2, # 信号确认K线数
            'max_signals_per_day': 6,      # 每日最大信号数
            
            # 仓位管理
            'base_position': 0.25,         # 基础仓位 (提高)
            'max_position': 0.4,           # 最大仓位
            'risk_per_trade': 0.02,        # 每笔风险
            
            # 优化的风控参数 (改善盈亏比)
            'stop_loss_base': 0.02,        # 基础止损2% (收紧)
            'take_profit_base': 0.08,      # 基础止盈8% (扩大，4:1盈亏比)
            'trailing_stop': True,         # 移动止损
            'profit_protection': 0.6,      # 盈利保护60%
            
            # 交易控制
            'min_signal_gap': 90,          # 最小信号间隔1.5小时
            'max_holding_hours': 16,       # 最大持仓16小时 (延长)
            
            # 新增：趋势过滤
            'trend_filter': True,          # 启用趋势过滤
            'trend_period': 50,            # 趋势周期
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_final_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算最终优化的技术指标"""
        try:
            df = data.copy()
            
            # EMA系统
            df['EMA_Fast'] = df['CLOSE'].ewm(span=self.strategy_params['ema_fast']).mean()
            df['EMA_Slow'] = df['CLOSE'].ewm(span=self.strategy_params['ema_slow']).mean()
            
            # 趋势过滤
            if self.strategy_params['trend_filter']:
                df['EMA_Trend'] = df['CLOSE'].ewm(span=self.strategy_params['trend_period']).mean()
            
            # MACD
            df['MACD'] = df['EMA_Fast'] - df['EMA_Slow']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['macd_signal']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 动量指标
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            # 价格强度
            df['Price_Strength'] = (df['CLOSE'] - df['CLOSE'].shift(10)) / df['CLOSE'].shift(10)
            
            return df
            
        except Exception as e:
            logger.warning(f"计算最终指标失败: {e}")
            return data
    
    def generate_final_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成最终优化的交易信号"""
        try:
            # 初始化信号
            df['Long_Signal'] = 0
            df['Short_Signal'] = 0
            df['Signal_Quality'] = 0
            
            # 做多信号 (高质量)
            long_base = (
                # 基础趋势
                (df['EMA_Fast'] > df['EMA_Slow']) &
                (df['MACD'] > df['MACD_Signal']) &
                (df['MACD_Histogram'] > 0) &
                
                # RSI条件
                (df['RSI'] >= self.strategy_params['long_rsi_min']) &
                (df['RSI'] <= self.strategy_params['long_rsi_max']) &
                
                # 动量条件 (更严格)
                (df['Momentum_5'] > self.strategy_params['long_momentum_min']) &
                (df['Momentum_15'] > 0) &
                
                # 成交量条件
                (df['Volume_Ratio'] > self.strategy_params['long_volume_min']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.008) & (df['Volatility'] < 0.08) &
                
                # 价格强度
                (df['Price_Strength'] > 0.01)
            )
            
            # 趋势过滤
            if self.strategy_params['trend_filter']:
                long_base = long_base & (df['CLOSE'] > df['EMA_Trend'])
            
            # 做空信号 (高质量)
            short_base = (
                # 基础趋势
                (df['EMA_Fast'] < df['EMA_Slow']) &
                (df['MACD'] < df['MACD_Signal']) &
                (df['MACD_Histogram'] < 0) &
                
                # RSI条件
                (df['RSI'] >= self.strategy_params['short_rsi_min']) &
                (df['RSI'] <= self.strategy_params['short_rsi_max']) &
                
                # 动量条件 (更严格)
                (df['Momentum_5'] < self.strategy_params['short_momentum_max']) &
                (df['Momentum_15'] < 0) &
                
                # 成交量条件
                (df['Volume_Ratio'] > self.strategy_params['short_volume_min']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.008) & (df['Volatility'] < 0.08) &
                
                # 价格强度
                (df['Price_Strength'] < -0.01)
            )
            
            # 趋势过滤
            if self.strategy_params['trend_filter']:
                short_base = short_base & (df['CLOSE'] < df['EMA_Trend'])
            
            # 信号确认
            confirm_bars = self.strategy_params['signal_confirmation_bars']
            
            # 做多信号确认
            for i in range(confirm_bars, len(df)):
                if long_base.iloc[i] and long_base.iloc[i-1]:
                    df.iloc[i, df.columns.get_loc('Long_Signal')] = 1
            
            # 做空信号确认
            for i in range(confirm_bars, len(df)):
                if short_base.iloc[i] and short_base.iloc[i-1]:
                    df.iloc[i, df.columns.get_loc('Short_Signal')] = 1
            
            # 计算信号质量 (更精确)
            df['Signal_Quality'] = (
                abs(df['MACD_Histogram']) * 800 * 0.3 +
                abs(df['Momentum_5']) * 100 * 0.25 +
                abs(df['Price_Strength']) * 100 * 0.2 +
                (df['Volume_Ratio'] - 1) * 0.15 +
                (50 - abs(df['RSI'] - 50)) / 50 * 0.1
            )
            
            return df
            
        except Exception as e:
            logger.error(f"生成最终信号失败: {e}")
            return df

    def simulate_final_strategy(self, data: pd.DataFrame) -> dict:
        """模拟最终优化策略"""
        try:
            print("🔄 模拟P09最终优化策略...")

            # 计算技术指标
            data = self.calculate_final_indicators(data)
            data = self.generate_final_signals(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            long_position = 0
            short_position = 0
            trades = []
            equity_curve = [initial_capital]

            # 交易控制变量
            last_trade_time = None
            long_entry_time = None
            short_entry_time = None
            long_entry_price = 0
            short_entry_price = 0
            daily_signals = {}

            for i in range(60, len(data)):  # 从第60个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']

                # 计算当前权益
                long_value = long_position * current_price if long_position > 0 else 0
                short_value = short_position * (2 * short_entry_price - current_price) if short_position > 0 else 0
                current_equity = cash + long_value + short_value
                equity_curve.append(current_equity)

                # 检查数据有效性
                if pd.isna(current_price):
                    continue

                # 每日信号限制
                current_date = current_time.date()
                if current_date not in daily_signals:
                    daily_signals[current_date] = 0

                if daily_signals[current_date] >= self.strategy_params['max_signals_per_day']:
                    continue

                # 检查信号间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_signal_gap']:
                        continue

                # 获取信号
                long_signal = current_data.get('Long_Signal', 0)
                short_signal = current_data.get('Short_Signal', 0)
                signal_quality = current_data.get('Signal_Quality', 0)

                # 信号质量过滤
                if signal_quality < self.strategy_params['min_signal_strength']:
                    continue

                # 检查做多信号
                if long_signal == 1:
                    # 平空头仓位
                    if short_position > 0:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': '反向信号平仓'
                        })

                        short_position = 0
                        short_entry_time = None

                    # 开多头仓位
                    if long_position == 0:
                        position_size = self.strategy_params['base_position']

                        # 根据信号质量调整仓位
                        if signal_quality > 2.0:
                            position_size = min(position_size * 1.3, self.strategy_params['max_position'])

                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            long_position = shares
                            cash -= shares * current_price * 1.0005
                            long_entry_time = current_time
                            long_entry_price = current_price

                            # 动态止损止盈
                            volatility = current_data.get('Volatility', 0.02)
                            stop_loss_pct = self.strategy_params['stop_loss_base'] * (1 + volatility * 2)
                            take_profit_pct = self.strategy_params['take_profit_base'] * (1 + volatility)

                            trades.append({
                                'time': current_time,
                                'action': 'buy_long',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'stop_loss': current_price * (1 - stop_loss_pct),
                                'take_profit': current_price * (1 + take_profit_pct),
                                'volatility': volatility
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 检查做空信号
                elif short_signal == 1:
                    # 平多头仓位
                    if long_position > 0:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': '反向信号平仓'
                        })

                        long_position = 0
                        long_entry_time = None

                    # 开空头仓位
                    if short_position == 0:
                        position_size = self.strategy_params['base_position']

                        # 根据信号质量调整仓位
                        if signal_quality > 2.0:
                            position_size = min(position_size * 1.3, self.strategy_params['max_position'])

                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            short_position = shares
                            cash -= shares * current_price * 1.0005
                            short_entry_time = current_time
                            short_entry_price = current_price

                            # 动态止损止盈
                            volatility = current_data.get('Volatility', 0.02)
                            stop_loss_pct = self.strategy_params['stop_loss_base'] * (1 + volatility * 2)
                            take_profit_pct = self.strategy_params['take_profit_base'] * (1 + volatility)

                            trades.append({
                                'time': current_time,
                                'action': 'sell_short',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'stop_loss': current_price * (1 + stop_loss_pct),
                                'take_profit': current_price * (1 - take_profit_pct),
                                'volatility': volatility
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 优化的多头出场逻辑
                if long_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'buy_long'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 高级移动止损
                    if self.strategy_params['trailing_stop']:
                        profit_pct = (current_price - long_entry_price) / long_entry_price
                        if profit_pct > 0.03:  # 盈利3%以上
                            protection_price = long_entry_price * (1 + profit_pct * self.strategy_params['profit_protection'])
                            stop_loss = max(stop_loss, protection_price)

                    holding_hours = (current_time - long_entry_time).total_seconds() / 3600

                    should_exit = False
                    exit_reason = ""

                    if current_price <= stop_loss:
                        should_exit = True
                        exit_reason = "多头止损"
                    elif current_price >= take_profit:
                        should_exit = True
                        exit_reason = "多头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "多头超时"

                    if should_exit:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'pnl_pct': long_pnl / (long_entry_price * long_position) * 100,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        long_position = 0
                        long_entry_time = None
                        last_trade_time = current_time

                # 优化的空头出场逻辑
                if short_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'sell_short'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 高级移动止损
                    if self.strategy_params['trailing_stop']:
                        profit_pct = (short_entry_price - current_price) / short_entry_price
                        if profit_pct > 0.03:  # 盈利3%以上
                            protection_price = short_entry_price * (1 - profit_pct * self.strategy_params['profit_protection'])
                            stop_loss = min(stop_loss, protection_price)

                    holding_hours = (current_time - short_entry_time).total_seconds() / 3600

                    should_exit = False
                    exit_reason = ""

                    if current_price >= stop_loss:
                        should_exit = True
                        exit_reason = "空头止损"
                    elif current_price <= take_profit:
                        should_exit = True
                        exit_reason = "空头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "空头超时"

                    if should_exit:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'pnl_pct': short_pnl / (short_entry_price * short_position) * 100,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        short_position = 0
                        short_entry_time = None
                        last_trade_time = current_time

            # 期末平仓
            final_price = data['CLOSE'].iloc[-1]

            if long_position > 0:
                long_pnl = (final_price - long_entry_price) * long_position
                cash += long_position * final_price * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell_long',
                    'price': final_price,
                    'shares': long_position,
                    'pnl': long_pnl,
                    'reason': '期末平仓'
                })

            if short_position > 0:
                short_pnl = short_position * (short_entry_price - final_price)
                cash += short_position * short_entry_price + short_pnl * 0.9995
                trades.append({
                    'time': data.index[-1],
                    'action': 'cover_short',
                    'price': final_price,
                    'shares': short_position,
                    'pnl': short_pnl,
                    'reason': '期末平仓'
                })

            final_equity = cash

            # 计算统计指标
            entry_trades = [t for t in trades if t['action'] in ['buy_long', 'sell_short']]
            exit_trades = [t for t in trades if t.get('pnl') is not None]

            total_trades = len(entry_trades)
            long_trades = len([t for t in trades if t['action'] == 'buy_long'])
            short_trades = len([t for t in trades if t['action'] == 'sell_short'])

            profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in exit_trades if t.get('pnl', 0) < 0])

            win_rate = profitable_trades / (profitable_trades + losing_trades) if (profitable_trades + losing_trades) > 0 else 0

            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in exit_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in exit_trades if t.get('pnl', 0) < 0]

            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital

            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            print(f"✅ P09最终优化策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   总交易次数: {total_trades} (多头:{long_trades}, 空头:{short_trades})")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")

            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'long_trades': long_trades,
                'short_trades': short_trades,
                'winning_trades': profitable_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }

        except Exception as e:
            logger.error(f"P09最终优化策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P09最终优化策略测试...")

    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return

    strategy = FinalOptimizedStrategy()

    print("\n🎯 P09最终优化策略特点:")
    print("=" * 70)
    print("  1. 🎯 针对胜率和盈亏比的最终优化")
    print("  2. 📊 4:1盈亏比设计 (2%止损, 8%止盈)")
    print("  3. 🔍 更严格的信号质量过滤")
    print("  4. 📈 趋势过滤确保方向正确")
    print("  5. 🛡️ 高级移动止损和盈利保护")
    print("  6. ⚖️ 平衡的交易频率控制")
    print("=" * 70)

    # 测试2024年4月
    try:
        print("\n📊 开始最终优化测试...")
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_final_strategy(data)

        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1

            print(f"\n📊 P09最终优化策略测试结果:")
            print("=" * 70)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"总交易次数: {result['total_trades']}")
            print(f"多头交易: {result['long_trades']}次")
            print(f"空头交易: {result['short_trades']}次")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            print("=" * 70)

            # 完整策略进化历程
            print(f"\n📈 完整策略进化历程:")
            print("-" * 60)
            print(f"P05简单策略: -16.24% (435次, 胜率53%, 盈亏比0.73)")
            print(f"P06双向策略: -7.95% (227次, 胜率37%, 盈亏比1.40)")
            print(f"P07优化策略: +0.00% (0次, 无交易)")
            print(f"P08平衡策略: -0.96% (3次, 胜率33%, 盈亏比0.74)")
            print(f"P09最终策略: {result['total_return']*100:+.2f}% ({result['total_trades']}次, 胜率{result['win_rate']*100:.1f}%, 盈亏比{result['profit_loss_ratio']:.2f})")

            # 关键改进分析
            print(f"\n🎯 最终改进分析:")
            print("-" * 50)

            # 与P05对比
            improvement_vs_p05 = result['total_return'] - (-0.1624)
            trade_reduction = 435 - result['total_trades']

            print(f"相比P05简单策略:")
            print(f"  收益改进: {improvement_vs_p05*100:+.2f}个百分点")
            print(f"  交易减少: {trade_reduction}次 (质量大幅提升)")
            print(f"  风险降低: 回撤从16%降至{result['max_drawdown']*100:.2f}%")

            # 与P08对比
            improvement_vs_p08 = result['total_return'] - (-0.0096)
            winrate_change = result['win_rate'] - 0.333
            ratio_change = result['profit_loss_ratio'] - 0.74

            print(f"\n相比P08平衡策略:")
            print(f"  收益改进: {improvement_vs_p08*100:+.2f}个百分点")
            print(f"  胜率变化: {winrate_change*100:+.1f}个百分点")
            print(f"  盈亏比改进: {ratio_change:+.2f}")

            # 客户目标最终检查
            print(f"\n🎯 客户目标最终达成检查:")
            print("=" * 60)

            has_trades = result['total_trades'] > 0
            is_profitable = result['total_return'] > 0
            excellent_annual = annual_return >= 0.15
            good_annual = annual_return >= 0.10
            decent_annual = annual_return >= 0.05
            excellent_winrate = result['win_rate'] >= 0.60
            good_winrate = result['win_rate'] >= 0.50
            decent_winrate = result['win_rate'] >= 0.40
            excellent_sharpe = result['sharpe_ratio'] >= 2.0
            good_sharpe = result['sharpe_ratio'] >= 1.0
            decent_sharpe = result['sharpe_ratio'] >= 0.5
            excellent_drawdown = result['max_drawdown'] <= 0.15
            good_drawdown = result['max_drawdown'] <= 0.20
            excellent_ratio = result['profit_loss_ratio'] >= 2.0
            good_ratio = result['profit_loss_ratio'] >= 1.5
            decent_ratio = result['profit_loss_ratio'] >= 1.0

            print(f"✅ 产生交易: {result['total_trades']}次 {'🎉' if has_trades else '❌'}")
            print(f"✅ 月度盈利: {result['total_return']*100:+.2f}% {'🎉' if is_profitable else '❌'}")
            print(f"📈 年化收益: {annual_return*100:+.2f}% {'🎉' if excellent_annual else '✅' if good_annual else '⚠️' if decent_annual else '❌'} (目标≥15%)")
            print(f"🎯 胜率: {result['win_rate']*100:.1f}% {'🎉' if excellent_winrate else '✅' if good_winrate else '⚠️' if decent_winrate else '❌'} (目标≥60%)")
            print(f"📊 夏普比率: {result['sharpe_ratio']:.2f} {'🎉' if excellent_sharpe else '✅' if good_sharpe else '⚠️' if decent_sharpe else '❌'} (目标≥2.0)")
            print(f"🛡️ 最大回撤: {result['max_drawdown']*100:.2f}% {'🎉' if excellent_drawdown else '✅' if good_drawdown else '❌'} (目标≤15%)")
            print(f"⚖️ 盈亏比: {result['profit_loss_ratio']:.2f} {'🎉' if excellent_ratio else '✅' if good_ratio else '⚠️' if decent_ratio else '❌'} (目标≥1.5)")

            # 最终综合评价
            excellent_count = sum([is_profitable, excellent_annual, excellent_winrate, excellent_sharpe, excellent_drawdown, excellent_ratio])
            good_count = sum([has_trades, is_profitable, good_annual, good_winrate, good_sharpe, good_drawdown, good_ratio])
            decent_count = sum([has_trades, is_profitable, decent_annual, decent_winrate, decent_sharpe, good_drawdown, decent_ratio])

            print(f"\n🏆 P09最终优化策略综合评价:")
            print("=" * 60)

            if excellent_count >= 5:
                print("🎉🎉🎉 完美表现! 策略完全达到客户目标!")
                evaluation = "完美"
            elif excellent_count >= 3:
                print("🎉🎉 卓越表现! 策略基本达到客户目标!")
                evaluation = "卓越"
            elif good_count >= 5:
                print("🎉 优秀表现! 策略显著改进，接近目标!")
                evaluation = "优秀"
            elif good_count >= 3:
                print("✅ 良好表现! 策略有明显改进!")
                evaluation = "良好"
            elif decent_count >= 4:
                print("⚠️ 一般表现! 策略有所改进!")
                evaluation = "一般"
            else:
                print("❌ 需要继续优化!")
                evaluation = "需要优化"

            # 最终总结
            print(f"\n🎯 最终总结:")
            print("=" * 60)
            print(f"📊 策略评级: {evaluation}")
            print(f"🔄 双向交易: {'✅ 已实现' if result['long_trades'] > 0 and result['short_trades'] > 0 else '❌ 未实现'}")
            print(f"📈 收益改进: 从-16.24%提升至{result['total_return']*100:+.2f}% (改进{improvement_vs_p05*100:.2f}个百分点)")
            print(f"🛡️ 风险控制: 回撤控制在{result['max_drawdown']*100:.2f}%以内")
            print(f"🎯 交易质量: 从435次降至{result['total_trades']}次，质量大幅提升")

            if is_profitable:
                print(f"🎉 重大突破: 实现月度盈利!")

            print(f"\n💡 下一步建议:")
            if result['total_trades'] < 5:
                print("  - 适当放宽信号条件，增加交易机会")
            if result['win_rate'] < 0.5:
                print("  - 继续优化信号质量，提高胜率")
            if result['profit_loss_ratio'] < 1.5:
                print("  - 调整止盈止损比例，改善盈亏比")
            if is_profitable and result['max_drawdown'] < 0.1:
                print("  - 策略表现良好，可考虑扩大测试周期")

    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
