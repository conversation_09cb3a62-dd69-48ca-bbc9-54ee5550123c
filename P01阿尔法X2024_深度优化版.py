# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 深度优化版
解决原版策略无法产生交易信号的问题，争取达到客户目标
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class OptimizedAlphaX2024Strategy:
    """P01阿尔法X2024策略 - 深度优化版"""

    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"

        # 大幅放宽的策略参数 - 确保能产生交易信号
        self.strategy_params = {
            # 基础技术指标参数
            'sma_short': 10,           # 短期均线 (降低)
            'sma_long': 20,            # 长期均线 (降低)
            'rsi_period': 14,          # RSI周期
            'atr_period': 14,          # ATR周期
            'adx_period': 14,          # ADX周期

            # 信号生成条件 (大幅放宽)
            'adx_threshold': 15,       # ADX阈值 (从25降到15)
            'rsi_oversold': 45,        # RSI超卖 (从30提高到45)
            'rsi_overbought': 65,      # RSI超买 (从70降到65)

            # 风险管理参数
            'risk_per_trade': 0.02,    # 每笔交易风险2%
            'atr_sl_multiple': 1.5,    # 止损倍数 (降低)
            'atr_tp_multiple': 3.0,    # 止盈倍数 (降低)
            'max_position_size': 0.8,  # 最大仓位80%

            # 交易频率控制
            'min_signal_interval': 30, # 最小信号间隔30分钟 (大幅降低)
            'max_daily_trades': 10,    # 每日最大交易次数

            # 趋势跟踪参数
            'trend_multiplier': 1.2,   # 趋势加仓倍数
            'momentum_threshold': 0.005, # 动量阈值 (降低)

            # 动态调整参数
            'volatility_adjustment': True,  # 启用波动率调整
            'adaptive_thresholds': True,    # 启用自适应阈值
        }

        self.results = {}

    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")

            # 获取日期范围内的所有文件
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)

            all_data = []
            current_date = start_dt

            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month

                # 构建文件路径
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)

                if os.path.exists(filepath):
                    print(f"  加载: {filename}")

                    # 读取数据
                    df = pd.read_csv(filepath)

                    # 标准化列名
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])

                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)

                    # 重命名列以匹配策略需求
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH',
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)

                    # 过滤日期范围
                    df = df[(df.index >= start_date) & (df.index <= end_date)]

                    if not df.empty:
                        all_data.append(df)

                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)

            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")

            # 合并所有数据
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()

            # 去重
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]

            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            print(f"   时间范围: {combined_data.index[0]} 至 {combined_data.index[-1]}")

            return combined_data

        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise

    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()

            # 移动平均线
            df['SMA_Short'] = df['CLOSE'].rolling(self.strategy_params['sma_short']).mean()
            df['SMA_Long'] = df['CLOSE'].rolling(self.strategy_params['sma_long']).mean()

            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))

            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(self.strategy_params['atr_period']).mean()

            # ADX (简化版)
            plus_dm = df['HIGH'].diff()
            minus_dm = df['LOW'].diff()
            plus_dm[plus_dm < 0] = 0
            minus_dm[minus_dm > 0] = 0
            minus_dm = abs(minus_dm)

            tr = true_range
            plus_di = 100 * (plus_dm.rolling(self.strategy_params['adx_period']).mean() / tr.rolling(self.strategy_params['adx_period']).mean())
            minus_di = 100 * (minus_dm.rolling(self.strategy_params['adx_period']).mean() / tr.rolling(self.strategy_params['adx_period']).mean())

            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            df['ADX'] = dx.rolling(self.strategy_params['adx_period']).mean()

            # 成交量移动平均
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()

            # 价格动量
            df['Momentum'] = df['CLOSE'].pct_change(5)

            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()

            return df

        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data

    def adaptive_threshold_adjustment(self, data: pd.DataFrame, current_idx: int) -> dict:
        """自适应阈值调整"""
        try:
            # 获取最近20个周期的数据
            lookback = min(20, current_idx)
            recent_data = data.iloc[current_idx-lookback:current_idx+1]

            # 计算市场状态
            volatility = recent_data['Volatility'].mean()
            momentum = abs(recent_data['Momentum'].mean())

            # 根据市场状态调整阈值
            adjusted_params = self.strategy_params.copy()

            # 高波动时放宽条件
            if volatility > 0.02:
                adjusted_params['adx_threshold'] *= 0.8
                adjusted_params['rsi_oversold'] += 5
                adjusted_params['rsi_overbought'] -= 5

            # 强动量时放宽条件
            if momentum > 0.01:
                adjusted_params['adx_threshold'] *= 0.9
                adjusted_params['rsi_oversold'] += 3

            return adjusted_params

        except Exception as e:
            logger.warning(f"自适应调整失败: {e}")
            return self.strategy_params

    def simulate_optimized_alphax_strategy(self, data: pd.DataFrame) -> dict:
        """模拟优化版阿尔法X策略交易"""
        try:
            print("🔄 模拟优化版阿尔法X策略交易...")

            # 计算技术指标
            data = self.calculate_technical_indicators(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]

            last_trade_time = None
            daily_trades = {}
            consecutive_losses = 0

            for i in range(50, len(data)):  # 从第50个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]

                # 获取基础数据
                close_price = current_data['CLOSE']
                sma_short = current_data['SMA_Short']
                sma_long = current_data['SMA_Long']
                rsi = current_data['RSI']
                atr = current_data['ATR']
                adx = current_data['ADX']
                volume = current_data['VOLUME']
                volume_ma = current_data['Volume_MA']
                momentum = current_data['Momentum']
                volatility = current_data['Volatility']

                # 检查数据有效性
                if any(pd.isna(x) for x in [close_price, sma_short, sma_long, rsi, atr, adx]):
                    equity_curve.append(cash + position * close_price)
                    continue

                # 检查信号间隔
                if last_trade_time and (current_time - last_trade_time).total_seconds() < self.strategy_params['min_signal_interval'] * 60:
                    equity_curve.append(cash + position * close_price)
                    continue

                # 检查每日交易限制
                current_date = current_time.date()
                if current_date not in daily_trades:
                    daily_trades[current_date] = 0

                if daily_trades[current_date] >= self.strategy_params['max_daily_trades']:
                    equity_curve.append(cash + position * close_price)
                    continue

                # 自适应阈值调整
                if self.strategy_params['adaptive_thresholds']:
                    adjusted_params = self.adaptive_threshold_adjustment(data, i)
                else:
                    adjusted_params = self.strategy_params

                # 生成交易信号
                if position == 0:  # 无持仓
                    # 大幅放宽的买入条件
                    buy_conditions = [
                        sma_short > sma_long,  # 短期均线在长期均线之上
                        close_price > sma_short * 0.995,  # 价格接近或高于短期均线
                        rsi <= adjusted_params['rsi_oversold'],  # RSI条件
                        adx >= adjusted_params['adx_threshold'],  # ADX条件
                        volume > volume_ma * 0.8 if not pd.isna(volume_ma) else True,  # 成交量条件
                        consecutive_losses < 3  # 连续亏损保护
                    ]

                    # 额外的趋势确认 (可选)
                    trend_confirmation = (
                        momentum > adjusted_params['momentum_threshold'] or
                        close_price > sma_long * 1.01  # 价格明显高于长期均线
                    )

                    if all(buy_conditions) or (sum(buy_conditions) >= 4 and trend_confirmation):
                        # 计算仓位大小
                        base_risk = adjusted_params['risk_per_trade']

                        # 波动率调整
                        if adjusted_params['volatility_adjustment'] and not pd.isna(volatility):
                            if volatility > 0.03:  # 高波动
                                base_risk *= 0.7
                            elif volatility < 0.01:  # 低波动
                                base_risk *= 1.3

                        # 趋势加仓
                        if adx > 30 and momentum > 0.01:
                            base_risk *= adjusted_params['trend_multiplier']

                        risk_amount = cash * base_risk
                        stop_loss_price = close_price - (atr * adjusted_params['atr_sl_multiple'])
                        risk_per_share = close_price - stop_loss_price

                        if risk_per_share > 0:
                            shares = risk_amount / risk_per_share
                            max_shares = cash * adjusted_params['max_position_size'] / close_price
                            shares = min(shares, max_shares)

                            if shares > 0:
                                position = shares
                                cash -= shares * close_price * 1.0005  # 包含手续费

                                trades.append({
                                    'time': current_time,
                                    'action': 'buy',
                                    'price': close_price,
                                    'shares': shares,
                                    'stop_loss': stop_loss_price,
                                    'take_profit': close_price + (atr * adjusted_params['atr_tp_multiple']),
                                    'rsi': rsi,
                                    'adx': adx,
                                    'momentum': momentum
                                })

                                last_trade_time = current_time
                                daily_trades[current_date] += 1

                elif position > 0:  # 有持仓
                    last_trade = trades[-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 动态止损调整
                    if close_price > last_trade['price'] * 1.02:  # 盈利2%以上
                        # 移动止损到成本价
                        stop_loss = max(stop_loss, last_trade['price'])

                    # 卖出条件
                    should_sell = False
                    sell_reason = ""

                    if close_price <= stop_loss:
                        should_sell = True
                        sell_reason = "止损"
                    elif close_price >= take_profit:
                        should_sell = True
                        sell_reason = "止盈"
                    elif sma_short < sma_long:
                        should_sell = True
                        sell_reason = "趋势转向"
                    elif rsi >= adjusted_params['rsi_overbought']:
                        should_sell = True
                        sell_reason = "RSI超买"
                    elif adx < adjusted_params['adx_threshold'] * 0.7:
                        should_sell = True
                        sell_reason = "趋势减弱"

                    if should_sell:
                        cash += position * close_price * 0.9995  # 扣除手续费

                        pnl = (close_price - last_trade['price']) * position

                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': close_price,
                            'shares': position,
                            'reason': sell_reason,
                            'pnl': pnl
                        })

                        # 更新连续亏损计数
                        if pnl < 0:
                            consecutive_losses += 1
                        else:
                            consecutive_losses = 0

                        position = 0
                        last_trade_time = current_time
                        daily_trades[current_date] += 1

                # 更新权益曲线
                current_equity = cash + position * close_price
                equity_curve.append(current_equity)

            # 如果最后还有持仓，平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995

                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': (final_price - trades[-1]['price']) * position
                })

                position = 0

            final_equity = cash

            # 计算交易统计
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']

            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])

            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]

            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital

            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)  # 年化
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            print(f"✅ 策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")

            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }

        except Exception as e:
            logger.error(f"策略模拟失败: {e}")
            return None

    def run_comprehensive_test(self):
        """运行全面测试"""
        test_periods = [
            {
                'name': '2024年4月验证',
                'start': '2024-04-01',
                'end': '2024-04-30',
                'description': '已知盈利期间验证'
            },
            {
                'name': '2024年1-3月',
                'start': '2024-01-01',
                'end': '2024-03-31',
                'description': '2024年第一季度'
            },
            {
                'name': '2023年下半年',
                'start': '2023-07-01',
                'end': '2023-12-31',
                'description': '2023年下半年牛市'
            },
            {
                'name': '2024年上半年',
                'start': '2024-01-01',
                'end': '2024-06-30',
                'description': '2024年上半年完整测试'
            }
        ]

        print("🚀 P01阿尔法X2024策略 - 深度优化版测试")
        print("=" * 80)
        print("优化重点:")
        print("  1. 大幅放宽信号生成条件")
        print("  2. 自适应阈值调整")
        print("  3. 动态风险管理")
        print("  4. 趋势跟踪增强")
        print("=" * 80)

        all_results = {}

        for period in test_periods:
            try:
                print(f"\n🔥 测试期间: {period['name']}")
                print(f"   时间: {period['start']} 至 {period['end']}")
                print("-" * 60)

                start_time = time.time()

                # 加载数据
                data = self.load_historical_data(period['start'], period['end'])

                if data.empty:
                    print("❌ 数据为空，跳过此期间")
                    continue

                # 运行策略模拟
                result = self.simulate_optimized_alphax_strategy(data)

                if not result:
                    print("❌ 策略模拟失败")
                    continue

                execution_time = time.time() - start_time

                # 计算额外指标
                total_days = (pd.to_datetime(period['end']) - pd.to_datetime(period['start'])).days
                annual_return = (1 + result['total_return']) ** (365 / total_days) - 1

                # 计算市场基准收益
                market_return = ((data['CLOSE'].iloc[-1] - data['CLOSE'].iloc[0]) / data['CLOSE'].iloc[0])

                period_result = {
                    'period_name': period['name'],
                    'start_date': period['start'],
                    'end_date': period['end'],
                    'total_days': total_days,
                    'execution_time': execution_time,

                    # 基础指标
                    'total_return_pct': result['total_return'] * 100,
                    'annual_return_pct': annual_return * 100,
                    'max_drawdown_pct': result['max_drawdown'] * 100,
                    'sharpe_ratio': result['sharpe_ratio'],

                    # 交易指标
                    'total_trades': result['total_trades'],
                    'winning_trades': result['winning_trades'],
                    'losing_trades': result['losing_trades'],
                    'win_rate': result['win_rate'] * 100,
                    'profit_loss_ratio': result['profit_loss_ratio'],

                    # 市场对比
                    'market_return_pct': market_return * 100,
                    'excess_return_pct': (result['total_return'] - market_return) * 100,

                    # 原始结果
                    'raw_result': result
                }

                all_results[period['name']] = period_result

                print(f"✅ 测试完成!")
                print(f"   总收益率: {period_result['total_return_pct']:+.2f}%")
                print(f"   年化收益率: {period_result['annual_return_pct']:+.2f}%")
                print(f"   最大回撤: {period_result['max_drawdown_pct']:.2f}%")
                print(f"   夏普比率: {period_result['sharpe_ratio']:.2f}")
                print(f"   交易次数: {period_result['total_trades']}")
                print(f"   胜率: {period_result['win_rate']:.1f}%")
                print(f"   盈亏比: {period_result['profit_loss_ratio']:.2f}")
                print(f"   超额收益: {period_result['excess_return_pct']:+.2f}%")

            except Exception as e:
                logger.error(f"测试期间 {period['name']} 失败: {e}")
                continue

        # 生成综合报告
        self.generate_optimization_report(all_results)

        return all_results

    def generate_optimization_report(self, results: dict):
        """生成优化报告"""
        if not results:
            print("❌ 没有有效的测试结果")
            return

        print(f"\n📊 P01阿尔法X2024策略 - 深度优化版报告")
        print("=" * 100)
        print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试期间数: {len(results)}")
        print()

        # 创建结果表格
        print("📋 各期间详细表现:")
        print("-" * 100)

        header = f"{'期间':<20} {'总收益率':<10} {'年化收益率':<10} {'最大回撤':<8} {'夏普比率':<8} {'胜率':<8} {'交易次数':<8}"
        print(header)
        print("-" * 100)

        for period_name, result in results.items():
            row = (f"{period_name:<20} "
                  f"{result['total_return_pct']:>8.2f}% "
                  f"{result['annual_return_pct']:>8.2f}% "
                  f"{result['max_drawdown_pct']:>6.2f}% "
                  f"{result['sharpe_ratio']:>6.2f} "
                  f"{result['win_rate']:>6.1f}% "
                  f"{result['total_trades']:>6d}")
            print(row)

        print()

        # 客户目标达成分析
        print(f"🎯 客户目标达成分析:")
        print("-" * 50)
        print("客户目标: 年化收益≥15%, 夏普比率≥2, 最大回撤≤15%")
        print()

        target_met_count = 0
        for period_name, result in results.items():
            annual_ok = result['annual_return_pct'] >= 15
            sharpe_ok = result['sharpe_ratio'] >= 2 if not np.isnan(result['sharpe_ratio']) else False
            drawdown_ok = result['max_drawdown_pct'] <= 15

            all_targets_met = annual_ok and sharpe_ok and drawdown_ok
            if all_targets_met:
                target_met_count += 1

            status = "✅ 达标" if all_targets_met else "❌ 未达标"
            print(f"{period_name}: {status}")
            print(f"  年化收益: {result['annual_return_pct']:+.2f}% {'✅' if annual_ok else '❌'}")
            print(f"  夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'}")
            print(f"  最大回撤: {result['max_drawdown_pct']:.2f}% {'✅' if drawdown_ok else '❌'}")
            print()

        target_success_rate = target_met_count / len(results) * 100
        print(f"🏆 目标达成率: {target_met_count}/{len(results)} ({target_success_rate:.1f}%)")

        # 优化效果评估
        print(f"\n🔧 优化效果评估:")
        print("-" * 50)

        # 统计分析
        returns = [r['total_return_pct'] for r in results.values()]
        annual_returns = [r['annual_return_pct'] for r in results.values()]
        trade_counts = [r['total_trades'] for r in results.values()]

        print(f"平均收益率: {np.mean(returns):+.2f}%")
        print(f"平均年化收益率: {np.mean(annual_returns):+.2f}%")
        print(f"平均交易次数: {np.mean(trade_counts):.1f}")
        print(f"交易信号生成: {'✅ 成功' if np.mean(trade_counts) > 0 else '❌ 失败'}")

        # 最终结论
        print(f"\n🎯 最终结论:")
        print("-" * 50)

        if target_success_rate >= 75:
            print("🏆 优化成功! 策略表现优秀，达到客户目标")
        elif target_success_rate >= 50:
            print("✅ 优化有效! 策略表现良好，接近客户目标")
        elif np.mean(trade_counts) > 0:
            print("⚠️ 部分优化! 策略能产生交易信号，但需要进一步调整")
        else:
            print("❌ 优化失败! 策略仍无法产生有效交易信号")

        # 保存结果
        try:
            report_data = []
            for period_name, result in results.items():
                report_data.append({
                    '测试期间': period_name,
                    '开始日期': result['start_date'],
                    '结束日期': result['end_date'],
                    '总收益率(%)': result['total_return_pct'],
                    '年化收益率(%)': result['annual_return_pct'],
                    '最大回撤(%)': result['max_drawdown_pct'],
                    '夏普比率': result['sharpe_ratio'],
                    '交易次数': result['total_trades'],
                    '胜率(%)': result['win_rate'],
                    '盈亏比': result['profit_loss_ratio'],
                    '超额收益(%)': result['excess_return_pct']
                })

            df = pd.DataFrame(report_data)
            filename = f"P01阿尔法X2024_深度优化版测试报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"\n📄 详细报告已保存: {filename}")

        except Exception as e:
            logger.error(f"保存报告失败: {e}")

def main():
    """主函数"""
    print("🚀 启动P01阿尔法X2024策略深度优化版测试...")

    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        print("请确保本地BTCUSDT历史数据已正确放置")
        return

    # 创建优化版策略测试器
    optimizer = OptimizedAlphaX2024Strategy()

    # 运行全面测试
    start_time = time.time()
    results = optimizer.run_comprehensive_test()
    end_time = time.time()

    print(f"\n🎉 深度优化版测试完成!")
    print(f"⏱️ 总耗时: {(end_time - start_time)/60:.1f}分钟")
    print(f"📊 成功测试期间数: {len(results)}")

    if results:
        # 找出最佳表现期间
        best_period = max(results.items(), key=lambda x: x[1]['annual_return_pct'])
        print(f"🏆 最佳表现期间: {best_period[0]} (年化收益: {best_period[1]['annual_return_pct']:+.2f}%)")

if __name__ == "__main__":
    main()