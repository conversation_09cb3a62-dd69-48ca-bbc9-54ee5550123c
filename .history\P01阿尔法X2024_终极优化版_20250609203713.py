# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 终极优化版
基于前面测试结果的深度分析，重新设计策略逻辑
目标：达到年化收益≥15%, 夏普比率≥2, 最大回撤≤15%
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class UltimateAlphaX2024Strategy:
    """P01阿尔法X2024策略 - 终极优化版"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 终极优化参数 - 基于前面测试结果的深度分析
        self.strategy_params = {
            # 趋势识别参数 (更长周期，更稳定)
            'trend_fast': 20,          # 快速趋势线
            'trend_slow': 50,          # 慢速趋势线
            'trend_filter': 100,       # 趋势过滤线
            
            # 动量指标
            'rsi_period': 21,          # RSI周期 (增加)
            'rsi_entry_low': 40,       # RSI入场下限
            'rsi_entry_high': 60,      # RSI入场上限
            'rsi_exit_low': 25,        # RSI退出下限
            'rsi_exit_high': 75,       # RSI退出上限
            
            # 波动率指标
            'atr_period': 21,          # ATR周期
            'bb_period': 20,           # 布林带周期
            'bb_std': 2.0,             # 布林带标准差
            
            # 严格的入场条件
            'min_trend_strength': 0.02,    # 最小趋势强度2%
            'volume_multiplier': 1.5,      # 成交量倍数
            'momentum_threshold': 0.01,    # 动量阈值1%
            
            # 保守的风险管理
            'base_position_size': 0.1,     # 基础仓位10%
            'max_position_size': 0.3,      # 最大仓位30%
            'stop_loss_atr': 2.0,          # 止损ATR倍数
            'take_profit_atr': 6.0,        # 止盈ATR倍数
            
            # 严格的交易控制
            'min_signal_interval_hours': 6,    # 最小信号间隔6小时
            'max_daily_trades': 2,             # 每日最大2次交易
            'max_weekly_trades': 8,            # 每周最大8次交易
            'max_consecutive_losses': 3,       # 最大连续亏损3次
            
            # 市场状态适应
            'bear_market_multiplier': 0.5,     # 熊市仓位减半
            'bull_market_multiplier': 1.2,     # 牛市仓位增加
            'sideways_market_multiplier': 0.8, # 震荡市仓位减少
        }
        
        self.results = {}
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            # 获取日期范围内的所有文件
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                # 构建文件路径
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    # 读取数据
                    df = pd.read_csv(filepath)
                    
                    # 标准化列名
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 重命名列以匹配策略需求
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    # 过滤日期范围
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            # 合并所有数据
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            
            # 去重
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            print(f"   时间范围: {combined_data.index[0]} 至 {combined_data.index[-1]}")
            
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_advanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算高级技术指标"""
        try:
            df = data.copy()
            
            # 多重趋势线
            df['Trend_Fast'] = df['CLOSE'].rolling(self.strategy_params['trend_fast']).mean()
            df['Trend_Slow'] = df['CLOSE'].rolling(self.strategy_params['trend_slow']).mean()
            df['Trend_Filter'] = df['CLOSE'].rolling(self.strategy_params['trend_filter']).mean()
            
            # 趋势强度
            df['Trend_Strength'] = (df['Trend_Fast'] - df['Trend_Slow']) / df['Trend_Slow']
            df['Long_Trend_Strength'] = (df['Trend_Slow'] - df['Trend_Filter']) / df['Trend_Filter']
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(self.strategy_params['atr_period']).mean()
            
            # 布林带
            df['BB_Middle'] = df['CLOSE'].rolling(self.strategy_params['bb_period']).mean()
            bb_std = df['CLOSE'].rolling(self.strategy_params['bb_period']).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * self.strategy_params['bb_std'])
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * self.strategy_params['bb_std'])
            df['BB_Position'] = (df['CLOSE'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 价格动量
            df['Price_Momentum'] = df['CLOSE'].pct_change(10)
            df['Price_Acceleration'] = df['Price_Momentum'].diff()
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            # 市场状态识别
            df['Market_State'] = 0  # 0=震荡, 1=牛市, -1=熊市
            
            # 牛市条件：价格在所有均线之上，趋势强度>2%
            bull_condition = (
                (df['CLOSE'] > df['Trend_Fast']) & 
                (df['Trend_Fast'] > df['Trend_Slow']) & 
                (df['Trend_Slow'] > df['Trend_Filter']) &
                (df['Trend_Strength'] > 0.02)
            )
            df.loc[bull_condition, 'Market_State'] = 1
            
            # 熊市条件：价格在所有均线之下，趋势强度<-2%
            bear_condition = (
                (df['CLOSE'] < df['Trend_Fast']) & 
                (df['Trend_Fast'] < df['Trend_Slow']) & 
                (df['Trend_Slow'] < df['Trend_Filter']) &
                (df['Trend_Strength'] < -0.02)
            )
            df.loc[bear_condition, 'Market_State'] = -1
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def get_market_state_multiplier(self, market_state: int) -> float:
        """根据市场状态获取仓位倍数"""
        if market_state == 1:  # 牛市
            return self.strategy_params['bull_market_multiplier']
        elif market_state == -1:  # 熊市
            return self.strategy_params['bear_market_multiplier']
        else:  # 震荡市
            return self.strategy_params['sideways_market_multiplier']
    
    def check_entry_conditions(self, data: pd.DataFrame, idx: int) -> tuple:
        """检查入场条件"""
        try:
            current = data.iloc[idx]
            
            # 基础数据检查
            required_fields = ['CLOSE', 'Trend_Fast', 'Trend_Slow', 'Trend_Filter', 
                             'RSI', 'ATR', 'BB_Position', 'Volume_Ratio', 
                             'Trend_Strength', 'Price_Momentum', 'Market_State']
            
            for field in required_fields:
                if pd.isna(current[field]):
                    return False, "数据不完整"
            
            # 严格的多头入场条件
            long_conditions = [
                # 1. 强趋势确认
                current['Trend_Fast'] > current['Trend_Slow'],
                current['Trend_Slow'] > current['Trend_Filter'],
                current['CLOSE'] > current['Trend_Fast'],
                
                # 2. 趋势强度足够
                current['Trend_Strength'] >= self.strategy_params['min_trend_strength'],
                
                # 3. RSI在合理范围
                self.strategy_params['rsi_entry_low'] <= current['RSI'] <= self.strategy_params['rsi_entry_high'],
                
                # 4. 价格位置合理 (不在布林带上轨附近)
                current['BB_Position'] <= 0.8,
                
                # 5. 成交量确认
                current['Volume_Ratio'] >= self.strategy_params['volume_multiplier'],
                
                # 6. 动量确认
                current['Price_Momentum'] >= self.strategy_params['momentum_threshold'],
                
                # 7. 市场状态不是熊市
                current['Market_State'] >= 0
            ]
            
            if all(long_conditions):
                return True, "多头入场"
            
            return False, "条件不满足"
            
        except Exception as e:
            logger.warning(f"检查入场条件失败: {e}")
            return False, "检查失败"
    
    def check_exit_conditions(self, data: pd.DataFrame, idx: int, entry_price: float, 
                            stop_loss: float, take_profit: float) -> tuple:
        """检查出场条件"""
        try:
            current = data.iloc[idx]
            current_price = current['CLOSE']
            
            # 止损止盈检查
            if current_price <= stop_loss:
                return True, "止损"
            
            if current_price >= take_profit:
                return True, "止盈"
            
            # 技术指标退出条件
            if not pd.isna(current['RSI']):
                if current['RSI'] >= self.strategy_params['rsi_exit_high']:
                    return True, "RSI超买"
                
                if current['RSI'] <= self.strategy_params['rsi_exit_low']:
                    return True, "RSI超卖"
            
            # 趋势转向检查
            if (current['Trend_Fast'] < current['Trend_Slow'] or 
                current['CLOSE'] < current['Trend_Fast'] * 0.98):
                return True, "趋势转向"
            
            # 市场状态转为熊市
            if current['Market_State'] == -1:
                return True, "市场转熊"
            
            return False, "持续持有"
            
        except Exception as e:
            logger.warning(f"检查出场条件失败: {e}")
            return True, "检查失败退出"

    def simulate_ultimate_strategy(self, data: pd.DataFrame) -> dict:
        """模拟终极优化策略"""
        try:
            print("🔄 模拟终极优化版阿尔法X策略...")

            # 计算高级技术指标
            data = self.calculate_advanced_indicators(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]

            # 交易控制变量
            last_trade_time = None
            daily_trades = {}
            weekly_trades = {}
            consecutive_losses = 0

            for i in range(100, len(data)):  # 从第100个数据点开始，确保指标稳定
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']

                # 更新权益曲线
                current_equity = cash + position * current_price
                equity_curve.append(current_equity)

                # 检查数据有效性
                if pd.isna(current_price):
                    continue

                # 检查时间间隔限制
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 3600
                    if time_diff < self.strategy_params['min_signal_interval_hours']:
                        continue

                # 检查交易频率限制
                current_date = current_time.date()
                current_week = current_time.isocalendar()[1]

                if current_date not in daily_trades:
                    daily_trades[current_date] = 0
                if current_week not in weekly_trades:
                    weekly_trades[current_week] = 0

                if daily_trades[current_date] >= self.strategy_params['max_daily_trades']:
                    continue
                if weekly_trades[current_week] >= self.strategy_params['max_weekly_trades']:
                    continue

                # 检查连续亏损限制
                if consecutive_losses >= self.strategy_params['max_consecutive_losses']:
                    continue

                # 无持仓时检查入场
                if position == 0:
                    can_enter, reason = self.check_entry_conditions(data, i)

                    if can_enter:
                        # 计算仓位大小
                        market_state = current_data['Market_State']
                        market_multiplier = self.get_market_state_multiplier(market_state)

                        base_size = self.strategy_params['base_position_size']
                        adjusted_size = base_size * market_multiplier
                        max_size = self.strategy_params['max_position_size']

                        position_size = min(adjusted_size, max_size)

                        # 根据波动率调整仓位
                        volatility = current_data.get('Volatility', 0.02)
                        if volatility > 0.04:  # 高波动
                            position_size *= 0.7
                        elif volatility < 0.015:  # 低波动
                            position_size *= 1.2

                        # 计算止损止盈
                        atr = current_data['ATR']
                        stop_loss = current_price - (atr * self.strategy_params['stop_loss_atr'])
                        take_profit = current_price + (atr * self.strategy_params['take_profit_atr'])

                        # 计算股数
                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            position = shares
                            cash -= shares * current_price * 1.0005  # 手续费

                            trades.append({
                                'time': current_time,
                                'action': 'buy',
                                'price': current_price,
                                'shares': shares,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit,
                                'market_state': market_state,
                                'position_size': position_size,
                                'reason': reason
                            })

                            last_trade_time = current_time
                            daily_trades[current_date] += 1
                            weekly_trades[current_week] += 1

                # 有持仓时检查出场
                elif position > 0:
                    last_trade = trades[-1]
                    should_exit, exit_reason = self.check_exit_conditions(
                        data, i, last_trade['price'],
                        last_trade['stop_loss'], last_trade['take_profit']
                    )

                    if should_exit:
                        cash += position * current_price * 0.9995  # 扣除手续费

                        pnl = (current_price - last_trade['price']) * position
                        pnl_pct = pnl / (last_trade['price'] * position) * 100

                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': exit_reason,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct
                        })

                        # 更新连续亏损计数
                        if pnl < 0:
                            consecutive_losses += 1
                        else:
                            consecutive_losses = 0

                        position = 0
                        last_trade_time = current_time
                        daily_trades[current_date] += 1
                        weekly_trades[current_week] += 1

            # 期末平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995

                pnl = (final_price - trades[-1]['price']) * position
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': pnl
                })
                position = 0

            final_equity = cash

            # 计算统计指标
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']

            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])

            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]

            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital

            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())

            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0

            # 计算索提诺比率
            negative_returns = returns[returns < 0]
            if len(negative_returns) > 0 and negative_returns.std() > 0:
                sortino_ratio = returns.mean() / negative_returns.std() * np.sqrt(252 * 24 * 60)
            else:
                sortino_ratio = 0

            print(f"✅ 终极策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")
            print(f"   索提诺比率: {sortino_ratio:.2f}")

            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }

        except Exception as e:
            logger.error(f"终极策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P01阿尔法X2024策略终极优化版测试...")

    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return

    strategy = UltimateAlphaX2024Strategy()

    # 测试2024年4月 (已知市场环境)
    print("\n🔥 测试期间: 2024年4月 (终极优化版)")
    print("=" * 60)

    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_ultimate_strategy(data)

        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1

            print(f"\n📊 终极优化版测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"索提诺比率: {result['sortino_ratio']:.2f}")
            print(f"交易次数: {result['total_trades']}")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")

            # 客户目标达成检查
            print(f"\n🎯 客户目标达成检查:")
            print("-" * 40)
            annual_ok = annual_return >= 0.15
            sharpe_ok = result['sharpe_ratio'] >= 2.0
            drawdown_ok = result['max_drawdown'] <= 0.15

            print(f"年化收益率: {annual_return*100:+.2f}% {'✅' if annual_ok else '❌'} (目标: ≥15%)")
            print(f"夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'} (目标: ≥2.0)")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}% {'✅' if drawdown_ok else '❌'} (目标: ≤15%)")

            all_targets_met = annual_ok and sharpe_ok and drawdown_ok
            print(f"\n🏆 综合评价: {'🎉 完全达标!' if all_targets_met else '⚠️ 部分达标' if sum([annual_ok, sharpe_ok, drawdown_ok]) >= 2 else '❌ 未达标'}")

            if all_targets_met:
                print("🎊 恭喜！终极优化版策略成功达到客户目标！")
            else:
                print("💡 策略仍需进一步优化，但已有显著改进")

    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
