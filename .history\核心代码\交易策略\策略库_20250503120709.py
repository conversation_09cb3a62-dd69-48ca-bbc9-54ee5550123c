# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

from ..交易策略.事件驱动策略基类 import EventDrivenStrategy

class TradingStrategy(EventDrivenStrategy, BASE_STRATEGY):  # 多重继承，支持事件驱动和回测
    """交易策略基类，同时支持事件驱动和回测模式"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功
    performance_stats = {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_pnl': 0.0,
        'max_win': 0.0,
        'max_loss': 0.0,
        'long_trades': 0,
        'short_trades': 0
    }

    def update_performance_stats(self, pnl: float, trade_type: str):
        """
        更新绩效统计数据
        
        Args:
            pnl: 盈亏比例
            trade_type: 交易类型 ('long' 或 'short')
        """
        self.performance_stats['total_trades'] += 1
        self.performance_stats['total_pnl'] += pnl
        
        if trade_type == 'long':
            self.performance_stats['long_trades'] += 1
        else:
            self.performance_stats['short_trades'] += 1
            
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            if pnl > self.performance_stats['max_win']:
                self.performance_stats['max_win'] = pnl
        else:
            self.performance_stats['losing_trades'] += 1
            if pnl < self.performance_stats['max_loss']:
                self.performance_stats['max_loss'] = pnl
                
        logger.info(f"更新绩效统计: 总交易数={self.performance_stats['total_trades']}, 总盈亏={self.performance_stats['total_pnl']:.2%}")

    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 分别调用两个父类的 __init__ 方法
        # 调用 EventDrivenStrategy 的 __init__，只传递 params
        EventDrivenStrategy.__init__(self, params_input) # 使用原始传入的 params

        # 调用 backtesting.Strategy (或其替身 BASE_STRATEGY) 的 __init__
        # 必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        if backtesting_available and isinstance(self, BacktestStrategy):
             BASE_STRATEGY.__init__(self, broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init().


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass

class TrendFollowingStrategy(TradingStrategy):
    """趋势跟踪策略"""
    # --- 定义可优化的参数作为类变量 ---
    window = 50             # SMA 窗口
    macd_fast = 12          # MACD 快线
    macd_slow = 26          # MACD 慢线
    macd_signal = 9           # MACD 信号线
    stop_loss_pct = 0.03    # 止损百分比 (基础值)
    risk_reward_ratio = 2.0 # 风险回报比
    # --- 非优化参数 (可以从命令行或配置传入) ---
    position_size = 0.1     # 仓位大小
    atr_window = 14         # ATR 窗口 (如果需要)

    def __init__(self, broker, data, params=None):
        # 调用基类 __init__，它会处理参数合并和设置
        super().__init__(broker, data, params)
        # 基类 __init__ 已经将最终的参数 (包括优化的) 设置为实例属性
        # 例如 self.window, self.macd_fast 等现在应该有正确的值
        # self.position_size = self.parameters.get('position_size', 0.1) # 确保非优化参数也被设置

    def init(self):
        # 计算技术指标 (使用实例属性访问参数)
        self.sma = self.I(pd.Series(self.data.Close).rolling(self.window).mean)
        # self.atr_window = self.parameters.get('atr_window', 14) # atr_window 现在是类/实例变量
        self.atr = self.I(lambda: pd.Series(self.data.TradePrice if hasattr(self.data, 'TradePrice') else self.data.Close).rolling(self.atr_window).std())

        # MACD 指标 (使用实例属性访问参数)
        # self.macd_fast = self.parameters.get('macd_fast', 12) # 已是实例变量
        # self.macd_slow = self.parameters.get('macd_slow', 26) # 已是实例变量
        # self.macd_signal = self.parameters.get('macd_signal', 9) # 已是实例变量
        self.macd_line = self.I(lambda: pd.Series(self.data.Close).ewm(span=self.macd_fast, adjust=False).mean() -
                              pd.Series(self.data.Close).ewm(span=self.macd_slow, adjust=False).mean())
        self.macd_signal_line = self.I(lambda: pd.Series(self.macd_line).ewm(span=self.macd_signal, adjust=False).mean())

        # 布林带指标 (如果还需要的话，也应将参数设为类变量)
        # self.bb_window = self.parameters.get('bb_window', 20)
        # self.bb_std = self.parameters.get('bb_std', 2)
        # self.bb_middle = self.I(pd.Series(self.data.Close).rolling(self.bb_window).mean)
        # self.bb_std_dev = self.I(pd.Series(self.data.Close).rolling(self.bb_window).std)
        # self.bb_upper = self.I(lambda: self.bb_middle + self.bb_std * self.bb_std_dev)
        # self.bb_lower = self.I(lambda: self.bb_middle - self.bb_std * self.bb_std_dev)


    def next(self):
        # 获取当前价格和指标值 (使用实例属性访问参数)
        current_price = self.data.Close[-1]
        sma = self.sma[-1]
        atr = self.atr[-1]
        rsi = self.data.RSI[-1]

        # 动态止损和止盈逻辑
        if self.position:
            if self.position and hasattr(self.position, 'entry_price'):
                entry_price = self.position.entry_price  # 使用 entry_price 获取入场价格
                current_pnl = (current_price - entry_price) / entry_price  # 计算盈亏比例
            else:
                entry_price = current_price  # 使用当前价格作为备选
                current_pnl = 0.0  # 如果无法获取 entry_price，则设置 pnl 为 0

            # 动态止损：基于ATR和固定止损比例
            dynamic_stop_loss = max(self.stop_loss_pct, atr * 1.5 / current_price)  # 降低 ATR 乘数
            
            # 动态止盈：基于风险回报比
            dynamic_take_profit = self.risk_reward_ratio * dynamic_stop_loss

            # 止损退出
            if current_pnl <= -dynamic_stop_loss:
                self.position.close()
                self.update_performance_stats(current_pnl, self.position.type)
                return

            # 止盈退出
            if current_pnl >= dynamic_take_profit:
                self.position.close()
                self.update_performance_stats(current_pnl, self.position.type)
                return

        # 确保最小交易频率
        if len(self.trades) == 0 or (len(self.trades) > 0 and (self.trades[-1].exit_bar is None or len(self.data) - self.trades[-1].exit_bar > 5)):
            # 简化买入信号：价格上穿SMA且MACD金叉
            if (current_price > sma and
                self.macd_line[-1] > self.macd_signal_line[-1]):
                
                # 使用固定比例仓位
                size = self.position_size # 使用传入的 position_size 比例
                if size > 0:
                    # 计算动态止损/止盈目标价格 (基于入场价和ATR/风险回报比)
                    entry_price = current_price
                    dynamic_stop_loss_price = entry_price * (1 - max(self.stop_loss_pct, atr * 1.5 / entry_price))
                    dynamic_take_profit_price = entry_price * (1 + self.risk_reward_ratio * max(self.stop_loss_pct, atr * 1.5 / entry_price))
                    
                    self.buy(size=size, sl=dynamic_stop_loss_price, tp=dynamic_take_profit_price)

            # 简化卖出信号 (做空): 价格下穿SMA且MACD死叉
            elif (current_price < sma and
                  self.macd_line[-1] < self.macd_signal_line[-1]):
                
                # 使用固定比例仓位
                size = self.position_size # 使用传入的 position_size 比例
                if size > 0:
                    # 计算动态止损/止盈目标价格 (基于入场价和ATR/风险回报比)
                    entry_price = current_price
                    dynamic_stop_loss_price = entry_price * (1 + max(self.stop_loss_pct, atr * 1.5 / entry_price))
                    dynamic_take_profit_price = entry_price * (1 - self.risk_reward_ratio * max(self.stop_loss_pct, atr * 1.5 / entry_price))

                    self.sell(size=size, sl=dynamic_stop_loss_price, tp=dynamic_take_profit_price)

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略"""
    def __init__(self, broker, data, params=None):
        super().__init__(broker, data, params)
        self.window = params.get('window', 10)
        self.threshold = params.get('threshold', 1.0)

    def init(self):
        self.sma = self.I(pd.Series(self.data.Close).rolling(self.window).mean)
        self.std = self.I(pd.Series(self.data.Close).rolling(self.window).std)

    def next(self):
        if self.position:
            return

        current_price = self.data.Close[-1]
        upper_band = self.sma[-1] + self.threshold * self.std[-1]
        lower_band = self.sma[-1] - self.threshold * self.std[-1]

        if current_price > upper_band:
            self.sell()
        elif current_price < lower_band:
            self.buy()

# 策略注册表
STRATEGIES = {
    "MeanReversion": MeanReversionStrategy,
    "TrendFollowing": TrendFollowingStrategy
}
