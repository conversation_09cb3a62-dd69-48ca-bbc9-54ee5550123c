# 订单执行服务部署指南

## 1. 环境要求

- Python 3.8+
- 依赖包: 
  ```bash
  pip install requests numpy
  ```

## 2. 配置说明

编辑`config/系统配置.py`:
```python
ORDER_EXECUTOR_CONFIG = {
    "api_key": "your_api_key_here",  # 必填
    "base_url": "http://your-trade-server:8000",
    "initial_capital": 1000000.0,  # 初始资金
    "max_order_size": 10000,  # 单笔最大订单量
    "enable_risk_control": True  # 启用风控
}
```

## 3. 启动服务

### 开发模式
```bash
python 后台服务_交易引擎.py --dev
```

### 生产模式
```bash
python 后台服务_交易引擎.py --prod --port 8000
```

## 4. 监控与日志

- 日志文件: `logs/quant_trading.log`
- 关键指标:
  - 订单执行延迟
  - 成交率
  - 资金利用率

## 5. 性能调优

1. 调整订单簿深度:
```python
# 在模拟执行器.py中修改
self.order_book = {'bid': deque(maxlen=500), 'ask': deque(maxlen=500)}
```

2. 优化网络连接:
```python
# 使用连接池
self.session = requests.Session()
```

3. 内存管理:
```python
# 定期清理已完成订单
self.pending_orders = {k:v for k,v in self.pending_orders.items() 
                      if v['status'] in ['NEW','PARTIALLY_FILLED']}
```

## 6. 实盘部署验证

1. 稳定性测试:
```bash
python 测试代码/实盘接口稳定性测试.py
```

2. 验证指标要求:
- 连接成功率 ≥ 99.9%
- 订单响应时间 ≤ 500ms
- 异常恢复时间 ≤ 3s
- 最大并发订单 ≥ 1000笔/分钟

3. 监控建议:
- 实时监控订单执行状态
- 设置异常自动报警
- 定期生成稳定性报告

## 7. 常见问题

Q: 订单执行超时
A: 检查网络连接，增加超时设置:
```python
response = self.session.post(url, timeout=5.0)
```

Q: 资金计算不准确  
A: 启用严格模式:
```python
self.strict_mode = True  # 启用严格资金检查
```

Q: 性能下降
A: 优化订单簿数据结构:
```python
from sortedcontainers import SortedDict
self.order_book = {'bid': SortedDict(), 'ask': SortedDict()}
