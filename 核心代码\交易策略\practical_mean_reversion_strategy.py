# -*- coding: utf-8 -*-
"""
实用均值回归策略 - 只使用基础价格数据，确保每天1次交易且盈利
核心特点：
1. 只使用OHLCV和SMA数据，不依赖复杂指标
2. 移除持仓检查，支持多次交易
3. 基于价格偏离和成交量的简单逻辑
4. 目标每天1次交易
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class PracticalMeanReversionStrategy:
    """实用均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'PracticalMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 基础技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.01)  # 1%偏离
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.01)  # 1%风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)  # 止损倍数
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 3.0)  # 止盈倍数
        
        # 交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 360)  # 6小时间隔
        self.max_daily_trades = all_params.get('max_daily_trades', 2)  # 每日最多2次
        
        # 简单的质量控制参数
        self.volume_threshold = all_params.get('volume_threshold', 1.0)  # 成交量阈值
        self.min_volatility_pct = all_params.get('min_volatility_pct', 0.005)  # 最小波动率
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.total_signals_generated = 0
        
        print(f"策略 PracticalMeanReversionStrategy 初始化...")
        print(f"PracticalMeanReversionStrategy: 价格偏离={self.price_deviation_pct*100}%, "
              f"盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, "
              f"信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_price_deviation(self, data: Dict[str, Any]) -> bool:
        """检查价格偏离（简单有效）"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 价格低于短期均线一定百分比
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            return deviation >= self.price_deviation_pct
        
        return False
    
    def check_volume_condition(self, data: Dict[str, Any]) -> bool:
        """检查成交量条件（简单）"""
        current_volume = data.get('VOLUME', 0)
        
        if current_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        # 简单的成交量检查：不能太低
        return current_volume >= self.volume_threshold
    
    def check_volatility_condition(self, data: Dict[str, Any]) -> bool:
        """检查波动率条件"""
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        close = data.get('CLOSE', 0)
        
        if high <= 0 or low <= 0 or close <= 0:
            return True
        
        # 简单的波动率检查：当前K线的波动率
        volatility = (high - low) / close
        return volatility >= self.min_volatility_pct
    
    def check_trend_condition(self, data: Dict[str, Any]) -> bool:
        """检查趋势条件（简单）"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 简单的趋势检查：不要在强烈下跌趋势中买入
        trend_ratio = sma_short / sma_long
        return trend_ratio >= 0.98  # 短期均线不能比长期均线低太多
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法 - 移除持仓检查"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 移除持仓检查，允许多次交易
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 实用均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'HIGH', 'LOW', 'VOLUME']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查SMA数据
        if self.sma_short_key not in data:
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取基础数据
        price = data.get('CLOSE')
        high = data.get('HIGH')
        low = data.get('LOW')
        volume = data.get('VOLUME')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key, sma_short)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, high, low, sma_short]):
            return signals
        
        # 计算简单的ATR替代
        atr = (high - low) if (high - low) > 0 else price * 0.02
        
        # 实用的买入条件（简单有效）
        buy_conditions = [
            self.check_price_deviation(data),  # 价格偏离
            self.check_volume_condition(data),  # 成交量条件
            self.check_volatility_condition(data),  # 波动率条件
            self.check_trend_condition(data),  # 趋势条件
        ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'PracticalMeanReversionStrategy',
                'signal_type': 'practical_mean_reversion',
                'reason': f'实用均值回归: 偏离={deviation:.2f}%, 价格={price:.2f}<SMA={sma_short:.2f}, 波动={atr:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            self.total_signals_generated += 1
            
            print(f"[{current_time}] PracticalMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, 价格={price:.2f}<SMA={sma_short:.2f}, "
                  f"数量={position_size:.4f}, 信号#{self.total_signals_generated}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'PracticalMeanReversionStrategy',
            'version': '1.0',
            'description': '实用均值回归策略，只使用基础价格数据',
            'features': [
                '只使用OHLCV和SMA数据',
                '移除持仓检查',
                '简单有效的信号逻辑',
                '目标每天1次交易'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'total_signals_generated': self.total_signals_generated,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
