#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试 - 验证调整后的策略参数
"""

import pandas as pd
import numpy as np

def final_test():
    print("="*60)
    print("最终测试 - 验证调整后的策略参数")
    print("="*60)
    
    try:
        from 配置.系统配置 import Config
        from 核心代码.市场数据.数据获取器 import MarketData
        from 核心代码.因子计算.因子库 import calculate_factors
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        config = Config()
        market_data = MarketData(config)
        
        # 加载数据
        df = market_data.get_market_data("BTCUSDT", "2025-04-01", "2025-04-29", source='local')
        print(f"✅ 加载数据: {len(df)} 条记录")
        
        # 计算因子
        df_with_factors = calculate_factors(df, config.factor_config)
        print(f"✅ 因子计算: {len(df_with_factors.columns)} 列")
        
        # 检查配置中的策略参数
        print(f"\n当前策略配置:")
        for strategy_config in config.strategies:
            if strategy_config['name'] == 'AlphaXInspiredStrategy':
                print(f"  AlphaXInspiredStrategy 参数: {strategy_config['params']}")
                break
        
        # 模拟引擎
        class MockEngine:
            def __init__(self):
                self.current_dt = None
            def get_position_size(self, symbol):
                return 0.0
            def get_portfolio_value(self, data):
                return {'total': 100000.0}
            @property
            def risk_manager(self):
                return MockRiskManager()
        
        class MockRiskManager:
            def calculate_trade_size(self, portfolio_value, price, risk_pct, stop_loss_price):
                if price <= 0 or stop_loss_price <= 0:
                    return 0.0
                risk_amount = portfolio_value * risk_pct
                price_diff = abs(price - stop_loss_price)
                if price_diff <= 0:
                    return 0.0
                size = risk_amount / price_diff
                return min(size, portfolio_value * 0.1 / price)
        
        mock_engine = MockEngine()
        
        # 使用配置中的策略参数
        strategy_config = next(s for s in config.strategies if s['name'] == 'AlphaXInspiredStrategy')
        strategy_params = strategy_config['params']
        
        strategy_class = STRATEGIES['AlphaXInspiredStrategy']
        strategy = strategy_class(mock_engine, ['BTCUSDT'], strategy_params)
        print(f"✅ 策略初始化: AlphaXInspiredStrategy")
        print(f"  RSI超卖阈值: {strategy.rsi_oversold}")
        print(f"  ADX阈值: {strategy.adx_threshold}")
        
        # 寻找满足新条件的时间点
        print(f"\n使用新参数寻找交易机会...")
        
        signal_count = 0
        checked_count = 0
        
        # 检查最后1000个数据点
        start_idx = max(100, len(df_with_factors) - 1000)
        
        for i in range(start_idx, len(df_with_factors), 50):  # 每50行检查一次
            current_data = df_with_factors.iloc[i]
            mock_engine.current_dt = current_data.name
            
            # 检查关键条件
            price = current_data['CLOSE']
            sma_20 = current_data['SMA_20']
            sma_60 = current_data['SMA_60']
            rsi = current_data['RSI_14']
            adx = current_data['ADX_14']
            
            checked_count += 1
            
            # 新的条件（更宽松）
            uptrend = sma_20 > sma_60
            strong_trend = adx > strategy.adx_threshold  # 20.0
            rsi_condition = rsi < strategy.rsi_oversold  # 60.0
            
            if uptrend and strong_trend and rsi_condition:
                print(f"\n✅ 找到满足新条件的时间点: {current_data.name}")
                print(f"   价格: {price:.2f}")
                print(f"   SMA_20 > SMA_60: {sma_20:.2f} > {sma_60:.2f} ✅")
                print(f"   ADX > {strategy.adx_threshold}: {adx:.2f} ✅")
                print(f"   RSI < {strategy.rsi_oversold}: {rsi:.2f} ✅")
                
                # 测试信号生成
                test_data = pd.DataFrame([current_data], index=['BTCUSDT'])
                
                try:
                    signals = strategy.on_bar(test_data)
                    if signals and len(signals) > 0:
                        signal_count += len(signals)
                        print(f"   🎯 成功生成 {len(signals)} 个交易信号!")
                        for j, signal in enumerate(signals):
                            print(f"     信号 {j+1}: {signal['action']} {signal['size']:.4f} @ {signal['price']:.2f}")
                            if signal.get('stop_loss_price'):
                                print(f"              止损: {signal['stop_loss_price']:.2f}")
                            if signal.get('take_profit_price'):
                                print(f"              止盈: {signal['take_profit_price']:.2f}")
                    else:
                        print(f"   ⚠️ 条件满足但仍未生成信号")
                        
                        # 检查策略内部方法
                        uptrend_check = strategy._is_strong_uptrend(current_data)
                        buy_trigger = strategy._is_buy_trigger(current_data)
                        print(f"     内部上升趋势检查: {uptrend_check}")
                        print(f"     内部买入触发检查: {buy_trigger}")
                        
                        # 检查是否有信号间隔限制
                        if hasattr(strategy, 'last_signal_time') and 'BTCUSDT' in strategy.last_signal_time:
                            last_time = strategy.last_signal_time['BTCUSDT']
                            interval = (mock_engine.current_dt - last_time).total_seconds() / 60
                            min_interval = strategy.min_signal_interval_minutes
                            print(f"     信号间隔: {interval:.1f}分钟 (最小: {min_interval}分钟)")
                
                except Exception as e:
                    print(f"   ❌ 信号生成出错: {e}")
                    import traceback
                    traceback.print_exc()
                
                # 找到几个就够了
                if signal_count > 0 or checked_count > 20:
                    break
        
        print(f"\n检查结果:")
        print(f"  检查了 {checked_count} 个时间点")
        print(f"  生成信号: {signal_count} 个")
        
        if signal_count > 0:
            print(f"\n🎉 成功！策略参数调整后能够生成交易信号！")
            return True
        else:
            print(f"\n⚠️ 仍未生成信号，可能需要进一步调整参数或检查策略逻辑")
            
            # 显示最新数据的具体值
            latest = df_with_factors.iloc[-1]
            print(f"\n最新数据分析:")
            print(f"  时间: {latest.name}")
            print(f"  价格: {latest['CLOSE']:.2f}")
            print(f"  SMA_20: {latest['SMA_20']:.2f}")
            print(f"  SMA_60: {latest['SMA_60']:.2f}")
            print(f"  RSI_14: {latest['RSI_14']:.2f}")
            print(f"  ADX_14: {latest['ADX_14']:.2f}")
            print(f"  SMA_20 > SMA_60: {latest['SMA_20'] > latest['SMA_60']}")
            print(f"  ADX > {strategy.adx_threshold}: {latest['ADX_14'] > strategy.adx_threshold}")
            print(f"  RSI < {strategy.rsi_oversold}: {latest['RSI_14'] < strategy.rsi_oversold}")
            
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = final_test()
    print(f"\n{'='*60}")
    if success:
        print(f"🎉 测试成功！系统监控和策略优化现在可以正常工作！")
        print(f"建议重新运行主程序查看交易信号生成。")
    else:
        print(f"⚠️ 需要进一步优化策略参数或检查策略逻辑。")
    print(f"{'='*60}")
