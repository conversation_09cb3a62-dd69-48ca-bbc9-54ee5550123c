# -*- coding: utf-8 -*-
"""
策略优化分析和改进建议
"""
import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def analyze_current_strategy_problems():
    """分析当前策略的主要问题"""
    print("=== 当前AlphaXInspiredStrategy问题分析 ===")
    print()
    
    problems = {
        "胜率过低": {
            "现状": "28.57% (6胜15负)",
            "问题": "策略信号质量不高，入场时机选择有误",
            "原因": [
                "RSI超卖阈值(35)可能过高，导致在下跌趋势中过早入场",
                "ADX趋势强度要求(25)可能不够严格",
                "缺乏市场环境判断，在震荡市中频繁交易"
            ]
        },
        "盈亏比不佳": {
            "现状": "75.80%，平均亏损 > 平均盈利",
            "问题": "止损止盈比例设置不合理",
            "原因": [
                "ATR止损倍数(2.0)可能过小，容易被市场噪音触发",
                "ATR止盈倍数(4.0)相对止损倍数比例不够大",
                "没有考虑市场波动性的动态调整"
            ]
        },
        "交易频率过高": {
            "现状": "7天21笔交易，平均每天3笔",
            "问题": "过度交易导致交易成本累积",
            "原因": [
                "信号间隔时间(120分钟)可能过短",
                "缺乏市场状态过滤机制",
                "没有考虑交易时段的影响"
            ]
        }
    }
    
    for problem, details in problems.items():
        print(f"【{problem}】")
        print(f"  现状: {details['现状']}")
        print(f"  问题: {details['问题']}")
        print("  原因:")
        for reason in details['原因']:
            print(f"    - {reason}")
        print()

def suggest_optimizations():
    """提出优化建议"""
    print("=== 策略优化建议 ===")
    print()
    
    optimizations = {
        "信号质量改进": [
            "调整RSI超卖阈值从35降至25-30，避免在下跌趋势中过早入场",
            "提高ADX趋势强度要求从25增至30-35，确保在强趋势中交易",
            "增加价格相对位置过滤：只在价格接近短期均线时入场",
            "添加成交量确认：要求成交量高于平均水平"
        ],
        "风险管理优化": [
            "动态调整ATR倍数：根据市场波动性调整止损止盈比例",
            "增加ATR止损倍数至2.5-3.0，减少噪音干扰",
            "调整止盈止损比例至1:3或1:4，提高盈亏比",
            "添加时间止损：持仓超过一定时间后强制平仓"
        ],
        "交易频率控制": [
            "延长信号间隔时间至240-360分钟",
            "添加市场状态过滤：避免在高波动或低流动性时段交易",
            "增加连续亏损保护：连续亏损后暂停交易",
            "考虑交易时段：避免在亚洲早盘等低流动性时段交易"
        ],
        "新增技术指标": [
            "添加MACD指标确认趋势方向",
            "使用布林带判断超买超卖状态",
            "引入威廉指标(%R)作为辅助确认",
            "考虑使用KDJ指标的金叉死叉信号"
        ]
    }
    
    for category, suggestions in optimizations.items():
        print(f"【{category}】")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
        print()

def create_optimized_strategy_params():
    """创建优化后的策略参数"""
    print("=== 优化策略参数建议 ===")
    print()
    
    original_params = {
        'sma_short_key': 'SMA_20',
        'sma_long_key': 'SMA_60', 
        'adx_threshold': 25,
        'rsi_oversold': 35,
        'risk_per_trade_pct': 0.01,
        'atr_sl_multiple': 2.0,
        'atr_tp_multiple': 4.0,
        'min_signal_interval_minutes': 120
    }
    
    optimized_params_v1 = {
        'sma_short_key': 'SMA_20',
        'sma_long_key': 'SMA_60',
        'adx_threshold': 30,  # 提高趋势强度要求
        'rsi_oversold': 25,   # 降低RSI阈值，更严格的超卖条件
        'risk_per_trade_pct': 0.01,
        'atr_sl_multiple': 2.5,  # 增加止损距离
        'atr_tp_multiple': 5.0,  # 增加止盈距离，提高盈亏比
        'min_signal_interval_minutes': 240,  # 延长信号间隔
        'max_daily_trades': 2,  # 新增：每日最大交易次数
        'volume_threshold': 1.2,  # 新增：成交量阈值（相对平均成交量）
    }
    
    optimized_params_v2 = {
        'sma_short_key': 'SMA_20',
        'sma_long_key': 'SMA_60',
        'adx_threshold': 35,  # 更严格的趋势要求
        'rsi_oversold': 20,   # 更严格的超卖条件
        'risk_per_trade_pct': 0.008,  # 降低单笔风险
        'atr_sl_multiple': 3.0,  # 更大的止损距离
        'atr_tp_multiple': 6.0,  # 更大的止盈距离
        'min_signal_interval_minutes': 360,  # 更长的信号间隔
        'max_daily_trades': 1,  # 每日最多1笔交易
        'volume_threshold': 1.5,  # 更高的成交量要求
        'price_near_sma': 0.005,  # 新增：价格必须接近短期均线(0.5%以内)
    }
    
    print("原始参数:")
    for key, value in original_params.items():
        print(f"  {key}: {value}")
    
    print("\n优化参数 V1 (保守优化):")
    for key, value in optimized_params_v1.items():
        print(f"  {key}: {value}")
    
    print("\n优化参数 V2 (激进优化):")
    for key, value in optimized_params_v2.items():
        print(f"  {key}: {value}")
    
    return original_params, optimized_params_v1, optimized_params_v2

def generate_test_commands():
    """生成测试命令"""
    print("\n=== 测试命令 ===")
    print()
    
    commands = [
        "# 测试原始策略（已完成）",
        "./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2023-01-01 --end_date 2023-01-07",
        "",
        "# 测试其他策略",
        "./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2023-01-01 --end_date 2023-01-07 --strategy TrendFollowing",
        "./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2023-01-01 --end_date 2023-01-07 --strategy MeanReversion",
        "",
        "# 测试更长时间周期",
        "./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2023-01-01 --end_date 2023-01-31",
        "",
        "# 测试不同初始资金",
        "./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2023-01-01 --end_date 2023-01-07 --initial_cash 50000",
    ]
    
    for cmd in commands:
        print(cmd)

if __name__ == '__main__':
    print("量化策略优化分析报告")
    print("=" * 50)
    print()
    
    # 分析当前问题
    analyze_current_strategy_problems()
    
    # 提出优化建议
    suggest_optimizations()
    
    # 创建优化参数
    create_optimized_strategy_params()
    
    # 生成测试命令
    generate_test_commands()
    
    print("\n=== 下一步行动计划 ===")
    print("1. 测试其他内置策略的表现")
    print("2. 创建优化版本的AlphaX策略")
    print("3. 进行参数敏感性分析")
    print("4. 在更长时间周期上验证策略稳定性")
    print("5. 考虑组合多个策略以分散风险")
