# -*- coding: utf-8 -*-
"""
高频量化交易系统完整性评估器
全面测试系统各个核心功能模块的完整性和有效性
"""

import sys
import os
import importlib
import traceback
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class SystemIntegrityEvaluator:
    """系统完整性评估器"""
    
    def __init__(self):
        self.test_results = {}
        self.core_modules = {}
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
    def evaluate_system_integrity(self):
        """评估系统完整性"""
        print("🚀 开始高频量化交易系统完整性评估...")
        print("=" * 80)
        
        # 1. 核心模块结构评估
        self.evaluate_core_structure()
        
        # 2. 数据层功能测试
        self.test_data_layer()
        
        # 3. 策略层功能测试
        self.test_strategy_layer()
        
        # 4. 执行层功能测试
        self.test_execution_layer()
        
        # 5. 监控报告层测试
        self.test_monitoring_layer()
        
        # 6. 系统集成测试
        self.test_system_integration()
        
        # 7. 生成完整性报告
        self.generate_integrity_report()
        
    def evaluate_core_structure(self):
        """评估核心模块结构"""
        print("\n📁 1. 核心模块结构评估")
        print("-" * 50)
        
        # 检查核心目录结构
        core_directories = [
            "核心代码",
            "核心代码/交易策略", 
            "核心代码/市场数据",
            "核心代码/订单执行",
            "核心代码/风险管理",
            "核心代码/监控与报告",
            "核心代码/资产组合",
            "核心代码/因子计算",
            "核心代码/优化器",
            "配置",
            "测试代码",
            "模型文件"
        ]
        
        structure_score = 0
        for directory in core_directories:
            if os.path.exists(directory):
                print(f"✅ {directory}")
                structure_score += 1
            else:
                print(f"❌ {directory} - 缺失")
        
        self.test_results['core_structure'] = {
            'score': structure_score / len(core_directories),
            'details': f"{structure_score}/{len(core_directories)} 核心目录存在"
        }
        
        print(f"📊 结构完整性: {structure_score}/{len(core_directories)} ({structure_score/len(core_directories)*100:.1f}%)")
        
    def test_data_layer(self):
        """测试数据层功能"""
        print("\n📊 2. 数据层功能测试")
        print("-" * 50)
        
        data_tests = []
        
        # 测试数据加载
        try:
            if os.path.exists(self.data_path):
                files = os.listdir(self.data_path)
                csv_files = [f for f in files if f.endswith('.csv')]
                if csv_files:
                    # 测试加载一个数据文件
                    test_file = os.path.join(self.data_path, csv_files[0])
                    df = pd.read_csv(test_file)
                    if len(df) > 0:
                        print(f"✅ 数据加载功能正常 - 测试文件: {csv_files[0]}")
                        print(f"   数据量: {len(df)} 条记录")
                        data_tests.append(True)
                    else:
                        print(f"❌ 数据文件为空")
                        data_tests.append(False)
                else:
                    print(f"❌ 数据目录中无CSV文件")
                    data_tests.append(False)
            else:
                print(f"❌ 数据目录不存在: {self.data_path}")
                data_tests.append(False)
        except Exception as e:
            print(f"❌ 数据加载测试失败: {e}")
            data_tests.append(False)
        
        # 测试数据处理功能
        try:
            # 创建测试数据
            test_data = pd.DataFrame({
                'timestamp': pd.date_range('2024-01-01', periods=100, freq='1min'),
                'open': np.random.randn(100).cumsum() + 100,
                'high': np.random.randn(100).cumsum() + 102,
                'low': np.random.randn(100).cumsum() + 98,
                'close': np.random.randn(100).cumsum() + 100,
                'volume': np.random.randint(1000, 10000, 100)
            })
            
            # 测试技术指标计算
            test_data['ma'] = test_data['close'].rolling(10).mean()
            test_data['rsi'] = self.calculate_rsi(test_data['close'])
            
            if not test_data['ma'].isna().all() and not test_data['rsi'].isna().all():
                print("✅ 技术指标计算功能正常")
                data_tests.append(True)
            else:
                print("❌ 技术指标计算异常")
                data_tests.append(False)
                
        except Exception as e:
            print(f"❌ 数据处理测试失败: {e}")
            data_tests.append(False)
        
        self.test_results['data_layer'] = {
            'score': sum(data_tests) / len(data_tests),
            'details': f"{sum(data_tests)}/{len(data_tests)} 数据功能正常"
        }
        
    def test_strategy_layer(self):
        """测试策略层功能"""
        print("\n🎯 3. 策略层功能测试")
        print("-" * 50)
        
        strategy_tests = []
        
        # 测试策略文件存在性
        strategy_files = [
            "P08平衡双向交易策略.py",
            "P01阿尔法X2024_最终修复版.py",
            "P02简化均值回归策略.py"
        ]
        
        working_strategies = 0
        for strategy_file in strategy_files:
            if os.path.exists(strategy_file):
                try:
                    # 尝试导入策略
                    module_name = strategy_file.replace('.py', '').replace('-', '_')
                    spec = importlib.util.spec_from_file_location(module_name, strategy_file)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    print(f"✅ {strategy_file} - 可正常导入")
                    working_strategies += 1
                except Exception as e:
                    print(f"❌ {strategy_file} - 导入失败: {str(e)[:50]}...")
            else:
                print(f"❌ {strategy_file} - 文件不存在")
        
        strategy_tests.append(working_strategies > 0)
        
        # 测试策略核心功能
        try:
            # 测试P08策略
            if os.path.exists("P08平衡双向交易策略.py"):
                sys.path.append('.')
                from P08平衡双向交易策略 import BalancedBidirectionalStrategy
                
                strategy = BalancedBidirectionalStrategy()
                
                # 创建测试数据
                test_data = pd.DataFrame({
                    'CLOSE': np.random.randn(100).cumsum() + 100,
                    'HIGH': np.random.randn(100).cumsum() + 102,
                    'LOW': np.random.randn(100).cumsum() + 98,
                    'VOLUME': np.random.randint(1000, 10000, 100)
                })
                test_data.index = pd.date_range('2024-01-01', periods=100, freq='1min')
                
                # 测试指标计算
                processed_data = strategy.calculate_balanced_indicators(test_data)
                
                if 'EMA_Fast' in processed_data.columns and 'RSI' in processed_data.columns:
                    print("✅ 策略指标计算功能正常")
                    strategy_tests.append(True)
                else:
                    print("❌ 策略指标计算异常")
                    strategy_tests.append(False)
                    
        except Exception as e:
            print(f"❌ 策略功能测试失败: {str(e)[:50]}...")
            strategy_tests.append(False)
        
        self.test_results['strategy_layer'] = {
            'score': sum(strategy_tests) / len(strategy_tests),
            'details': f"{sum(strategy_tests)}/{len(strategy_tests)} 策略功能正常"
        }
        
    def test_execution_layer(self):
        """测试执行层功能"""
        print("\n⚡ 4. 执行层功能测试")
        print("-" * 50)
        
        execution_tests = []
        
        # 检查执行相关文件
        execution_files = [
            "核心代码/订单执行",
            "主程序_实盘模拟.py",
            "后台服务_交易引擎.py"
        ]
        
        for file_path in execution_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
                execution_tests.append(True)
            else:
                print(f"❌ {file_path} - 不存在")
                execution_tests.append(False)
        
        # 测试模拟交易功能
        try:
            # 模拟交易逻辑测试
            initial_capital = 100000
            position = 0
            cash = initial_capital
            
            # 模拟买入
            buy_price = 100
            shares = 1000
            position += shares
            cash -= shares * buy_price * 1.0005  # 包含手续费
            
            # 模拟卖出
            sell_price = 105
            cash += position * sell_price * 0.9995  # 包含手续费
            final_equity = cash
            
            profit = final_equity - initial_capital
            if profit > 0:
                print(f"✅ 模拟交易逻辑正常 - 盈利: {profit:.2f}")
                execution_tests.append(True)
            else:
                print(f"❌ 模拟交易逻辑异常")
                execution_tests.append(False)
                
        except Exception as e:
            print(f"❌ 交易逻辑测试失败: {e}")
            execution_tests.append(False)
        
        self.test_results['execution_layer'] = {
            'score': sum(execution_tests) / len(execution_tests),
            'details': f"{sum(execution_tests)}/{len(execution_tests)} 执行功能正常"
        }
        
    def test_monitoring_layer(self):
        """测试监控报告层"""
        print("\n📈 5. 监控报告层测试")
        print("-" * 50)
        
        monitoring_tests = []
        
        # 检查报告相关文件
        report_files = [
            "专业回测报告图表模块.py",
            "月度收益回测图表生成器.py",
            "performance_analysis.py"
        ]
        
        for file_path in report_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
                monitoring_tests.append(True)
            else:
                print(f"❌ {file_path} - 不存在")
                monitoring_tests.append(False)
        
        # 测试图表生成功能
        try:
            import matplotlib.pyplot as plt
            
            # 创建测试图表
            fig, ax = plt.subplots(figsize=(10, 6))
            x = np.linspace(0, 10, 100)
            y = np.sin(x)
            ax.plot(x, y)
            ax.set_title('测试图表')
            
            # 保存测试图表
            test_chart_path = "test_chart.png"
            plt.savefig(test_chart_path)
            plt.close()
            
            if os.path.exists(test_chart_path):
                print("✅ 图表生成功能正常")
                os.remove(test_chart_path)  # 清理测试文件
                monitoring_tests.append(True)
            else:
                print("❌ 图表生成失败")
                monitoring_tests.append(False)
                
        except Exception as e:
            print(f"❌ 图表生成测试失败: {e}")
            monitoring_tests.append(False)
        
        self.test_results['monitoring_layer'] = {
            'score': sum(monitoring_tests) / len(monitoring_tests),
            'details': f"{sum(monitoring_tests)}/{len(monitoring_tests)} 监控功能正常"
        }
        
    def test_system_integration(self):
        """测试系统集成"""
        print("\n🔗 6. 系统集成测试")
        print("-" * 50)
        
        integration_tests = []
        
        # 测试端到端流程
        try:
            print("🔄 执行端到端流程测试...")
            
            # 1. 数据加载
            test_data = pd.DataFrame({
                'timestamp': pd.date_range('2024-01-01', periods=50, freq='1min'),
                'OPEN': np.random.randn(50).cumsum() + 100,
                'HIGH': np.random.randn(50).cumsum() + 102,
                'LOW': np.random.randn(50).cumsum() + 98,
                'CLOSE': np.random.randn(50).cumsum() + 100,
                'VOLUME': np.random.randint(1000, 10000, 50)
            })
            test_data.set_index('timestamp', inplace=True)
            
            # 2. 技术指标计算
            test_data['MA'] = test_data['CLOSE'].rolling(10).mean()
            test_data['RSI'] = self.calculate_rsi(test_data['CLOSE'])
            
            # 3. 信号生成
            test_data['Signal'] = 0
            test_data.loc[test_data['RSI'] < 30, 'Signal'] = 1  # 买入信号
            test_data.loc[test_data['RSI'] > 70, 'Signal'] = -1  # 卖出信号
            
            # 4. 模拟交易
            trades = []
            position = 0
            cash = 100000
            
            for i, row in test_data.iterrows():
                if row['Signal'] == 1 and position == 0:  # 买入
                    shares = 1000
                    position = shares
                    cash -= shares * row['CLOSE'] * 1.0005
                    trades.append({'action': 'buy', 'price': row['CLOSE'], 'shares': shares})
                elif row['Signal'] == -1 and position > 0:  # 卖出
                    cash += position * row['CLOSE'] * 0.9995
                    trades.append({'action': 'sell', 'price': row['CLOSE'], 'shares': position})
                    position = 0
            
            # 5. 性能计算
            final_equity = cash + position * test_data['CLOSE'].iloc[-1]
            total_return = (final_equity - 100000) / 100000
            
            if len(trades) > 0:
                print(f"✅ 端到端流程正常 - 交易次数: {len(trades)}, 收益率: {total_return*100:.2f}%")
                integration_tests.append(True)
            else:
                print("⚠️ 端到端流程完成但无交易信号")
                integration_tests.append(True)
                
        except Exception as e:
            print(f"❌ 端到端流程测试失败: {e}")
            integration_tests.append(False)
        
        # 测试配置文件
        config_files = ["配置/系统配置.py", "requirements.txt"]
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✅ {config_file}")
                integration_tests.append(True)
            else:
                print(f"❌ {config_file} - 不存在")
                integration_tests.append(False)
        
        self.test_results['system_integration'] = {
            'score': sum(integration_tests) / len(integration_tests),
            'details': f"{sum(integration_tests)}/{len(integration_tests)} 集成功能正常"
        }
        
    def calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
        
    def generate_integrity_report(self):
        """生成完整性报告"""
        print("\n📋 7. 系统完整性评估报告")
        print("=" * 80)
        
        total_score = 0
        total_weight = 0
        
        # 权重设置
        weights = {
            'core_structure': 0.15,
            'data_layer': 0.25,
            'strategy_layer': 0.25,
            'execution_layer': 0.20,
            'monitoring_layer': 0.10,
            'system_integration': 0.05
        }
        
        print("📊 各模块评估结果:")
        print("-" * 50)
        
        for module, result in self.test_results.items():
            score = result['score']
            weight = weights.get(module, 0)
            weighted_score = score * weight
            total_score += weighted_score
            total_weight += weight
            
            status = "🎉" if score >= 0.8 else "✅" if score >= 0.6 else "⚠️" if score >= 0.4 else "❌"
            
            print(f"{status} {module.replace('_', ' ').title()}: {score*100:.1f}% (权重: {weight*100:.0f}%)")
            print(f"   {result['details']}")
        
        overall_score = total_score / total_weight if total_weight > 0 else 0
        
        print("\n🎯 系统整体评估:")
        print("-" * 50)
        print(f"整体完整性得分: {overall_score*100:.1f}%")
        
        if overall_score >= 0.9:
            grade = "A+ (优秀)"
            recommendation = "系统完整性优秀，可投入生产使用"
        elif overall_score >= 0.8:
            grade = "A (良好)"
            recommendation = "系统完整性良好，建议小幅优化后使用"
        elif overall_score >= 0.7:
            grade = "B (一般)"
            recommendation = "系统基本完整，需要重点改进薄弱环节"
        elif overall_score >= 0.6:
            grade = "C (及格)"
            recommendation = "系统勉强可用，需要大幅改进"
        else:
            grade = "D (不及格)"
            recommendation = "系统存在重大缺陷，不建议使用"
        
        print(f"系统等级: {grade}")
        print(f"建议: {recommendation}")
        
        # 保存报告
        report_content = f"""
高频量化交易系统完整性评估报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

整体评估:
- 完整性得分: {overall_score*100:.1f}%
- 系统等级: {grade}
- 建议: {recommendation}

各模块详细评估:
"""
        
        for module, result in self.test_results.items():
            report_content += f"\n{module.replace('_', ' ').title()}:\n"
            report_content += f"  得分: {result['score']*100:.1f}%\n"
            report_content += f"  详情: {result['details']}\n"
        
        with open(f"系统完整性评估报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📄 详细报告已保存")
        
        return overall_score

def main():
    """主函数"""
    evaluator = SystemIntegrityEvaluator()
    overall_score = evaluator.evaluate_system_integrity()
    
    print(f"\n🎯 评估完成! 系统完整性得分: {overall_score*100:.1f}%")

if __name__ == "__main__":
    main()
