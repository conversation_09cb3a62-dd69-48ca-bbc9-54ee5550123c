# -*- coding: utf-8 -*-
import logging
from typing import Dict, List, Tuple, Optional, Any, Type
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from collections import defaultdict

# --- 导入与配置 ---
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    BASE_STRATEGY = BacktestStrategy
    backtesting_available = True
except ImportError:
    logging.warning("backtesting库未安装，部分基于该库的策略功能可能受限。")
    # Dummy classes for environments without backtesting
    class DummyStrategy:
         def __init__(self, broker, data, params): self.parameters = params or {} # Store params
         def init(self): pass
         def next(self): pass
         def I(self, func, *args, **kwargs): return pd.Series(dtype=np.float64)
         @property
         def data(self): return pd.DataFrame({'Open':[], 'High':[], 'Low':[], 'Close':[], 'Volume':[]})
         @property
         def position(self):
              class MP: size=0;is_long=False;is_short=False;pl_pct=0.0;entry_price=0.0;def close(self):pass; return MP();
         def buy(self,**kwargs):pass; def sell(self,**kwargs):pass
    BASE_STRATEGY = DummyStrategy
    backtesting_available = False
    def crossover(s1, s2): return False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 辅助函数和数据类 ---
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool:
    """验证输入数据的有效性，检查必要的列（大小写不敏感）"""
    if data is None or data.empty: logger.error("输入数据为空"); return False
    if not isinstance(data, pd.DataFrame): logger.error(f"输入类型错误: {type(data)}"); return False
    data_cols_lower = {str(c).lower(): c for c in data.columns} # Map lower to original
    required_cols_lower = [c.lower() for c in required_cols]
    missing_cols = []
    for col_lower in required_cols_lower:
        if col_lower not in data_cols_lower:
            original_col = next((c for c in required_cols if c.lower() == col_lower), col_lower.capitalize())
            missing_cols.append(original_col)
    if missing_cols: logger.error(f"输入数据缺少列: {missing_cols}"); return False
    # 检查NaN (只检查必要的列)
    check_cols_original = [data_cols_lower[c_lower] for c_lower in required_cols_lower]
    if data[check_cols_original].isnull().values.any(): logger.warning("输入数据包含NaN值")
    return True

@dataclass
class BacktestResult:
    """存储回测结果的数据类"""
    sharpe: float = np.nan
    returns: float = np.nan # 总收益率
    drawdown: float = np.nan # 最大回撤幅度 (正数)
    trades: int = 0
    win_rate: float = np.nan
    net_return: float = np.nan # 考虑交易成本后的净收益率
    annual_return: float = np.nan
    volatility: float = np.nan
    calmar_ratio: float = np.nan

# --- 交易策略基类 (因子版) ---
class TradingStrategy(BASE_STRATEGY):
    """
    交易策略基类 - 假设因子已预先计算并包含在数据中。
    子类应定义所需的因子列名作为类属性或在参数中传递。
    """
    # 策略逻辑参数 (可被覆盖)
    stop_loss_pct: Optional[float] = 0.05 # 可选，None表示不使用此内置止损
    take_profit_pct: Optional[float] = 0.10 # 可选，None表示不使用此内置止盈

    # 内部属性
    parameters: Dict[str, Any] = field(default_factory=dict)
    transaction_cost_pct: float = 0.001
    risk_manager = None
    _data = None # 用于非backtesting框架的数据

    def __init__(self, broker=None, data=None, params: Optional[Dict] = None):
        """初始化策略，合并默认参数和传入参数"""
        # 1. 初始化实例参数字典
        self.parameters = {}
        # 2. 获取类定义的默认参数 (非魔法方法、非可调用、特定类型)
        cls = self.__class__
        for name, value in cls.__dict__.items():
            if not name.startswith('_') and not callable(value) and isinstance(value, (int, float, str, bool, list, dict, type(None))):
                 # 排除从基类继承的 parameters 字典本身
                 if name != 'parameters':
                      self.parameters[name] = value
        # 3. 更新传入的参数
        if params:
            self.parameters.update(params)
        # 4. 将最终参数设置到实例属性 (为了 backtesting 库能访问)
        for key, value in self.parameters.items():
            setattr(self, key, value) # 动态设置实例属性

        # 5. 调用 backtesting 父类构造函数 (如果可用)
        if backtesting_available:
            super().__init__(broker, data, self.parameters)

        # 6. 设置交易成本
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))

        # 7. 处理数据 (如果直接传入)
        if data is not None and not isinstance(data, list):
             self.set_data(data) # Use setter for validation


    def set_data(self, data: pd.DataFrame):
        if validate_input(data): # 假设验证基础OHLC
            self._data = data
            # 模拟 backtesting 的 self.data 行为 (简化)
            if not hasattr(self, 'data') or self.data is None:
                 # backtesting 的 self.data 是特殊对象，这里用DataFrame简化模拟
                 if backtesting_available:
                      # 在backtesting环境外，我们不能完全模拟其数据访问方式
                      # logger.warning("Cannot fully simulate backtesting's self.data outside its environment.")
                      # setattr(self, 'data', data) # 直接赋值可能导致问题
                      pass
                 else: # 如果 backtesting 不可用
                      # setattr(self, 'data', data)
                      pass
        else:
            raise ValueError("设置的数据无效")

    def set_transaction_cost(self, cost_pct: float):
        if 0 <= cost_pct < 1:
            self.transaction_cost_pct = cost_pct
            self.parameters['transaction_cost_pct'] = cost_pct
        else:
            logger.error(f"无效的交易成本比例: {cost_pct}")

    def init(self):
        """
        初始化。子类应在此检查所需因子列是否存在于 self.data 中。
        可以使用 self.I() 计算因子的衍生指标。
        """
        logger.debug(f"{self.__class__.__name__} 策略初始化 (init)。参数: {self.parameters}")
        # 检查 stop_loss_pct 和 take_profit_pct 是否有效
        self.stop_loss_val = self.parameters.get('stop_loss_pct')
        self.take_profit_val = self.parameters.get('take_profit_pct')
        if self.stop_loss_val is not None and not (0 < self.stop_loss_val < 1):
            logger.warning(f"无效的止损比例 {self.stop_loss_val}, 将禁用止损")
            self.stop_loss_val = None
        if self.take_profit_val is not None and not (self.take_profit_val > 0):
            logger.warning(f"无效的止盈比例 {self.take_profit_val}, 将禁用止盈")
            self.take_profit_val = None
        pass # 子类实现

    def next(self):
        """
        交易逻辑。子类实现，通过 self.data.FactorName[-1] 访问最新因子值。
        """
        raise NotImplementedError("子类必须实现 next 方法")

    def _get_sl_tp_prices(self) -> tuple[Optional[float], Optional[float]]:
        """辅助函数：根据当前价格和配置计算止损止盈价"""
        sl_price, tp_price = None, None
        if self.stop_loss_val is not None or self.take_profit_val is not None:
             try:
                  # 在 next 方法中访问最新价格
                  price = self.data.Close[-1]
                  if self.stop_loss_val is not None:
                       # sl = price * self.stop_loss_val # backtesting框架处理百分比
                       sl_price = price * (1 - self.stop_loss_val) # 假设多头
                  if self.take_profit_val is not None:
                       # tp = price * self.take_profit_val
                       tp_price = price * (1 + self.take_profit_val) # 假设多头
             except IndexError:
                  logger.warning("无法获取最新价格计算 SL/TP")
             except Exception as e:
                  logger.error(f"计算 SL/TP 时出错: {e}")
        # 注意：backtesting 的 buy/sell 会自动处理多空方向的 SL/TP 价格
        # 返回的 sl 和 tp 参数应为价格值或百分比（取决于框架）
        # backtesting v0.3.3+ 接受价格
        # return sl_price, tp_price
        # 或者返回比例供 buy/sell 使用 (backtesting框架推荐)
        return self.stop_loss_val, self.take_profit_val


    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """生成信号DataFrame - 子类应覆盖此方法"""
        target_data = data if data is not None else self._data
        index = pd.Index([]) if target_data is None else target_data.index
        logger.warning(f"{self.__class__.__name__} 未实现 generate_signals_dataframe (因子版)，返回全0信号")
        return pd.DataFrame({'Signal': 0}, index=index)

    def run_simple_backtest(self, data: Optional[pd.DataFrame] = None, initial_capital: float = 100000.0) -> BacktestResult:
        """运行简单的向量化回测"""
        # ... (与上一个回答中的实现相同) ...
        target_data = data if data is not None else self._data
        if not validate_input(target_data): return BacktestResult()
        if not isinstance(target_data.index, pd.DatetimeIndex): return BacktestResult()
        signals_df = self.generate_signals_dataframe(target_data)
        if 'Signal' not in signals_df.columns: return BacktestResult()
        merged_df = target_data.join(signals_df[['Signal']], how='left')
        merged_df['Position'] = merged_df['Signal'].ffill().fillna(0).astype(int)
        merged_df['MarketReturn'] = merged_df['Close'].pct_change()
        merged_df['StrategyReturn'] = merged_df['Position'].shift(1) * merged_df['MarketReturn']
        trades = merged_df['Position'].diff().fillna(0) != 0
        trade_cost = trades * self.transaction_cost_pct # 单边成本更合理
        merged_df['StrategyReturn'] -= trade_cost
        merged_df['StrategyReturn'] = merged_df['StrategyReturn'].fillna(0)
        merged_df['EquityCurve'] = initial_capital * (1 + merged_df['StrategyReturn']).cumprod()
        # ... (指标计算逻辑与上一个回答相同) ...
        final_equity = merged_df['EquityCurve'].iloc[-1] if not merged_df.empty else initial_capital
        initial_equity = initial_capital # Start from initial capital
        total_return = (final_equity / initial_equity) - 1 if initial_equity != 0 else 0.0
        daily_strategy_returns = merged_df['StrategyReturn'].iloc[1:]
        if daily_strategy_returns.empty or daily_strategy_returns.std()==0: sharpe,volatility,calmar,annual_return=0.0,0.0,0.0,0.0
        else: volatility=daily_strategy_returns.std()*np.sqrt(252); sharpe=np.sqrt(252)*daily_strategy_returns.mean()/daily_strategy_returns.std(); years=max((merged_df.index[-1]-merged_df.index[0]).days/365.25,1/252); annual_return=(1+total_return)**(1/years)-1
        rolling_max=merged_df['EquityCurve'].cummax(); daily_drawdown=(merged_df['EquityCurve']/rolling_max)-1.0; max_drawdown=abs(daily_drawdown.min())
        if max_drawdown==0: calmar=np.inf if annual_return>0 else 0
        else: calmar=annual_return/max_drawdown
        num_trades = trades.sum() # 总的换仓次数
        trade_returns = merged_df.loc[trades, 'StrategyReturn'] # 注意这包含了成本
        win_rate = (trade_returns > -self.transaction_cost_pct).sum() / len(trade_returns) if len(trade_returns) > 0 else 0.0 # 收益大于成本算赢
        logger.info(f"简易回测: Ret={total_return:.2%}, Sharpe={sharpe:.2f}, MaxDD={max_drawdown:.2%}, Trades={num_trades}, WinRate={win_rate:.1%}")
        return BacktestResult(sharpe=float(sharpe), returns=float(total_return), drawdown=float(max_drawdown), trades=int(num_trades), win_rate=float(win_rate), net_return=float(total_return), annual_return=float(annual_return), volatility=float(volatility), calmar_ratio=float(calmar) if calmar!=np.inf else None)


    # 其他方法省略...
    def backtest_with_shocks(self, crisis_data: Dict[str, Dict[str, float]], broker_simulator: Any, prices: pd.DataFrame) -> Dict: pass
    def apply_price_shock(self, change_pct: float): pass
    def grid_search(self, param_ranges: Dict[str, List], data: pd.DataFrame) -> Dict[str, Any]: pass
    def parameter_sensitivity_matrix(self, param_ranges: Dict[str, List], data: pd.DataFrame) -> pd.DataFrame: pass
    def generate_synthetic_data(self, vol_state: str = 'low_vol', trend_state: str = 'strong_trend', n_points: int = 1000) -> pd.DataFrame: pass
    def adjust_strategy_parameters(self) -> None: pass
    def execute_live_trade(self, broker: Any, symbol: str, quantity: int): pass
    def run_stress_test(self, scenario: str, data: pd.DataFrame) -> BacktestResult: pass


# --- 具体策略实现 (使用因子列) ---

class TrendFollowingStrategy(TradingStrategy):
    """移动平均线交叉策略 - 使用预计算的因子"""
    # 因子名称参数 (必须在数据中存在)
    short_factor_name: str = 'SMA_20'
    long_factor_name: str = 'SMA_60'
    stop_loss_pct: Optional[float] = 0.05
    take_profit_pct: Optional[float] = 0.15

    def init(self):
        """检查因子列并赋值"""
        missing = []
        if not hasattr(self.data, self.short_factor_name): missing.append(self.short_factor_name)
        if not hasattr(self.data, self.long_factor_name): missing.append(self.long_factor_name)
        if missing: raise ValueError(f"策略 {self.__class__.__name__} 数据缺少因子列: {missing}")
        # 直接访问因子 Series
        self.short_ma = self.data[self.short_factor_name]
        self.long_ma = self.data[self.long_factor_name]
        logger.info(f"TrendFollowingStrategy initialized with factors: {self.short_factor_name}, {self.long_factor_name}")

    def next(self):
        """交易逻辑"""
        sl_val, tp_val = self._get_sl_tp_prices() # 获取 SL/TP 价格或比例

        # 使用 Backtesting 库的 crossover 函数
        if crossover(self.short_ma, self.long_ma):
            if not self.position:
                self.buy(sl=sl_val, tp=tp_val) # 使用计算出的SL/TP价格或比例
        elif crossover(self.long_ma, self.short_ma):
            if self.position:
                self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """为简单回测生成信号"""
        target_data = data if data is not None else self._data
        required = ['Close', self.short_factor_name, self.long_factor_name]
        if not validate_input(target_data, required):
             index = pd.Index([]) if target_data is None else target_data.index
             return pd.DataFrame({'Signal': 0}, index=index)

        signals_df = pd.DataFrame(index=target_data.index)
        signals_df['RawSignal'] = np.where(target_data[self.short_factor_name] > target_data[self.long_factor_name], 1, -1)
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]


class MeanReversionStrategy(TradingStrategy):
    """均值回归策略 - 使用预计算的因子 (BBands, RSI)"""
    bbands_factor_prefix: str = 'BBands_Default' # BBands因子前缀
    rsi_factor_name: str = 'RSI_14'
    rsi_overbought: float = 70.0
    rsi_oversold: float = 30.0
    close_on_mid: bool = True # 是否在中轨附近平仓
    stop_loss_pct: Optional[float] = 0.03
    take_profit_pct: Optional[float] = 0.08

    def init(self):
        """检查因子列"""
        self.bb_lower_col = f"{self.bbands_factor_prefix}_BB_Lower"
        self.bb_upper_col = f"{self.bbands_factor_prefix}_BB_Upper"
        self.bb_middle_col = f"{self.bbands_factor_prefix}_BB_Middle" # 需要中轨平仓
        required = [self.bb_lower_col, self.bb_upper_col, self.rsi_factor_name]
        if self.close_on_mid: required.append(self.bb_middle_col)
        missing = [f for f in required if not hasattr(self.data, f)]
        if missing: raise ValueError(f"策略 {self.__class__.__name__} 数据缺少因子列: {missing}")

        self.bb_lower = self.data[self.bb_lower_col]
        self.bb_upper = self.data[self.bb_upper_col]
        self.rsi = self.data[self.rsi_factor_name]
        if self.close_on_mid: self.bb_middle = self.data[self.bb_middle_col]
        logger.info(f"MeanReversionStrategy initialized with factors: BBands prefix='{self.bbands_factor_prefix}', RSI='{self.rsi_factor_name}'")


    def next(self):
        """交易逻辑"""
        # 访问最新因子值
        try:
            price = self.data.Close[-1]
            bb_low_val = self.bb_lower[-1]
            bb_up_val = self.bb_upper[-1]
            rsi_val = self.rsi[-1]
            bb_mid_val = self.bb_middle[-1] if self.close_on_mid and len(self.bb_middle) > 0 else None
        except IndexError:
            return # 数据不足

        sl_val, tp_val = self._get_sl_tp_prices()

        # 做空信号
        if price > bb_up_val and rsi_val > self.rsi_overbought:
            if not self.position:
                self.sell(sl=sl_val, tp=tp_val) # SL/TP比例由框架处理方向
        # 做多信号
        elif price < bb_low_val and rsi_val < self.rsi_oversold:
            if not self.position:
                self.buy(sl=sl_val, tp=tp_val)
        # 平仓信号
        elif self.position and self.close_on_mid and bb_mid_val is not None:
            if self.position.is_long and price >= bb_mid_val:
                self.position.close()
            elif self.position.is_short and price <= bb_mid_val:
                self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """为简单回测生成信号"""
        target_data = data if data is not None else self._data
        bb_low_col = f"{self.bbands_factor_prefix}_BB_Lower"; bb_up_col = f"{self.bbands_factor_prefix}_BB_Upper"; rsi_col = self.rsi_factor_name
        required = ['Close', bb_low_col, bb_up_col, rsi_col]
        if not validate_input(target_data, required):
             index = pd.Index([]) if target_data is None else target_data.index
             return pd.DataFrame({'Signal': 0}, index=index)

        signals_df = pd.DataFrame(index=target_data.index)
        buy_cond = (target_data['Close'] < target_data[bb_low_col]) & (target_data[rsi_col] < self.rsi_oversold)
        sell_cond = (target_data['Close'] > target_data[bb_up_col]) & (target_data[rsi_col] > self.rsi_overbought)
        signals_df['RawSignal'] = np.where(buy_cond, 1, np.where(sell_cond, -1, 0))
        # 平仓逻辑简化：持有信号直到反向信号出现
        signals_df['Position'] = signals_df['RawSignal'].replace(0, method='ffill').fillna(0)
        signals_df['Signal'] = signals_df['Position'].diff().fillna(0)
        return signals_df[['Signal']]


class EnhancedMeanReversionStrategy(TradingStrategy):
    """增强版均值回归策略 - 结合多个因子"""
    # 基本因子
    bbands_factor_prefix: str = 'BBands_Default'
    rsi_factor_name: str = 'RSI_14'
    atr_factor_name: str = 'ATR_14'
    macd_fast_factor_name: str = 'MACD_Fast'
    macd_signal_factor_name: str = 'MACD_Signal'
    
    # 策略参数
    rsi_overbought: float = 70.0
    rsi_oversold: float = 30.0
    atr_multiplier: float = 2.0  # ATR倍数用于动态止损
    macd_confirmation: bool = True  # 是否使用MACD确认信号
    position_size_pct: float = 0.1  # 每笔交易仓位比例
    trailing_stop: bool = True  # 是否使用追踪止损
    
    def init(self):
        """检查并初始化因子"""
        required = [
            self.bbands_factor_prefix + '_BB_Lower',
            self.bbands_factor_prefix + '_BB_Upper',
            self.rsi_factor_name,
            self.atr_factor_name
        ]
        if self.macd_confirmation:
            required.extend([self.macd_fast_factor_name, self.macd_signal_factor_name])
            
        missing = [f for f in required if not hasattr(self.data, f)]
        if missing:
            raise ValueError(f"策略 {self.__class__.__name__} 数据缺少因子列: {missing}")
            
        self.bb_lower = self.data[self.bbands_factor_prefix + '_BB_Lower']
        self.bb_upper = self.data[self.bbands_factor_prefix + '_BB_Upper']
        self.rsi = self.data[self.rsi_factor_name]
        self.atr = self.data[self.atr_factor_name]
        if self.macd_confirmation:
            self.macd_fast = self.data[self.macd_fast_factor_name]
            self.macd_signal = self.data[self.macd_signal_factor_name]
            
        logger.info(f"EnhancedMeanReversionStrategy initialized with factors: {required}")

    def next(self):
        """交易逻辑"""
        try:
            price = self.data.Close[-1]
            bb_low = self.bb_lower[-1]
            bb_up = self.bb_upper[-1]
            rsi = self.rsi[-1]
            atr = self.atr[-1]
            if self.macd_confirmation:
                macd_fast = self.macd_fast[-1]
                macd_signal = self.macd_signal[-1]
        except IndexError:
            return
            
        # 计算动态止损
        stop_loss = self.atr_multiplier * atr
        
        # 做空信号
        if (price > bb_up and rsi > self.rsi_overbought and 
            (not self.macd_confirmation or macd_fast < macd_signal)):
            if not self.position:
                size = int(self.position_size_pct * self.equity / price)
                self.sell(size=size, sl=price + stop_loss, tp=price - stop_loss)
                
        # 做多信号
        elif (price < bb_low and rsi < self.rsi_oversold and
              (not self.macd_confirmation or macd_fast > macd_signal)):
            if not self.position:
                size = int(self.position_size_pct * self.equity / price)
                self.buy(size=size, sl=price - stop_loss, tp=price + stop_loss)
                
        # 追踪止损
        if self.position and self.trailing_stop:
            if self.position.is_long:
                self.position.sl = max(self.position.sl or 0, price - stop_loss)
            elif self.position.is_short:
                self.position.sl = min(self.position.sl or float('inf'), price + stop_loss)

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """生成信号DataFrame"""
        target_data = data if data is not None else self._data
        required = ['Close']
        required.extend([
            self.bbands_factor_prefix + '_BB_Lower',
            self.bbands_factor_prefix + '_BB_Upper',
            self.rsi_factor_name,
            self.atr_factor_name
        ])
        if self.macd_confirmation:
            required.extend([self.macd_fast_factor_name, self.macd_signal_factor_name])
            
        if not validate_input(target_data, required):
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)
            
        signals_df = pd.DataFrame(index=target_data.index)
        
        # 生成信号
        bb_low = target_data[self.bbands_factor_prefix + '_BB_Lower']
        bb_up = target_data[self.bbands_factor_prefix + '_BB_Upper']
        rsi = target_data[self.rsi_factor_name]
        price = target_data['Close']
        
        buy_cond = (price < bb_low) & (rsi < self.rsi_oversold)
        sell_cond = (price > bb_up) & (rsi > self.rsi_overbought)
        
        if self.macd_confirmation:
            macd_fast = target_data[self.macd_fast_factor_name]
            macd_signal = target_data[self.macd_signal_factor_name]
            buy_cond &= (macd_fast > macd_signal)
            sell_cond &= (macd_fast < macd_signal)
            
        signals_df['RawSignal'] = np.where(buy_cond, 1, np.where(sell_cond, -1, 0))
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        
        return signals_df[['Signal']]


class TrendWithVolatilityStrategy(TradingStrategy):
    """趋势+波动率过滤策略 - 使用预计算因子"""
    short_factor_name: str = 'SMA_20'
    long_factor_name: str = 'SMA_60'
    volatility_factor_name: str = 'Volatility_20'
    vol_threshold: float = 0.20 # 波动率阈值
    stop_loss_pct: Optional[float] = 0.06
    take_profit_pct: Optional[float] = 0.12

    def init(self):
        """检查因子列"""
        required = [self.short_factor_name, self.long_factor_name, self.volatility_factor_name]
        missing = [f for f in required if not hasattr(self.data, f)]
        if missing: raise ValueError(f"策略 {self.__class__.__name__} 数据缺少因子列: {missing}")
        self.short_ma = self.data[self.short_factor_name]
        self.long_ma = self.data[self.long_factor_name]
        self.volatility = self.data[self.volatility_factor_name]
        logger.info(f"TrendWithVolatilityStrategy initialized with factors: MA='{self.short_factor_name}'/'{self.long_factor_name}', Vol='{self.volatility_factor_name}'")


    def next(self):
        """交易逻辑"""
        try:
            current_vol = self.volatility[-1]
        except IndexError: return # 数据不足

        if pd.isna(current_vol): return # 波动率无效

        sl_val, tp_val = self._get_sl_tp_prices()

        if current_vol < self.vol_threshold:
            if self.position: self.position.close() # 低波动平仓
            return # 低波动不交易

        # 趋势信号
        if crossover(self.short_ma, self.long_ma):
            if not self.position: self.buy(sl=sl_val, tp=tp_val)
        elif crossover(self.long_ma, self.short_ma):
            if self.position: self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """为简单回测生成信号"""
        target_data = data if data is not None else self._data
        required = ['Close', self.short_factor_name, self.long_factor_name, self.volatility_factor_name]
        if not validate_input(target_data, required):
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)

        signals_df = pd.DataFrame(index=target_data.index)
        volatility = target_data[self.volatility_factor_name]
        trend_signal = np.where(target_data[self.short_factor_name] > target_data[self.long_factor_name], 1, -1)
        # 应用过滤
        signals_df['RawSignal'] = np.where(volatility >= self.vol_threshold, trend_signal, 0)
        # 生成交易动作信号
        signals_df['Position'] = signals_df['RawSignal'].replace(0, method='ffill').fillna(0) # 信号出现时持有
        signals_df['Signal'] = signals_df['Position'].diff().fillna(0)
        return signals_df[['Signal']]


# class EnhancedTrendFollowingStrategy(TradingStrategy):
#     """增强版趋势跟踪策略 - 结合多个因子 (示例结构)"""
#     short_term_ma_factor = 'SMA_20'
#     long_term_ma_factor = 'SMA_60'
#     volatility_factor = 'Volatility_20'
#     rsi_factor = 'RSI_14'
#     vol_threshold = 0.25
#     rsi_confirm_level = 50 # 例如，只在RSI>50时做多，RSI<50时做空
#     stop_loss_pct = 0.04
#     take_profit_pct = 0.10

#     def init(self):
#         required = [self.short_term_ma_factor, self.long_term_ma_factor, self.volatility_factor, self.rsi_factor]
#         missing = [f for f in required if not hasattr(self.data, f)]
#         if missing: raise ValueError(f"策略 {self.__class__.__name__} 数据缺少因子列: {missing}")
#         self.short_ma = self.data[self.short_term_ma_factor]
#         self.long_ma = self.data[self.long_term_ma_factor]
#         self.volatility = self.data[self.volatility_factor]
#         self.rsi = self.data[self.rsi_factor]
#         logger.info("EnhancedTrendFollowingStrategy initialized.")

#     def next(self):
#         try:
#              current_vol = self.volatility[-1]
#              current_rsi = self.rsi[-1]
#         except IndexError: return
#         if pd.isna(current_vol) or pd.isna(current_rsi): return

#         sl_val, tp_val = self._get_sl_tp_prices()

#         # 过滤低波动
#         if current_vol < self.vol_threshold:
#              if self.position: self.position.close()
#              return

#         # 金叉 + RSI 确认
#         if crossover(self.short_ma, self.long_ma) and current_rsi > self.rsi_confirm_level:
#             if not self.position.is_long: # 如果有空仓先平掉
#                  if self.position.is_short: self.position.close()
#                  self.buy(sl=sl_val, tp=tp_val)
#         # 死叉 + RSI 确认
#         elif crossover(self.long_ma, self.short_ma) and current_rsi < self.rsi_confirm_level:
#              if not self.position.is_short: # 如果有多仓先平掉
#                   if self.position.is_long: self.position.close()
#                   # 如果允许做空
#                   # self.sell(sl=sl_val, tp=tp_val)
#                   pass # 仅平多仓示例

# --- 策略注册表 ---
# 移除 EnhancedMeanReversionStrategy，保留因子版本的策略
STRATEGIES: Dict[str, Type[TradingStrategy]] = {
    'MeanReversion': MeanReversionStrategy,
    'EnhancedMeanReversion': EnhancedMeanReversionStrategy,
    'RevisedTrend': TrendFollowingStrategy,
    'TrendWithVolatility': TrendWithVolatilityStrategy
}

# --- 内部测试 ---
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    try:
         from ..核心代码.市场数据.数据获取器 import MarketData, load_sample_data
         from ..配置.系统配置 import Config
         from ..核心代码.因子计算.因子库 import calculate_factors # Need this for testing
    except ImportError:
         import sys; import os; project_root=os.path.abspath(os.path.join(os.path.dirname(__file__),'..','..'));
         if project_root not in sys.path: sys.path.insert(0,project_root)
         from 核心代码.市场数据.数据获取器 import MarketData, load_sample_data
         from 配置.系统配置 import Config
         from 核心代码.因子计算.因子库 import calculate_factors

    # 1. Generate sample data
    raw_data = load_sample_data('AAPL', '2022-01-01', '2023-12-31')

    if raw_data is not None:
        # 2. Define factor config for testing (should match strategy needs)
        test_factor_config = {
             'SMA_20': {'function': 'SMA', 'params': {'window': 20}},
             'SMA_60': {'function': 'SMA', 'params': {'window': 60}},
             'RSI_14': {'function': 'RSI', 'params': {'window': 14}},
             'BBands_Default': {'function': 'BBands', 'params': {}},
             'Volatility_20': {'function': 'Volatility', 'params': {'window': 20}},
        }
        # 3. Calculate factors on sample data
        test_data_with_factors = calculate_factors(raw_data, test_factor_config)
        print("Data with factors generated (tail):")
        print(test_data_with_factors.tail())
        print("Columns:", test_data_with_factors.columns.tolist())


        if test_data_with_factors is not None and not test_data_with_factors.empty:
            print("\n--- Testing factor-based strategies (simple backtest) ---")
            # Strategies should now use factor names defined in test_factor_config
            trend_strat_fac = TrendFollowingStrategy(params={'short_factor_name':'SMA_20', 'long_factor_name':'SMA_60', 'stop_loss_pct': 0.06})
            mr_strat_fac = MeanReversionStrategy(params={'bbands_factor_prefix':'BBands_Default', 'rsi_factor_name':'RSI_14', 'rsi_oversold': 25, 'rsi_overbought': 75})
            vol_trend_strat = TrendWithVolatilityStrategy(params={'short_factor_name':'SMA_20', 'long_factor_name':'SMA_60', 'volatility_factor_name': 'Volatility_20', 'vol_threshold': 0.15})

            for strat in [trend_strat_fac, mr_strat_fac, vol_trend_strat]:
                print(f"\nTesting {strat.__class__.__name__}...")
                # Check if required factors exist before running
                required_factors_for_strat = []
                if hasattr(strat,'short_factor_name'): required_factors_for_strat.append(strat.short_factor_name)
                if hasattr(strat,'long_factor_name'): required_factors_for_strat.append(strat.long_factor_name)
                if hasattr(strat,'rsi_factor_name'): required_factors_for_strat.append(strat.rsi_factor_name)
                if hasattr(strat,'volatility_factor_name'): required_factors_for_strat.append(strat.volatility_factor_name)
                if hasattr(strat,'bbands_factor_prefix'):
                     required_factors_for_strat.extend([f"{strat.bbands_factor_prefix}_BB_Lower", f"{strat.bbands_factor_prefix}_BB_Upper", f"{strat.bbands_factor_prefix}_BB_Middle"])

                missing_in_data = [f for f in required_factors_for_strat if f not in test_data_with_factors.columns]
                if missing_in_data:
                     print(f"SKIPPING: Data missing required factors for {strat.__class__.__name__}: {missing_in_data}")
                     continue

                result = strat.run_simple_backtest(test_data_with_factors)
                print(f"结果: {result}")
                if result.trades == 0: print("警告：回测未产生交易。")
        else: print("无法生成带因子的测试数据。")
    else: print("无法加载原始模拟数据。")
