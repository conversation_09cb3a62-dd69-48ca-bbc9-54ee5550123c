# -*- coding: utf-8 -*-
"""
盈利均值回归策略 - 目标每天1次交易且必须盈利
核心改进：
1. 解决持仓检查问题，支持多次交易
2. 严格的入场条件，确保信号质量
3. 优化的止损止盈，提高盈亏比
4. 智能的交易频率控制
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class ProfitableMeanReversionStrategy:
    """盈利均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'ProfitableMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 严格的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.02)  # 2%偏离
        
        # 严格的RSI参数
        self.rsi_key = all_params.get('rsi_key', 'RSI_14')
        self.rsi_oversold = all_params.get('rsi_oversold', 30)  # 真正的超卖
        
        # 优化的风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.005)  # 0.5%风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)  # 给予充分空间
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 4.0)  # 2:1盈亏比
        
        # 智能的交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 360)  # 6小时间隔
        self.max_daily_trades = all_params.get('max_daily_trades', 2)  # 每日最多2次
        
        # 新增质量控制参数
        self.volume_threshold = all_params.get('volume_threshold', 1.2)  # 成交量确认
        self.adx_max_threshold = all_params.get('adx_max_threshold', 25)  # 震荡市过滤
        self.min_price_level = all_params.get('min_price_level', 50000)  # 最低价格水平
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.total_signals_generated = 0
        
        print(f"策略 ProfitableMeanReversionStrategy 初始化...")
        print(f"ProfitableMeanReversionStrategy: 价格偏离={self.price_deviation_pct*100}%, "
              f"RSI≤{self.rsi_oversold}, 盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, "
              f"信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_high_quality_mean_reversion(self, data: Dict[str, Any]) -> bool:
        """检查高质量均值回归机会"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 条件1：价格显著低于短期均线
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            if deviation >= self.price_deviation_pct:
                return True
        
        return False
    
    def check_rsi_oversold(self, data: Dict[str, Any]) -> bool:
        """检查RSI真正超卖"""
        rsi = data.get(self.rsi_key, 50)
        
        if pd.isna(rsi):
            return False  # 没有RSI数据，不交易
        
        return rsi <= self.rsi_oversold
    
    def check_market_conditions(self, data: Dict[str, Any]) -> bool:
        """检查市场条件"""
        price = data.get('CLOSE', 0)
        adx = data.get('ADX_14', 0)
        volume = data.get('VOLUME', 0)
        avg_volume = data.get('VOLUME_SMA_20', volume)
        
        # 条件1：价格不能太低
        if price < self.min_price_level:
            return False
        
        # 条件2：震荡市环境（ADX不能太高）
        if not pd.isna(adx) and adx > 0:
            if adx > self.adx_max_threshold:
                return False
        
        # 条件3：成交量确认
        if avg_volume > 0:
            volume_ratio = volume / avg_volume
            if volume_ratio < self.volume_threshold:
                return False
        
        return True
    
    def check_trend_support(self, data: Dict[str, Any]) -> bool:
        """检查趋势支撑"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 长期趋势不能是强烈下跌
        trend_ratio = sma_short / sma_long
        return trend_ratio >= 0.95  # 短期均线不能比长期均线低太多
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法 - 关键：不检查持仓"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 移除持仓检查，允许多次交易
            # 注释掉原来的持仓检查逻辑
            # if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
            #     continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 高质量均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.sma_short_key, self.rsi_key]
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key, sma_short)
        rsi = data.get(self.rsi_key)
        atr = data.get('ATR_14', price * 0.02)  # 默认2%的ATR
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, sma_short, atr]):
            return signals
        
        if pd.isna(rsi):
            return signals
        
        # 高质量的买入条件（非常严格）
        buy_conditions = [
            self.check_high_quality_mean_reversion(data),  # 高质量均值回归机会
            self.check_rsi_oversold(data),  # RSI真正超卖
            self.check_market_conditions(data),  # 市场条件良好
            self.check_trend_support(data),  # 趋势支撑
        ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'ProfitableMeanReversionStrategy',
                'signal_type': 'profitable_mean_reversion',
                'reason': f'高质量均值回归: 偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}vs SMA={sma_short:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            self.total_signals_generated += 1
            
            print(f"[{current_time}] ProfitableMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}vs SMA={sma_short:.2f}, "
                  f"数量={position_size:.4f}, 信号#{self.total_signals_generated}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'ProfitableMeanReversionStrategy',
            'version': '1.0',
            'description': '盈利均值回归策略，目标每天1次交易且必须盈利',
            'improvements': [
                '解决持仓检查问题',
                '严格的入场条件',
                '优化的止损止盈',
                '智能的交易频率控制',
                '多重质量确认'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'rsi_oversold': self.rsi_oversold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'total_signals_generated': self.total_signals_generated,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
