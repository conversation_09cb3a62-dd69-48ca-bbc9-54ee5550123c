# -*- coding: utf-8 -*-
"""
P03客户目标导向策略
专门设计来达到客户目标：年化收益≥15%, 夏普比率≥2, 最大回撤≤15%
采用趋势跟踪 + 严格风控 + 动态仓位管理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class ClientTargetOrientedStrategy:
    """P03客户目标导向策略 - 专门为达到客户目标而设计"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 客户目标导向参数设计
        self.strategy_params = {
            # 趋势识别参数 (避免均值回归的缺陷)
            'trend_fast_ma': 12,           # 快速均线
            'trend_slow_ma': 26,           # 慢速均线
            'trend_signal_ma': 9,          # 信号均线
            'trend_strength_threshold': 0.02, # 趋势强度阈值
            
            # 严格风险控制 (确保回撤≤15%)
            'max_position_risk': 0.08,     # 单笔最大风险8%
            'daily_loss_limit': 0.03,      # 日亏损限制3%
            'max_drawdown_limit': 0.12,    # 最大回撤限制12%
            'stop_loss_pct': 0.02,         # 止损2%
            'take_profit_pct': 0.08,       # 止盈8% (4:1盈亏比)
            
            # 动态仓位管理 (提高收益率)
            'base_position': 0.15,         # 基础仓位15%
            'max_position': 0.4,           # 最大仓位40%
            'position_scaling': True,      # 启用仓位缩放
            'trend_multiplier': 1.5,       # 强趋势仓位倍数
            
            # 信号过滤 (提高胜率)
            'volume_confirmation': True,   # 成交量确认
            'momentum_confirmation': True, # 动量确认
            'volatility_filter': True,    # 波动率过滤
            'min_signal_strength': 0.6,   # 最小信号强度
            
            # 交易频率控制
            'min_holding_minutes': 60,     # 最小持仓60分钟
            'max_holding_hours': 8,        # 最大持仓8小时
            'max_daily_trades': 3,         # 每日最多3次交易
            'signal_cooldown': 120,        # 信号冷却120分钟
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_trend_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算趋势跟踪指标"""
        try:
            df = data.copy()
            
            # MACD指标
            df['EMA_Fast'] = df['CLOSE'].ewm(span=self.strategy_params['trend_fast_ma']).mean()
            df['EMA_Slow'] = df['CLOSE'].ewm(span=self.strategy_params['trend_slow_ma']).mean()
            df['MACD'] = df['EMA_Fast'] - df['EMA_Slow']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['trend_signal_ma']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # 趋势强度
            df['Trend_Strength'] = abs(df['MACD']) / df['CLOSE']
            
            # 价格动量
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)
            df['Momentum_60'] = df['CLOSE'].pct_change(60)
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(14).mean()
            
            # RSI (辅助确认)
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            return df
            
        except Exception as e:
            logger.warning(f"计算趋势指标失败: {e}")
            return data
    
    def generate_trend_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成趋势跟踪信号"""
        try:
            # 初始化信号
            df['Signal'] = 0
            df['Signal_Strength'] = 0
            
            # 趋势确认条件
            trend_up = (
                (df['MACD'] > df['MACD_Signal']) &  # MACD金叉
                (df['MACD_Histogram'] > 0) &        # 柱状图为正
                (df['EMA_Fast'] > df['EMA_Slow']) & # 快线在慢线上方
                (df['Momentum_15'] > 0.005) &       # 正动量
                (df['Trend_Strength'] > self.strategy_params['trend_strength_threshold'])  # 趋势强度足够
            )
            
            # 成交量确认
            if self.strategy_params['volume_confirmation']:
                volume_confirm = df['Volume_Ratio'] > 1.2
                trend_up = trend_up & volume_confirm
            
            # 动量确认
            if self.strategy_params['momentum_confirmation']:
                momentum_confirm = (
                    (df['Momentum_5'] > 0) &
                    (df['Momentum_60'] > 0.01)
                )
                trend_up = trend_up & momentum_confirm
            
            # 波动率过滤
            if self.strategy_params['volatility_filter']:
                volatility_ok = (df['Volatility'] > 0.01) & (df['Volatility'] < 0.08)
                trend_up = trend_up & volatility_ok
            
            # RSI过滤 (避免极端超买)
            rsi_ok = df['RSI'] < 75
            trend_up = trend_up & rsi_ok
            
            # 设置买入信号
            df.loc[trend_up, 'Signal'] = 1
            
            # 计算信号强度
            df['Signal_Strength'] = (
                abs(df['MACD_Histogram']) * 0.3 +
                df['Trend_Strength'] * 0.3 +
                df['Momentum_15'] * 0.2 +
                (df['Volume_Ratio'] - 1) * 0.2
            )
            
            # 卖出信号
            trend_down = (
                (df['MACD'] < df['MACD_Signal']) |  # MACD死叉
                (df['MACD_Histogram'] < 0) |        # 柱状图转负
                (df['Momentum_5'] < -0.005) |       # 负动量
                (df['RSI'] > 80)                    # 极度超买
            )
            
            df.loc[trend_down, 'Signal'] = -1
            
            return df
            
        except Exception as e:
            logger.error(f"生成趋势信号失败: {e}")
            return df
    
    def simulate_client_target_strategy(self, data: pd.DataFrame) -> dict:
        """模拟客户目标导向策略"""
        try:
            print("🔄 模拟P03客户目标导向策略...")
            
            # 计算技术指标
            data = self.calculate_trend_indicators(data)
            data = self.generate_trend_signals(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            # 风险控制变量
            daily_pnl = {}
            max_equity = initial_capital
            last_trade_time = None
            entry_time = None
            entry_price = 0
            stop_loss_price = 0
            take_profit_price = 0
            daily_trades = {}
            
            for i in range(60, len(data)):  # 从第60个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']
                
                # 更新权益曲线
                current_equity = cash + position * current_price
                equity_curve.append(current_equity)
                
                # 更新最大权益
                max_equity = max(max_equity, current_equity)
                
                # 检查最大回撤限制
                current_drawdown = (max_equity - current_equity) / max_equity
                if current_drawdown > self.strategy_params['max_drawdown_limit']:
                    # 强制平仓
                    if position > 0:
                        cash += position * current_price * 0.9995
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': '回撤限制强制平仓'
                        })
                        position = 0
                    continue
                
                # 检查数据有效性
                if pd.isna(current_price) or pd.isna(current_data.get('Signal')):
                    continue
                
                # 检查每日交易限制
                current_date = current_time.date()
                if current_date not in daily_trades:
                    daily_trades[current_date] = 0
                
                if daily_trades[current_date] >= self.strategy_params['max_daily_trades']:
                    continue
                
                # 检查信号冷却
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['signal_cooldown']:
                        continue
                
                # 无持仓时检查入场
                if position == 0:
                    signal = current_data['Signal']
                    signal_strength = current_data.get('Signal_Strength', 0)
                    
                    if signal == 1 and signal_strength >= self.strategy_params['min_signal_strength']:
                        # 计算动态仓位
                        base_pos = self.strategy_params['base_position']
                        
                        if self.strategy_params['position_scaling']:
                            # 根据信号强度调整仓位
                            position_multiplier = min(signal_strength * 2, self.strategy_params['trend_multiplier'])
                            position_size = base_pos * position_multiplier
                        else:
                            position_size = base_pos
                        
                        position_size = min(position_size, self.strategy_params['max_position'])
                        
                        # 风险控制：根据ATR调整仓位
                        atr = current_data.get('ATR', current_price * 0.02)
                        risk_adjusted_size = min(position_size, 
                                               self.strategy_params['max_position_risk'] / (atr / current_price))
                        
                        # 计算股数
                        position_value = cash * risk_adjusted_size
                        shares = position_value / current_price
                        
                        if shares > 0:
                            position = shares
                            cash -= shares * current_price * 1.0005  # 手续费
                            entry_time = current_time
                            entry_price = current_price
                            
                            # 设置止损止盈
                            stop_loss_price = current_price * (1 - self.strategy_params['stop_loss_pct'])
                            take_profit_price = current_price * (1 + self.strategy_params['take_profit_pct'])
                            
                            trades.append({
                                'time': current_time,
                                'action': 'buy',
                                'price': current_price,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'position_size': risk_adjusted_size,
                                'stop_loss': stop_loss_price,
                                'take_profit': take_profit_price
                            })
                            
                            last_trade_time = current_time
                            daily_trades[current_date] += 1
                
                # 有持仓时检查出场
                elif position > 0:
                    signal = current_data['Signal']
                    
                    # 计算持仓时间
                    holding_minutes = (current_time - entry_time).total_seconds() / 60
                    
                    # 出场条件
                    should_exit = False
                    exit_reason = ""
                    
                    # 止损
                    if current_price <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                    # 止盈
                    elif current_price >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                    # 信号转向
                    elif signal == -1:
                        should_exit = True
                        exit_reason = "信号转向"
                    # 最小持仓时间后的趋势减弱
                    elif (holding_minutes > self.strategy_params['min_holding_minutes'] and
                          current_data.get('Trend_Strength', 0) < self.strategy_params['trend_strength_threshold'] * 0.5):
                        should_exit = True
                        exit_reason = "趋势减弱"
                    # 最大持仓时间
                    elif holding_minutes > self.strategy_params['max_holding_hours'] * 60:
                        should_exit = True
                        exit_reason = "超时平仓"
                    
                    if should_exit:
                        cash += position * current_price * 0.9995  # 扣除手续费
                        
                        pnl = (current_price - entry_price) * position
                        pnl_pct = pnl / (entry_price * position) * 100
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': exit_reason,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'holding_minutes': holding_minutes
                        })
                        
                        position = 0
                        entry_time = None
                        last_trade_time = current_time
                        daily_trades[current_date] += 1
            
            # 期末平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                pnl = (final_price - entry_price) * position
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': pnl
                })
                position = 0
            
            final_equity = cash
            
            # 计算统计指标
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ P03客户目标导向策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"P03客户目标导向策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P03客户目标导向策略测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    strategy = ClientTargetOrientedStrategy()
    
    print("\n🎯 P03客户目标导向策略特点:")
    print("=" * 60)
    print("  1. 趋势跟踪 (避免均值回归缺陷)")
    print("  2. 严格风控 (确保回撤≤15%)")
    print("  3. 动态仓位 (提高收益率)")
    print("  4. 多重确认 (提高胜率)")
    print("  5. 4:1盈亏比 (优化收益)")
    print("=" * 60)
    
    # 测试2024年4月
    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_client_target_strategy(data)
        
        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1
            
            print(f"\n📊 P03客户目标导向策略测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"交易次数: {result['total_trades']}")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            
            # 客户目标达成检查
            print(f"\n🎯 客户目标达成检查:")
            print("-" * 40)
            annual_ok = annual_return >= 0.15
            sharpe_ok = result['sharpe_ratio'] >= 2.0
            drawdown_ok = result['max_drawdown'] <= 0.15
            
            print(f"年化收益率: {annual_return*100:+.2f}% {'✅' if annual_ok else '❌'} (目标: ≥15%)")
            print(f"夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'} (目标: ≥2.0)")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}% {'✅' if drawdown_ok else '❌'} (目标: ≤15%)")
            
            targets_met = sum([annual_ok, sharpe_ok, drawdown_ok])
            
            print(f"\n🏆 P03客户目标导向策略评价:")
            if targets_met == 3:
                print("🎉 完全达标! 策略成功达到所有客户目标!")
            elif targets_met == 2:
                print("✅ 基本达标! 策略表现优秀，接近完美!")
            elif targets_met == 1:
                print("⚠️ 部分达标! 策略有改进，但仍需优化!")
            else:
                print("❌ 未达标! 需要进一步调整策略参数!")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
