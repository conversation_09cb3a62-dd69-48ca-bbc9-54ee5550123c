# -*- coding: utf-8 -*-
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
import os # 引入os以便使用环境变量

@dataclass
class Config:
    """系统配置类"""
    # 基础配置
    initial_capital: float = 100000.0 # 之前是50000，统一为100000
    us_symbols: List[str] = field(default_factory=lambda: [])  # 暂时不使用美股
    cn_symbols: List[str] = field(default_factory=lambda: [])  # 暂时不使用A股
    crypto_pairs: List[str] = field(default_factory=lambda: ["BTCUSDT"])  # 只使用BTCUSDT测试
    max_daily_loss: float = 0.02
    position_limit: float = 0.5
    allow_short_selling: bool = False
    transaction_cost_pct: float = 0.001

    # 本地数据路径配置 (修改为项目内路径)
    local_minute_data_path: Optional[str] = "数据/BTCUSDT"  # 使用项目内的数据目录

    # API密钥 (建议从环境变量读取)
    tushare_token: str = os.environ.get("TUSHARE_TOKEN", "YOUR_TUSHARE_TOKEN_PLACEHOLDER")
    binance_api_key: str = os.environ.get("BINANCE_API_KEY", "YOUR_BINANCE_KEY_PLACEHOLDER")
    binance_api_secret: str = os.environ.get("BINANCE_API_SECRET", "YOUR_BINANCE_SECRET_PLACEHOLDER")

    # 低延迟架构配置
    fpga_enabled: bool = False
    fpga_bitstream: Optional[str] = None
    redis_host: str = "localhost"
    redis_port: int = 6379
    order_routing: str = "smart"

    # 手续费配置
    fees: Dict[str, Dict[str, float]] = field(default_factory=dict) # 让post_init填充

    # 策略相关配置
    strategies: List[Dict[str, Any]] = field(default_factory=list) # 让post_init填充

    # 实时交易模式配置
    live_trading: bool = False  # 是否启用实盘交易模式

    # --- 修改后的因子配置 ---
    # 这个结构定义了要计算哪些因子，使用哪个函数，以及具体的参数
    # 键名 (如 'SMA_20') 将作为 DataFrame 中的列名 (或前缀)
    factor_config: Dict[str, Any] = field(default_factory=lambda: {
        # 简单移动平均线
        'SMA_20': {'function': 'SMA', 'params': {'window': 20}, 'source_col': 'Close'},
        'SMA_60': {'function': 'SMA', 'params': {'window': 60}, 'source_col': 'Close'},
        # 指数移动平均线 (如果需要，可以添加)
        'EMA_10': {'function': 'EMA', 'params': {'window': 10}, 'source_col': 'Close'},
        'EMA_20': {'function': 'EMA', 'params': {'window': 20}, 'source_col': 'Close'},
        # 相对强弱指数
        'RSI_14': {'function': 'RSI', 'params': {'window': 14}, 'source_col': 'Close'},
        # 平均真实波幅 (ATR)
        'ATR_14': {'function': 'ATR', 'params': {'window': 14}}, # ATR自动使用HLC
        # 平均方向性指数 (ADX)
        'ADX_14': {'function': 'ADX', 'params': {'window': 14}}, # ADX自动使用HLC
        # 移动平均收敛散度 (MACD) - 使用默认参数
        'MACD_Default': {'function': 'MACD', 'params': {
            # 'fast_period': 12, 'slow_period': 26, 'signal_period': 9 # 这些是因子库函数的默认值
        }},
        # 布林带 (BBands) - 使用默认参数
        'BBands_Default': {'function': 'BBands', 'params': {
            # 'window': 20, 'num_std_dev': 2.0 # 这些是因子库函数的默认值
        }},
        # 波动率因子
        'Volatility_20': {'function': 'Volatility', 'params': {'window': 20}, 'source_col': 'Close'},
        # 动量因子
        'Momentum_10': {'function': 'Momentum', 'params': {'window': 10}, 'source_col': 'Close'},
        # 新增的技术指标
        'CCI_20': {'function': 'CCI', 'params': {'window': 20}}, # CCI使用HLC
        'Donchian_20': {'function': 'Donchian', 'params': {'window': 20}}, # Donchian使用HL
        'Keltner_Default': {'function': 'Keltner', 'params': {}}, # Keltner使用HLC，采用函数默认参数
        # 如果想用自定义参数的 Keltner 通道:
        # 'Keltner_Custom': {'function': 'Keltner', 'params': {'ema_window': 15, 'atr_window': 7, 'atr_multiplier': 1.5}},
    })

    def __post_init__(self):
        """初始化默认值和进行简单验证"""
        # 默认标的列表 (如果外部未提供)
        if self.us_symbols is None: self.us_symbols = ["AAPL", "MSFT", "GOOG"]
        if self.cn_symbols is None: self.cn_symbols = ["600519.SH", "600036.SH", "000001.SZ"]
        if self.crypto_pairs is None: self.crypto_pairs = ["BTCUSDT", "ETHUSDT"]

        # 默认手续费 (如果外部未提供)
        if not self.fees: # 检查是否为空字典
            self.fees = {
                "cn": {"commission": 0.0003, "min_commission": 5.0, "stamp_duty": 0.001, "transfer": 0.00001},
                "us": {"commission": 0.005, "min_commission": 1.0, "sec_fee_rate": 0.000008, "finra_fee_rate": 0.000145},
                "crypto": {"maker": 0.001, "taker": 0.001}
            }

        # 默认策略配置 (如果外部未提供)
        # 注意：这里的因子名称需要与 factor_config 中定义的键名或生成的列名一致
        if not self.strategies: # 检查是否为空列表
            self.strategies = [
                {'name': 'AlphaXInspiredStrategy', # 使用可用的策略
                 'params': {
                     'risk_per_trade_pct': 0.01,
                     'atr_sl_multiple': 2.0,
                     'atr_tp_multiple': 3.0,
                     'rsi_oversold': 60.0,  # 调整RSI超卖阈值，使其更容易触发
                     'adx_threshold': 20.0,  # 降低ADX阈值，使其更容易满足趋势条件
                     'min_signal_interval_minutes': 60  # 减少信号间隔时间
                 }},
                {'name': 'MeanReversion',
                 'params': {
                     'bbands_factor_prefix': 'BBands_Default', # 会生成 BBands_Default_BB_Lower 等
                     'rsi_factor_name': 'RSI_14',
                     'atr_factor_name': 'ATR_14', # 确保策略会用到
                     'macd_line_factor_name': 'MACD_Default_MACD_Line', # 确保策略会用到
                     'macd_signal_factor_name': 'MACD_Default_Signal_Line', # 确保策略会用到
                     'rsi_overbought': 68,
                     'rsi_oversold': 32,
                     'max_position_per_symbol': 100.0 # Added parameter
                 }}
            ]

        # 基础验证
        if self.initial_capital <= 0: raise ValueError("初始资金必须大于0")
        if not (0 < self.max_daily_loss < 1): raise ValueError("最大日亏损比例必须在 0 和 1 之间")
        if not (0 < self.position_limit < 1): raise ValueError("头寸限制比例必须在 0 和 1 之间")

        # 提示替换占位符API密钥
        if "YOUR_TUSHARE_TOKEN_PLACEHOLDER" in self.tushare_token:
            print("警告: Tushare Token 未配置，请在 系统配置.py 或环境变量中设置。")
        if "YOUR_BINANCE_KEY_PLACEHOLDER" in self.binance_api_key:
            print("警告: Binance API Key 未配置。")


