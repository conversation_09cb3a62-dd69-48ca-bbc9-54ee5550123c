# -*- coding: utf-8 -*-
"""
P06双向交易策略
模拟大机构的双向交易能力：上涨做多，下跌做空
这是关键突破：让我们能在任何市场环境下都有盈利机会
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class BidirectionalTradingStrategy:
    """P06双向交易策略 - 模拟大机构的做多做空能力"""

    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"

        # 双向交易策略参数
        self.strategy_params = {
            # 技术指标参数
            'ma_short': 12,                # 短期均线
            'ma_long': 26,                 # 长期均线
            'rsi_period': 14,              # RSI周期
            'macd_signal': 9,              # MACD信号线

            # 做多信号阈值
            'long_rsi_min': 35,            # 做多RSI最小值
            'long_rsi_max': 65,            # 做多RSI最大值
            'long_momentum_min': 0.002,    # 做多最小动量

            # 做空信号阈值
            'short_rsi_min': 35,           # 做空RSI最小值
            'short_rsi_max': 65,           # 做空RSI最大值
            'short_momentum_max': -0.002,  # 做空最大动量(负值)

            # 仓位管理
            'position_size': 0.25,         # 单向仓位25%
            'max_total_exposure': 0.5,     # 最大总敞口50%
            'allow_hedging': True,         # 允许对冲(同时持有多空)

            # 风险控制
            'stop_loss_long': 0.025,       # 做多止损2.5%
            'take_profit_long': 0.05,      # 做多止盈5%
            'stop_loss_short': 0.025,      # 做空止损2.5%
            'take_profit_short': 0.05,     # 做空止盈5%

            # 交易控制
            'min_signal_gap': 30,          # 最小信号间隔30分钟
            'max_holding_hours': 12,       # 最大持仓12小时
            'force_close_opposite': True,  # 反向信号时强制平仓
        }

    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")

            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)

            all_data = []
            current_date = start_dt

            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month

                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)

                if os.path.exists(filepath):
                    print(f"  加载: {filename}")

                    df = pd.read_csv(filepath)

                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])

                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)

                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH',
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)

                    df = df[(df.index >= start_date) & (df.index <= end_date)]

                    if not df.empty:
                        all_data.append(df)

                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)

            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")

            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]

            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data

        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise