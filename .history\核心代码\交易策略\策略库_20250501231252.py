# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
# ... other imports ...
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError: # ... Dummy classes ...
    pass

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

class TradingStrategy(BASE_STRATEGY):  # 确保继承自 backtesting.Strategy 或其替身
    """交易策略基类"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功


    # --- !! 关键：修正 __init__ 签名 !! ---
    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 调用 backtesting.Strategy 的父类 __init__
        #    必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        #    父类 __init__ 会处理将 params 设置到实例属性
        if backtesting_available and isinstance(self, BacktestStrategy):
             super().__init__(broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) 调用子类的 init() - backtesting 库会自动调用
        #    但如果我们在基类 __init__ 中做了很多事，确保不要阻止它。
        #    实际上，backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init()。


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass
    # ... 其他自定义方法 ...

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略"""
    # --- 定义策略特定的默认参数 (这些会被 __init__ 读取) ---
    window = 20
    threshold = 2.0
    rsi_window = 14
    rsi_overbought = 70
    rsi_oversold = 30
    atr_window = 14 # 如果需要ATR
    trade_frequency = '2W'  # 交易频率
    position_size = 1.0  # 仓位大小
    stop_loss = 0.05  # 止损比例
    take_profit = 0.1  # 止盈比例
    rsi_factor_name = 'RSI'  # RSI 因子名称
    bbands_factor_prefix = 'BB'  # 布林带因子前缀
    macd_line_factor_name = 'MACD_Line'  # MACD 线因子名称
    macd_signal_factor_name = 'Signal_Line'  # MACD 信号线因子名称
    atr_factor_name = None  # ATR 因子名称
    # (从基类继承 stop_loss_pct, take_profit_pct)

    # --- !! 不需要重新定义 __init__ !! ---
    # 继承自 TradingStrategy 的 __init__ 已经处理了参数合并和调用父类
    # 如果你需要特殊的初始化逻辑，可以覆盖 __init__，但 *必须* 调用 super().__init__(broker, data, params)

    # --- 策略核心逻辑在 init 和 next 中 ---
    def init(self):
        # 这个 init 由 backtesting 库自动调用
        logger.debug(f"Initializing {self.__class__.__name__} with final params: {self.parameters}")

        # --- 检查必需列 (使用 TitleCase/因子名) ---
        # 因子名应与 calculate_factors 生成的名称匹配
        required_factors = ['Close', 'BB_Lower', 'BB_Upper', 'BB_Middle', 'RSI', 'MACD_Line', 'Signal_Line']
        # 如果策略参数指定了 ATR 因子名称，则也检查它
        self.atr_factor_name = self.parameters.get('atr_factor_name', None) # 从参数获取，可能为 None
        if self.atr_factor_name:
            required_factors.append(self.atr_factor_name)

        # 直接检查 self.data 的列名 (backtesting 库应保留原始大小写)
        available_cols = self.data.df.columns.tolist()
        missing_factors = [f for f in required_factors if f not in available_cols]

        if missing_factors:
             logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing_factors}")
             logger.error(f"可用列: {available_cols}")
             self._ready = False
             return # 阻止策略运行
        else:
             logger.info(f"策略 '{self.__class__.__name__}' 初始化成功，所有必需因子/列存在。")
             self._ready = True

    def next(self):
        if not self._ready: return

        try:
            # --- 使用 TitleCase/因子名访问数据 ---
            price = self.data.Close[-1]
            bb_low_val = self.data.BB_Lower[-1]
            bb_up_val = self.data.BB_Upper[-1]
            bb_mid_val = self.data.BB_Middle[-1]
            rsi_val = self.data.RSI[-1]
            macd_line_val = self.data.MACD_Line[-1]
            macd_signal_val = self.data.Signal_Line[-1]
            # 检查所有值是否有效 (非 NaN)
            if not all(pd.notna(v) for v in [price, bb_low_val, bb_up_val, bb_mid_val, rsi_val, macd_line_val, macd_signal_val]):
                return # 如果任何关键数据点是 NaN，则跳过此周期
        except (IndexError, AttributeError, KeyError) as e: # 保持错误捕获
             # Catch potential errors if columns still missing despite init check
             # logger.warning(f"Error accessing data in next(): {e}") # Avoid spam
             return

        # --- Get parameters (remains same) ---
        rsi_ob = self.parameters.get('rsi_overbought', 70)
        rsi_os = self.parameters.get('rsi_oversold', 30)
        sl_pct = self.parameters.get('stop_loss_pct', 0.03)
        tp_pct = self.parameters.get('take_profit_pct', 0.08)

        sl_buy = price * (1 - sl_pct); tp_buy = price * (1 + tp_pct)
        sl_sell = price * (1 + sl_pct); tp_sell = price * (1 - tp_pct)

        # --- Enhanced Trading Logic ---
        # Calculate MACD crossover (使用 TitleCase/因子名)
        macd_crossover_up = macd_line_val > macd_signal_val and self.data.MACD_Line[-2] <= self.data.Signal_Line[-2]
        macd_crossover_down = macd_line_val < macd_signal_val and self.data.MACD_Line[-2] >= self.data.Signal_Line[-2]

        # Enhanced entry conditions with volume, volatility, momentum and ATR filter
        buy_condition = (
            (price < bb_low_val and rsi_val < rsi_os and self.data.Volume[-1] > 2000000 and self.data.Volatility[-1] > 0.02 and self.data.Momentum[-1] > 0 and self.data.ATR[-1] > 20) or  # Classic mean reversion with volume, volatility, momentum and ATR
            (price < bb_mid_val and rsi_val < 50 and macd_crossover_up and self.data.Volume[-1] > 1500000 and self.data.Volatility[-1] > 0.015 and self.data.Momentum[-1] > 0 and self.data.ATR[-1] > 15)  # MACD confirmation with volume, volatility, momentum and ATR
        )

        sell_condition = (
            (price > bb_up_val and rsi_val > rsi_ob and self.data.Volume[-1] > 2000000 and self.data.Volatility[-1] > 0.02 and self.data.Momentum[-1] < 0 and self.data.ATR[-1] > 20) or  # Classic mean reversion with volume, volatility, momentum and ATR
            (price > bb_mid_val and rsi_val > 50 and macd_crossover_down and self.data.Volume[-1] > 1500000 and self.data.Volatility[-1] > 0.015 and self.data.Momentum[-1] < 0 and self.data.ATR[-1] > 15)  # MACD confirmation with volume, volatility, momentum and ATR
        )

        # Execute trades
        if buy_condition and not self.position:
            self.buy(sl=sl_buy, tp=tp_buy)
        elif sell_condition and not self.position:
            self.sell(sl=sl_sell, tp=tp_sell)

        # Enhanced exit conditions
        if self.position:
            if self.position.is_long and (price >= bb_mid_val or macd_crossover_down):
                self.position.close()
            elif self.position.is_short and (price <= bb_mid_val or macd_crossover_up):
                self.position.close()

    # generate_signals_dataframe (for simple backtest) still uses original case
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        # Use original case names here as this function doesn't use backtesting's self.data
        bb_low_col = f"{self.bbands_factor_prefix}_BB_Lower"
        bb_up_col = f"{self.bbands_factor_prefix}_BB_Upper"
        rsi_col = self.rsi_factor_name
        required = ['Close', bb_low_col, bb_up_col, rsi_col] # Original case 'Close'
        if not validate_input(target_data, required):
            logger.error(f"MR信号生成缺少列: {required}")
            return pd.DataFrame({'Signal': 0}, index=getattr(target_data,'index',pd.Index([])))
        # ... (rest of the logic using original case column names) ...
        signals_df = pd.DataFrame(index=target_data.index)
        price = target_data['Close']; bb_low = target_data[bb_low_col]; bb_up = target_data[bb_up_col]; rsi = target_data[rsi_col]
        rsi_ob = self.parameters.get('rsi_overbought', 70); rsi_os = self.parameters.get('rsi_oversold', 30)
        buy_condition = (price < bb_low) & (rsi < rsi_os); sell_condition = (price > bb_up) & (rsi > rsi_ob)
        signals_df['RawSignal'] = np.where(buy_condition, 1, np.where(sell_condition, -1, 0))
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0); signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]


# --- Apply similar changes to other strategies (TrendFollowingStrategy, TrendWithVolatilityStrategy) ---

class TrendFollowingStrategy(TradingStrategy):
    short_factor_name = 'SMA_20'
    long_factor_name = 'SMA_60'
    stop_loss_pct = 0.05
    take_profit_pct = 0.15
    _ready: bool = False

    def init(self):
        # 检查必需列 (使用 TitleCase/因子名)
        # 假设因子名是 'SMA_20', 'SMA_60' 等，与 calculate_factors 对应
        # 注意：如果 calculate_factors 生成的是 'SMA20'，这里需要相应调整
        required_factors = ['Close', self.short_factor_name, self.long_factor_name]
        available_cols = self.data.df.columns.tolist()
        missing = [f for f in required_factors if f not in available_cols]
        if missing:
            logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing}")
            logger.error(f"可用列: {available_cols}")
            self._ready = False
            return
        self._ready = True
        logger.info(f"策略 '{self.__class__.__name__}' 初始化成功。")

    def next(self):
        if not self._ready: return
        try:
            # 使用 TitleCase/因子名访问数据
            # 假设因子名是 'SMA_20', 'SMA_60'
            short_ma_series = self.data[self.short_factor_name]
            long_ma_series = self.data[self.long_factor_name]
            price = self.data.Close[-1] # 使用 TitleCase 'Close'
        except (AttributeError, KeyError, IndexError):
            # logger.warning(f"Error accessing data in {self.__class__.__name__}.next()")
            return # Data not ready or column missing

        sl = price * (1 - self.stop_loss_pct)
        tp = price * (1 + self.take_profit_pct)

        # Use crossover with the accessed series
        if crossover(short_ma_series, long_ma_series):
            if not self.position: self.buy(sl=sl, tp=tp)
        elif crossover(long_ma_series, short_ma_series):
            if self.position: self.position.close()

    # generate_signals_dataframe remains the same (using original case)
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        # ... (uses original case names like self.short_factor_name) ...
        pass # Keep previous implementation

class RevisedTrendStrategy(TrendFollowingStrategy):
    """改进版的趋势跟踪策略，增加了动态止盈止损和仓位管理"""
    # --- 新增参数 ---
    dynamic_sl_tp = True  # 是否启用动态止盈止损
    position_scaling = True  # 是否启用仓位管理
    max_position_size = 0.1  # 最大仓位比例
    volatility_window = 20  # 用于计算动态止损的波动率窗口
    
    def init(self):
        super().init() # 调用父类的 init 检查 SMA 因子
        if not self._ready: return # 如果父类 init 失败，则不继续

        if self.dynamic_sl_tp:
            # 假设 ATR 因子名为 'ATR' (由 calculate_factors 生成)
            self.atr_factor_name = self.parameters.get('atr_factor_name', 'ATR') # 从参数获取或使用默认 'ATR'
            required_factors = [self.atr_factor_name]
            available_cols = self.data.df.columns.tolist()
            missing = [f for f in required_factors if f not in available_cols]
            if missing:
                logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，缺少 ATR 列: {missing}")
                self._ready = False # 标记为未就绪
                return
        # 如果父类和 ATR 检查都通过，则 _ready 保持 True

    def next(self):
        if not self._ready: return
        
        try:
            # 使用 TitleCase/因子名访问
            price = self.data.Close[-1]
            short_ma = self.data[self.short_factor_name][-1]
            long_ma = self.data[self.long_factor_name][-1]

            if self.dynamic_sl_tp:
                # 假设 ATR 因子名为 'ATR'
                atr = self.data[self.atr_factor_name][-1]
                if not pd.notna(atr): return # 如果 ATR 无效则跳过
                sl = price - 2 * atr
                tp = price + 3 * atr
            else:
                sl = price * (1 - self.stop_loss_pct)
                tp = price * (1 + self.take_profit_pct)
                
            if self.position_scaling:
                position_size = min(self.max_position_size, 
                                 0.5 * (price - sl) / atr if self.dynamic_sl_tp else self.max_position_size)
            else:
                position_size = 1.0
                
        except (AttributeError, KeyError, IndexError):
            return
            
        if crossover(short_ma, long_ma):
            if not self.position:
                self.buy(sl=sl, tp=tp, size=position_size)
        elif crossover(long_ma, short_ma):
            if self.position:
                self.position.close()

# 定义所有可用策略
STRATEGIES = {
    'MeanReversion': MeanReversionStrategy,
    'TrendFollowing': TrendFollowingStrategy,
    'RevisedTrend': RevisedTrendStrategy,
}
