# -*- coding: utf-8 -*-
import logging
from typing import Dict, List, Tuple, Optional, Any, Type
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from collections import defaultdict

# --- 导入与配置 (保持不变) ---
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    BASE_STRATEGY = BacktestStrategy
    backtesting_available = True
except ImportError:
    logging.warning("backtesting库未安装...")
    class DummyStrategy: # ... (保持不变) ...
        def __init__(self, broker, data, params): pass
        def init(self): pass; def next(self): pass
        def I(self, func, *args, **kwargs): return pd.Series(dtype=np.float64)
        @property
        def data(self): return pd.DataFrame({'Open':[], 'High':[], 'Low':[], 'Close':[], 'Volume':[]})
        @property
        def position(self): class MP: is_long=False;is_short=False;pl_pct=0.0;def close(self):pass; return MP();
        def buy(self,**kwargs):pass; def sell(self,**kwargs):pass
    BASE_STRATEGY = DummyStrategy
    backtesting_available = False
    def crossover(s1, s2): return False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 辅助函数和数据类 (保持不变) ---
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool: # ... (保持不变) ...
    # ...
    return True
@dataclass
class BacktestResult: # ... (保持不变) ...
    sharpe: float = np.nan; returns: float = np.nan; drawdown: float = np.nan; trades: int = 0; win_rate: float = np.nan; net_return: float = np.nan; annual_return: float = np.nan; volatility: float = np.nan; calmar_ratio: float = np.nan


# --- 交易策略基类 (修改：现在假设因子已在数据中) ---
class TradingStrategy(BASE_STRATEGY):
    """交易策略基类 - 假设因子已预先计算并包含在数据中"""
    # 移除因子计算相关的默认参数，让子类或配置决定需要哪些因子
    # window = 20 # 示例参数，子类根据需要定义
    stop_loss_pct = 0.05
    take_profit_pct = 0.10

    parameters: Dict[str, Any] = field(default_factory=dict)
    transaction_cost_pct: float = 0.001
    risk_manager = None
    _data = None

    def __init__(self, broker=None, data=None, params: Optional[Dict] = None):
        """初始化策略"""
        self.parameters = {}
        cls = self.__class__
        
        # 查找类中定义的潜在参数
        potential_params = [
            p for p in dir(cls) 
            if not p.startswith('_') 
            and not callable(getattr(cls, p)) 
            and isinstance(getattr(cls, p), (int, float, str, bool))
        ]
        
        # 将类属性添加到参数字典
        for param_name in potential_params:
            self.parameters[param_name] = getattr(cls, param_name)
        
        # 合并传入的参数
        if params:
            self.parameters.update(params)
        
        # 设置实例属性
        for key, value in self.parameters.items():
            if hasattr(self, key):
                setattr(self, key, value)

        # 调用父类初始化
        if backtesting_available:
            super().__init__(broker, data, self.parameters)

        # 设置交易成本
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))

        # 设置数据
        if data is not None and not isinstance(data, list):
            self._data = data


    # --- 修改: 策略现在依赖数据中已有的因子列 ---
    def init(self):
        """
        初始化。在 backtesting 框架中，访问因子通过 self.data.<因子列名>。
        注意：不能在这里使用 self.I() 来 *计算* 因子，因为它们应该已经存在于数据中。
        如果需要因子的平滑版本或其他衍生，可以使用 self.I()。
        """
        logger.debug(f"{self.__class__.__name__} 策略初始化 (init)。期望数据中包含所需因子列。")
        # 示例：获取预计算的 SMA 因子列 (假设列名为 SMA_20)
        # if hasattr(self.data, 'SMA_20'):
        #    self.sma20 = self.data.SMA_20 # 直接访问
        # else:
        #    logger.warning("数据中未找到 'SMA_20' 因子列")
        #    self.sma20 = pd.Series(np.nan, index=self.data.index) # 创建一个空的 Series

        # 如果需要 SMA 的 SMA，可以使用 I
        # self.sma_of_sma = self.I(lambda x: pd.Series(x).rolling(5).mean(), self.sma20)
        pass # 子类实现具体的因子访问或衍生计算

    def next(self):
        """
        交易逻辑。访问因子值通过 self.data.<因子列名>[-1]。
        """
        # 示例：
        # price = self.data.Close[-1]
        # sma20_value = self.data.SMA_20[-1] if hasattr(self.data, 'SMA_20') and len(self.data.SMA_20) > 0 else np.nan
        # rsi_value = self.data.RSI_14[-1] if hasattr(self.data, 'RSI_14') and len(self.data.RSI_14) > 0 else 50 # 默认50
        # if price > sma20_value and rsi_value < 70:
        #     if not self.position: self.buy()
        # elif price < sma20_value:
        #     if self.position: self.position.close()
        pass # 子类实现

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        生成信号 DataFrame - 现在假设输入的 data 已包含因子列。
        子类应覆盖此方法。
        """
        target_data = data if data is not None else self._data
        if target_data is None or target_data.empty:
            logger.error("无法生成信号DataFrame：无有效数据")
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)
        # 检查所需因子是否存在
        # required_factors = ['SMA_20', 'RSI_14'] # 示例
        # if not all(factor_col in target_data.columns for factor_col in required_factors):
        #     logger.error(f"数据缺少必要的因子列: {required_factors}")
        #     return pd.DataFrame({'Signal': 0}, index=target_data.index)

        logger.warning(f"{self.__class__.__name__} 未实现 generate_signals_dataframe (使用因子版)，返回全0信号")
        return pd.DataFrame({'Signal': 0}, index=target_data.index)

    # --- 其他方法保持不变 ---
    def set_data(self, data: pd.DataFrame): # ... (保持不变) ...
        if validate_input(data): self._data=data
        else: raise ValueError("设置数据无效")
    def set_transaction_cost(self, cost_pct: float): # ... (保持不变) ...
        if 0<=cost_pct<1: self.transaction_cost_pct=cost_pct; self.parameters['transaction_cost_pct']=cost_pct
        else: logger.error(f"无效交易成本: {cost_pct}")
    def run_simple_backtest(self, data: Optional[pd.DataFrame] = None, initial_capital: float = 100000.0) -> BacktestResult: # ... (保持不变) ...
        pass
    def backtest_with_shocks(self, crisis_data: Dict[str, Dict[str, float]], broker_simulator: Any, prices: pd.DataFrame) -> Dict: # ... (保持不变) ...
        pass
    def apply_price_shock(self, change_pct: float): # ... (保持不变) ...
        pass
    def grid_search(self, param_ranges: Dict[str, List], data: pd.DataFrame) -> Dict[str, Any]: # ... (保持不变) ...
        pass
    def parameter_sensitivity_matrix(self, param_ranges: Dict[str, List], data: pd.DataFrame) -> pd.DataFrame: # ... (保持不变) ...
        pass
    def generate_synthetic_data(self, vol_state: str = 'low_vol', trend_state: str = 'strong_trend', n_points: int = 1000) -> pd.DataFrame: # ... (保持不变) ...
        pass
    def adjust_strategy_parameters(self) -> None: # ... (保持不变) ...
        pass
    def execute_live_trade(self, broker: Any, symbol: str, quantity: int): # ... (保持不变) ...
        pass
    def run_stress_test(self, scenario: str, data: pd.DataFrame) -> BacktestResult: # ... (保持不变) ...
        pass


# --- 具体策略实现 (修改为使用因子列) ---

class EnhancedTrendFollowingStrategy(TradingStrategy):
    """增强版趋势跟踪策略 - 结合多个因子"""
    # 因子配置
    short_term_ma = 'SMA_20'      # 短期移动平均
    long_term_ma = 'SMA_60'       # 长期移动平均
    volatility_factor = 'Volatility_20'  # 波动率因子
    rsi_factor = 'RSI_14'         # 相对强弱指数
    
    # 策略参数
    stop_loss_pct = 0.05
    take_profit_pct = 0.15
    vol_threshold = 0.25          # 波动率阈值
    rsi_overbought = 70           # RSI超买水平
    rsi_oversold = 30             # RSI超卖水平

    def init(self):
        # 检查因子是否存在，如果不存在则策略无法运行或需要备用逻辑
        if not hasattr(self.data, self.short_factor_name):
             logger.error(f"策略 {self.__class__.__name__} 需要因子 '{self.short_factor_name}' 但未在数据中找到！")
             # 可以选择引发错误或禁用策略
        if not hasattr(self.data, self.long_factor_name):
             logger.error(f"策略 {self.__class__.__name__} 需要因子 '{self.long_factor_name}' 但未在数据中找到！")
        # 直接访问数据中的因子列 (backtesting 会自动处理 Series 对齐)
        self.short_ma = self.data[self.short_factor_name]
        self.long_ma = self.data[self.long_factor_name]

    def next(self):
        price = self.data.Close[-1]
        sl = price * (1 - self.stop_loss_pct)
        tp = price * (1 + self.take_profit_pct)

        # 使用 short_ma 和 long_ma 进行交叉判断
        # 注意：直接比较 Series 的最后值可能不是 backtesting 推荐的方式
        # 应该使用 crossover(self.short_ma, self.long_ma)
        if crossover(self.short_ma, self.long_ma):
            if not self.position: self.buy(sl=sl, tp=tp)
        elif crossover(self.long_ma, self.short_ma):
            if self.position: self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if not validate_input(target_data, ['Close', self.short_factor_name, self.long_factor_name]):
             logger.error(f"趋势策略信号生成缺少必要列: Close, {self.short_factor_name}, {self.long_factor_name}")
             index = pd.Index([]) if target_data is None else target_data.index
             return pd.DataFrame({'Signal': 0}, index=index)

        signals_df = pd.DataFrame(index=target_data.index)
        short_ma = target_data[self.short_factor_name]
        long_ma = target_data[self.long_factor_name]
        signals_df['RawSignal'] = np.where(short_ma > long_ma, 1, np.where(short_ma < long_ma, -1, 0))
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]


class MeanReversionStrategy(TradingStrategy):
    """均值回归策略 - 使用预计算的因子 (BBands, RSI)"""
    # 参数定义因子名称
    bbands_factor_prefix = 'BBands' # 来自因子库 BBands(window=20, num_std_dev=2.0) 的默认输出
    rsi_factor_name = 'RSI_14'     # 来自因子库 RSI(window=14)
    rsi_overbought = 70
    rsi_oversold = 30
    stop_loss_pct = 0.03
    take_profit_pct = 0.08

    def init(self):
        # 检查所需因子列
        self.bb_lower_col = f"{self.bbands_factor_prefix}_BB_Lower"
        self.bb_upper_col = f"{self.bbands_factor_prefix}_BB_Upper"
        required_factors = [self.bb_lower_col, self.bb_upper_col, self.rsi_factor_name]
        missing = [f for f in required_factors if not hasattr(self.data, f)]
        if missing: logger.error(f"均值回归策略缺少因子列: {missing}"); return

        self.bb_lower = self.data[self.bb_lower_col]
        self.bb_upper = self.data[self.bb_upper_col]
        self.rsi = self.data[self.rsi_factor_name]

    def next(self):
        price = self.data.Close[-1]
        # 检查是否有足够的历史数据来获取因子值
        if len(self.bb_lower) < 1 or len(self.bb_upper) < 1 or len(self.rsi) < 1: return

        bb_low_val = self.bb_lower[-1]
        bb_up_val = self.bb_upper[-1]
        rsi_val = self.rsi[-1]

        sl_buy = price * (1 - self.stop_loss_pct); tp_buy = price * (1 + self.take_profit_pct)
        sl_sell = price * (1 + self.stop_loss_pct); tp_sell = price * (1 - self.take_profit_pct)

        # 使用布林带和RSI信号
        if price > bb_up_val and rsi_val > self.rsi_overbought:
            if not self.position: self.sell(sl=sl_sell, tp=tp_sell) # 做空
        elif price < bb_low_val and rsi_val < self.rsi_oversold:
            if not self.position: self.buy(sl=sl_buy, tp=tp_buy) # 做多
        # 平仓逻辑可以基于价格回到中轨或固定止盈止损
        elif self.position:
             # 可以在这里添加额外的平仓逻辑，例如价格回到中轨
             bb_mid_col = f"{self.bbands_factor_prefix}_BB_Middle"
             if hasattr(self.data, bb_mid_col) and len(self.data[bb_mid_col]) > 0:
                  bb_mid_val = self.data[bb_mid_col][-1]
                  if self.position.is_long and price >= bb_mid_val: self.position.close()
                  elif self.position.is_short and price <= bb_mid_val: self.position.close()


    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        # 检查所需列
        bb_low_col = f"{self.bbands_factor_prefix}_BB_Lower"
        bb_up_col = f"{self.bbands_factor_prefix}_BB_Upper"
        rsi_col = self.rsi_factor_name
        required = ['Close', bb_low_col, bb_up_col, rsi_col]
        if not validate_input(target_data, required):
            logger.error(f"均值回归信号生成缺少列: {required}")
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)

        signals_df = pd.DataFrame(index=target_data.index)
        price = target_data['Close']
        bb_low = target_data[bb_low_col]
        bb_up = target_data[bb_up_col]
        rsi = target_data[rsi_col]

        buy_condition = (price < bb_low) & (rsi < self.rsi_oversold)
        sell_condition = (price > bb_up) & (rsi > self.rsi_overbought)

        signals_df['RawSignal'] = np.where(buy_condition, 1, np.where(sell_condition, -1, 0))
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]

# --- TrendWithVolatilityStrategy 和 STRATEGIES 保持不变 ---
class TrendWithVolatilityStrategy(TradingStrategy):
    """趋势+波动率过滤策略 - 使用预计算因子"""
    short_factor_name = 'SMA_20'
    long_factor_name = 'SMA_60'
    volatility_factor_name = 'Volatility_20' # 年化波动率因子
    vol_threshold = 0.20 # 波动率阈值
    stop_loss_pct = 0.06
    take_profit_pct = 0.12

    def init(self):
        required = [self.short_factor_name, self.long_factor_name, self.volatility_factor_name]
        missing = [f for f in required if not hasattr(self.data, f)]
        if missing: logger.error(f"趋势波动率策略缺少因子: {missing}"); return
        self.short_ma = self.data[self.short_factor_name]
        self.long_ma = self.data[self.long_factor_name]
        self.volatility = self.data[self.volatility_factor_name]

    def next(self):
        if len(self.volatility) < 1 or pd.isna(self.volatility[-1]): return
        current_vol = self.volatility[-1]
        price = self.data.Close[-1]
        sl = price * (1 - self.stop_loss_pct); tp = price * (1 + self.take_profit_pct)

        if current_vol < self.vol_threshold:
            if self.position: self.position.close()
            return

        if crossover(self.short_ma, self.long_ma):
            if not self.position: self.buy(sl=sl, tp=tp)
        elif crossover(self.long_ma, self.short_ma):
            if self.position: self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        required = ['Close', self.short_factor_name, self.long_factor_name, self.volatility_factor_name]
        if not validate_input(target_data, required):
            logger.error(f"趋势波动率信号生成缺少列: {required}")
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)

        signals_df = pd.DataFrame(index=target_data.index)
        short_ma = target_data[self.short_factor_name]
        long_ma = target_data[self.long_factor_name]
        volatility = target_data[self.volatility_factor_name]

        signals_df['TrendSignal'] = np.where(short_ma > long_ma, 1, np.where(short_ma < long_ma, -1, 0))
        signals_df['VolSignal'] = np.where(volatility >= self.vol_threshold, signals_df['TrendSignal'], 0)
        signals_df['Signal'] = signals_df['VolSignal'].diff().fillna(0)
        signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]


STRATEGIES: Dict[str, Type[TradingStrategy]] = {
    'TrendWithVolatility': TrendWithVolatilityStrategy,
    'MeanReversion': MeanReversionStrategy,
    'RevisedTrend': TrendFollowingStrategy
}

# --- 内部测试 ---
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    try:
         from ..核心代码.市场数据.数据获取器 import MarketData, load_sample_data
         from ..配置.系统配置 import Config
    except ImportError:
         # Fallback
         import sys; import os
         project_root=os.path.abspath(os.path.join(os.path.dirname(__file__),'..','..'));
         if project_root not in sys.path: sys.path.insert(0,project_root)
         from 核心代码.市场数据.数据获取器 import MarketData, load_sample_data
         from 配置.系统配置 import Config

    # 使用包含因子计算的 MarketData
    config = Config()
    # 定义需要计算的因子 (这些名字需要和策略中使用的匹配)
    config.factor_config = {
        'SMA_20': {'function': 'SMA', 'params': {'window': 20}},
        'SMA_60': {'function': 'SMA', 'params': {'window': 60}},
        'RSI_14': {'function': 'RSI', 'params': {'window': 14}},
        'BBands': {'function': 'BBands', 'params': {'window': 20, 'num_std_dev': 2.0}}, # BBands 会生成多列 BBands_BB_Upper 等
        'Volatility_20': {'function': 'Volatility', 'params': {'window': 20}},
    }
    md_handler = MarketData(config)

    test_data_with_factors = md_handler.get_market_data('AAPL', '2022-01-01', '2023-12-31')

    if test_data_with_factors is not None:
        print("\n--- 测试使用因子数据的策略 (简单回测) ---")
        # 策略参数现在可以省略因子计算参数，因为因子已存在
        trend_strat_fac = TrendFollowingStrategy(params={'stop_loss_pct': 0.06})
        mr_strat_fac = MeanReversionStrategy(params={'rsi_overbought': 72})

        for strat in [trend_strat_fac, mr_strat_fac]:
            print(f"\nTesting {strat.__class__.__name__} with factors...")
            result = strat.run_simple_backtest(test_data_with_factors)
            print(f"结果: {result}")
            if result.trades == 0:
                 print("警告：回测未产生交易，请检查策略逻辑或因子数据。")
                 print("可用因子列:", test_data_with_factors.columns.tolist())

    else: print("无法加载带因子数据，跳过策略测试。")
