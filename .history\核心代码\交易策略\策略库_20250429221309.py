# -*- coding: utf-8 -*-
import logging
from typing import Dict, List, Tuple, Optional, Any, Type
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from collections import defaultdict

# 尝试导入 backtesting 用于类型提示和基类
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    BASE_STRATEGY = BacktestStrategy
    backtesting_available = True
except ImportError:
    logging.warning("backtesting库未安装，部分基于该库的策略功能可能受限。")
    class DummyStrategy:
         def __init__(self, broker, data, params): pass
         def init(self): pass
         def next(self): pass
         def I(self, func, *args, **kwargs): return pd.Series(dtype=np.float64)
         @property
         def data(self):
              return pd.DataFrame({'Open':[], 'High':[], 'Low':[], 'Close':[], 'Volume':[]})
         @property
         def position(self):
              class MockPosition:
                   size = 0
                   is_long = False
                   is_short = False
                   pl_pct = 0.0
                   entry_price = 0.0
                   def close(self): return
              return MockPosition()
         def buy(self, **kwargs): pass
         def sell(self, **kwargs): pass
    BASE_STRATEGY = DummyStrategy
    backtesting_available = False
    def crossover(s1, s2): return False

logger = logging.getLogger(__name__)

# --- 辅助函数和数据类 ---
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool:
    if data is None or data.empty:
        logger.error("输入数据为空")
        return False
    if not isinstance(data, pd.DataFrame):
        logger.error(f"输入数据类型错误，应为pd.DataFrame，得到：{type(data)}")
        return False
    data_columns_lower = [str(c).lower() for c in data.columns]
    required_cols_lower = [c.lower() for c in required_cols]
    for col_lower in required_cols_lower:
        if col_lower not in data_columns_lower:
            original_col = next((c for c in required_cols if c.lower() == col_lower), col_lower.capitalize())
            logger.error(f"输入数据缺少必要列: {original_col}")
            return False
    check_cols = [c for c in data.columns if str(c).lower() in required_cols_lower]
    if data[check_cols].isnull().values.any():
        logger.warning("输入数据包含NaN值，可能影响策略计算。建议填充。")
    return True

@dataclass
class BacktestResult:
    sharpe: float = np.nan
    returns: float = np.nan
    drawdown: float = np.nan
    trades: int = 0
    win_rate: float = np.nan
    net_return: float = np.nan
    annual_return: float = np.nan
    volatility: float = np.nan
    calmar_ratio: float = np.nan

# --- 交易策略基类 ---
class TradingStrategy(BASE_STRATEGY):
    window = 20
    threshold = 1.5
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    parameters: Dict[str, Any] = field(default_factory=dict)
    transaction_cost_pct: float = 0.001
    risk_manager = None
    _data = None

    def __init__(self, broker=None, data=None, params: Optional[Dict] = None):
        self.parameters = {}
        cls = self.__class__
        potential_params = ['window', 'threshold', 'stop_loss_pct', 'take_profit_pct']
        for param_name in potential_params:
             if hasattr(cls, param_name):
                 self.parameters[param_name] = getattr(cls, param_name)
        if params:
            self.parameters.update(params)
        for key, value in self.parameters.items():
             if hasattr(self, key):
                 setattr(self, key, value)
        if backtesting_available:
            super().__init__(broker, data, self.parameters)
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        if data is not None and not isinstance(data, list):
             self._data = data

    def set_data(self, data: pd.DataFrame):
        if validate_input(data):
            self._data = data
            if not hasattr(self, 'data') or self.data is None:
                 self.data = data
        else:
            raise ValueError("设置的数据无效")

    def set_transaction_cost(self, cost_pct: float):
        if 0 <= cost_pct < 1:
            self.transaction_cost_pct = cost_pct
            self.parameters['transaction_cost_pct'] = cost_pct
        else:
            logger.error(f"无效的交易成本比例: {cost_pct}")

    def generate_signal(self, current_data: pd.DataFrame) -> str:
        logger.warning(f"{self.__class__.__name__} 未实现 generate_signal 方法，返回 HOLD")
        return 'HOLD'

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if target_data is None or target_data.empty:
            logger.error("无法生成信号DataFrame：无有效数据")
            index = pd.Index([]) if target_data is None else target_data.index
            return pd.DataFrame({'Signal': 0}, index=index)
        logger.warning(f"{self.__class__.__name__} 未实现 generate_signals_dataframe 方法，返回全0信号")
        return pd.DataFrame({'Signal': 0}, index=target_data.index)

    def run_simple_backtest(self, data: Optional[pd.DataFrame] = None, initial_capital: float = 100000.0) -> BacktestResult:
        target_data = data if data is not None else self._data
        if not validate_input(target_data):
            logger.error("无法运行简单回测：数据无效")
            return BacktestResult()
        if not isinstance(target_data.index, pd.DatetimeIndex):
            logger.error("简单回测需要 DataFrame 索引为 DatetimeIndex")
            return BacktestResult()
        signals_df = self.generate_signals_dataframe(target_data)
        if 'Signal' not in signals_df.columns:
             logger.error("generate_signals_dataframe 未返回包含 'Signal' 列的 DataFrame")
             return BacktestResult()
        merged_df = target_data.join(signals_df[['Signal']], how='left')
        merged_df['Position'] = merged_df['Signal'].ffill().fillna(0)
        merged_df['Position'] = np.sign(merged_df['Position']).astype(int)
        merged_df['MarketReturn'] = merged_df['Close'].pct_change()
        merged_df['StrategyReturn'] = merged_df['Position'].shift(1) * merged_df['MarketReturn']
        trades = merged_df['Position'].diff().fillna(0) != 0
        trade_cost = trades * self.transaction_cost_pct * 2
        merged_df['StrategyReturn'] -= trade_cost
        merged_df['StrategyReturn'] = merged_df['StrategyReturn'].fillna(0)
        merged_df['EquityCurve'] = initial_capital * (1 + merged_df['StrategyReturn']).cumprod()
        final_equity = merged_df['EquityCurve'].iloc[-1] if not merged_df.empty else initial_capital
        initial_equity = merged_df['EquityCurve'].iloc[0] if not merged_df.empty else initial_capital
        try:
            total_return = (final_equity / initial_equity) - 1 if initial_equity != 0 else 0.0
        except Exception as e:
            logger.error(f"计算总收益率时发生错误: {str(e)}")
            total_return = 0.0
        daily_strategy_returns = merged_df['StrategyReturn'].iloc[1:]
        if daily_strategy_returns.empty or daily_strategy_returns.std() == 0:
             sharpe = 0.0
             volatility = 0.0
             calmar = 0.0
             annual_return = 0.0
        else:
             volatility = daily_strategy_returns.std() * np.sqrt(252)
             sharpe = np.sqrt(252) * daily_strategy_returns.mean() / daily_strategy_returns.std()
             if not merged_df.empty and len(merged_df.index) > 1:
                 time_delta = merged_df.index[-1] - merged_df.index[0]
                 years = max(time_delta.days / 365.25, 1/252)
             else:
                 years = 1.0
             annual_return = (1 + total_return) ** (1 / years) - 1
        rolling_max = merged_df['EquityCurve'].cummax()
        daily_drawdown = (merged_df['EquityCurve'] / rolling_max) - 1.0
        max_drawdown = abs(daily_drawdown.min())
        if max_drawdown == 0:
            calmar = np.inf if annual_return > 0 else 0
        else:
            calmar = annual_return / max_drawdown
        num_trades = trades.sum() // 2
        trade_returns = merged_df.loc[trades, 'StrategyReturn']
        win_rate = (trade_returns > 0).sum() / len(trade_returns) if len(trade_returns) > 0 else 0.0
        logger.info(f"简单回测: Ret={total_return:.2%}, Sharpe={sharpe:.2f}, MaxDD={max_drawdown:.2%}, Trades={num_trades}, WinRate={win_rate:.1%}")
        return BacktestResult(
            sharpe=float(sharpe),
            returns=float(total_return),
            drawdown=float(max_drawdown),
            trades=int(num_trades),
            win_rate=float(win_rate),
            net_return=float(total_return),
            annual_return=float(annual_return),
            volatility=float(volatility),
            calmar_ratio=float(calmar) if calmar != np.inf else None
        )

# --- 具体策略实现 ---
class MeanReversionStrategy(TradingStrategy):
    window = 2
    threshold = 0.6
    rsi_window = 5
    rsi_overbought = 60
    rsi_oversold = 40
    trade_frequency = '2W'  # 每周交易2次
    position_size = 1.0     # 全仓交易
    stop_loss = 0.05        # 止损5%
    take_profit = 0.1       # 止盈10%

    def init(self):
        close = pd.Series(self.data.Close)
        self.mean = self.I(lambda x: x.rolling(self.window).mean(), close)
        self.std = self.I(lambda x: x.rolling(self.window).std(), close)
        def rsi_func(c, n):
            delta = c.diff()
            gain = delta.where(delta > 0, 0).ewm(com=n-1, adjust=False).mean()
            loss = -delta.where(delta < 0, 0).ewm(com=n-1, adjust=False).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
        self.rsi = self.I(rsi_func, close, self.rsi_window)

    def next(self):
        if len(self.mean) < 1 or len(self.std) < 1 or self.std[-1] == 0 or len(self.rsi) < 1:
            return
        price = self.data.Close[-1]
        z_score = (price - self.mean[-1]) / self.std[-1]
        rsi_val = self.rsi[-1]
        sl_buy = price * (1 - self.stop_loss)
        tp_buy = price * (1 + self.take_profit)
        sl_sell = price * (1 + self.stop_loss)
        tp_sell = price * (1 - self.take_profit)
        if z_score > self.threshold and rsi_val > self.rsi_overbought:
            if not self.position:
                self.sell(sl=sl_sell, tp=tp_sell)
        elif z_score < -self.threshold and rsi_val < self.rsi_oversold:
            if not self.position:
                self.buy(sl=sl_buy, tp=tp_buy)
        elif abs(z_score) < 0.5 and self.position:
            self.position.close()

    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        if not validate_input(target_data, ['Close']):
            raise ValueError("数据无效")
        win = self.parameters.get('window', self.window)
        thresh = self.parameters.get('threshold', self.threshold)
        signals = pd.DataFrame(index=target_data.index)
        mean = target_data['Close'].rolling(win).mean()
        std = target_data['Close'].rolling(win).std().replace(0, np.nan)
        z_score = (target_data['Close'] - mean) / std
        signals['Signal'] = np.where(z_score > thresh, -1, 
                                   np.where(z_score < -thresh, 1, 0))
        signals['Signal'] = signals['Signal'].diff().fillna(0)
        signals['Signal'] = np.sign(signals['Signal']).astype(int)
        return signals

# --- 策略注册表 ---
STRATEGIES: Dict[str, Type[TradingStrategy]] = {
    'MeanReversion': MeanReversionStrategy
}
