# -*- coding: utf-8 -*-
"""
机器学习增强趋势跟踪策略 - MLEnhancedTrendFollowingStrategy
使用机器学习模型预测信号质量和市场状态
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
import pickle
import os
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class MLEnhancedTrendFollowingStrategy:
    """
    机器学习增强趋势跟踪策略
    
    核心功能:
    1. 使用ML模型预测信号质量
    2. 动态调整策略参数
    3. 市场状态识别
    4. 风险评估
    """
    
    def __init__(self, backtester=None, symbols=None, params=None):
        if params is None:
            params = {}
        
        self.strategy_name = "MLEnhancedTrendFollowingStrategy"
        self.backtester = backtester
        self.symbols = symbols or ['BTCUSDT']
        
        # 基础策略参数
        self.sma_short = params.get('sma_short', 12)
        self.sma_long = params.get('sma_long', 26)
        self.rsi_period = params.get('rsi_period', 14)
        self.atr_period = params.get('atr_period', 14)
        self.adx_period = params.get('adx_period', 14)
        
        # ML模型参数
        self.use_ml_filter = params.get('use_ml_filter', True)
        self.ml_confidence_threshold = params.get('ml_confidence_threshold', 0.6)
        self.feature_lookback = params.get('feature_lookback', 20)
        self.retrain_frequency = params.get('retrain_frequency', 1000)  # 每1000个样本重训练
        
        # 风险管理参数
        self.base_risk_per_trade = params.get('base_risk_per_trade', 0.01)
        self.max_risk_per_trade = params.get('max_risk_per_trade', 0.02)
        self.min_risk_per_trade = params.get('min_risk_per_trade', 0.005)
        
        # ML模型
        self.signal_quality_model = None
        self.market_regime_model = None
        self.scaler = StandardScaler()
        self.is_model_trained = False
        
        # 数据存储
        self.training_data = []
        self.model_performance = {'accuracy': 0, 'last_retrain': 0}
        
        # 模型文件路径
        self.model_dir = "D:/高频量化交易系统/模型文件"
        os.makedirs(self.model_dir, exist_ok=True)
        
        # 加载预训练模型
        self.load_models()
        
        logger.info(f"{self.strategy_name}: ML增强策略初始化完成")
        logger.info(f"ML过滤: {self.use_ml_filter}, 置信度阈值: {self.ml_confidence_threshold}")
    
    def extract_features(self, data: pd.DataFrame, current_idx: int) -> np.ndarray:
        """提取机器学习特征"""
        try:
            if current_idx < self.feature_lookback:
                return None
            
            # 获取历史数据窗口
            window_data = data.iloc[current_idx-self.feature_lookback:current_idx+1]
            
            features = []
            
            # 价格特征
            close_prices = window_data['CLOSE'].values
            returns = np.diff(close_prices) / close_prices[:-1]
            
            features.extend([
                np.mean(returns),           # 平均收益率
                np.std(returns),            # 收益率波动率
                np.skew(returns) if len(returns) > 2 else 0,  # 偏度
                np.max(returns),            # 最大收益率
                np.min(returns),            # 最小收益率
                (close_prices[-1] - close_prices[0]) / close_prices[0]  # 总收益率
            ])
            
            # 技术指标特征
            if f'SMA_{self.sma_short}' in window_data.columns:
                sma_short = window_data[f'SMA_{self.sma_short}'].iloc[-1]
                sma_long = window_data[f'SMA_{self.sma_long}'].iloc[-1]
                features.extend([
                    (close_prices[-1] - sma_short) / sma_short if sma_short > 0 else 0,
                    (close_prices[-1] - sma_long) / sma_long if sma_long > 0 else 0,
                    (sma_short - sma_long) / sma_long if sma_long > 0 else 0
                ])
            else:
                features.extend([0, 0, 0])
            
            # RSI特征
            if f'RSI_{self.rsi_period}' in window_data.columns:
                rsi_values = window_data[f'RSI_{self.rsi_period}'].dropna()
                if len(rsi_values) > 0:
                    features.extend([
                        rsi_values.iloc[-1] / 100,  # 当前RSI
                        np.mean(rsi_values) / 100,  # 平均RSI
                        np.std(rsi_values) / 100    # RSI波动率
                    ])
                else:
                    features.extend([0.5, 0.5, 0])
            else:
                features.extend([0.5, 0.5, 0])
            
            # ATR特征
            if f'ATR_{self.atr_period}' in window_data.columns:
                atr_values = window_data[f'ATR_{self.atr_period}'].dropna()
                if len(atr_values) > 0:
                    features.extend([
                        atr_values.iloc[-1] / close_prices[-1],  # ATR相对值
                        np.mean(atr_values) / close_prices[-1],  # 平均ATR相对值
                    ])
                else:
                    features.extend([0, 0])
            else:
                features.extend([0, 0])
            
            # ADX特征
            if f'ADX_{self.adx_period}' in window_data.columns:
                adx_values = window_data[f'ADX_{self.adx_period}'].dropna()
                if len(adx_values) > 0:
                    features.extend([
                        adx_values.iloc[-1] / 100,  # 当前ADX
                        np.mean(adx_values) / 100,  # 平均ADX
                    ])
                else:
                    features.extend([0, 0])
            else:
                features.extend([0, 0])
            
            # 成交量特征
            if 'VOLUME' in window_data.columns:
                volumes = window_data['VOLUME'].values
                features.extend([
                    volumes[-1] / np.mean(volumes) if np.mean(volumes) > 0 else 1,  # 相对成交量
                    np.std(volumes) / np.mean(volumes) if np.mean(volumes) > 0 else 0  # 成交量变异系数
                ])
            else:
                features.extend([1, 0])
            
            # 价格位置特征
            high_prices = window_data['HIGH'].values
            low_prices = window_data['LOW'].values
            price_range = np.max(high_prices) - np.min(low_prices)
            if price_range > 0:
                price_position = (close_prices[-1] - np.min(low_prices)) / price_range
            else:
                price_position = 0.5
            features.append(price_position)
            
            return np.array(features)
            
        except Exception as e:
            logger.warning(f"特征提取失败: {e}")
            return None
    
    def create_training_labels(self, data: pd.DataFrame, signal_idx: int, 
                             lookforward: int = 10) -> Tuple[int, float]:
        """创建训练标签"""
        try:
            if signal_idx + lookforward >= len(data):
                return 0, 0.0  # 中性信号
            
            entry_price = data.iloc[signal_idx]['CLOSE']
            future_prices = data.iloc[signal_idx+1:signal_idx+lookforward+1]['CLOSE']
            
            # 计算未来收益率
            max_return = (future_prices.max() - entry_price) / entry_price
            min_return = (future_prices.min() - entry_price) / entry_price
            final_return = (future_prices.iloc[-1] - entry_price) / entry_price
            
            # 标签定义
            if max_return > 0.02:  # 2%以上收益
                label = 1  # 买入信号
                quality = min(1.0, max_return * 10)  # 质量分数
            elif min_return < -0.02:  # 2%以上亏损
                label = 2  # 卖出信号
                quality = min(1.0, abs(min_return) * 10)
            else:
                label = 0  # 中性信号
                quality = 0.1
            
            return label, quality
            
        except Exception as e:
            logger.warning(f"创建标签失败: {e}")
            return 0, 0.0
    
    def train_models(self, data: pd.DataFrame):
        """训练ML模型"""
        try:
            logger.info("开始训练ML模型...")
            
            # 确保数据有技术指标
            data = self.calculate_technical_indicators(data)
            
            features_list = []
            labels_list = []
            quality_list = []
            
            # 提取训练数据
            for i in range(self.feature_lookback, len(data) - 10):
                features = self.extract_features(data, i)
                if features is not None:
                    label, quality = self.create_training_labels(data, i)
                    
                    features_list.append(features)
                    labels_list.append(label)
                    quality_list.append(quality)
            
            if len(features_list) < 100:
                logger.warning("训练数据不足，跳过模型训练")
                return
            
            X = np.array(features_list)
            y = np.array(labels_list)
            
            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)
            
            # 分割训练测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 训练信号质量模型
            self.signal_quality_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            )
            self.signal_quality_model.fit(X_train, y_train)
            
            # 评估模型
            y_pred = self.signal_quality_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            self.model_performance['accuracy'] = accuracy
            self.model_performance['last_retrain'] = len(data)
            self.is_model_trained = True
            
            logger.info(f"ML模型训练完成，准确率: {accuracy:.3f}")
            
            # 保存模型
            self.save_models()
            
        except Exception as e:
            logger.error(f"ML模型训练失败: {e}")
    
    def predict_signal_quality(self, features: np.ndarray) -> Tuple[int, float]:
        """预测信号质量"""
        try:
            if not self.is_model_trained or self.signal_quality_model is None:
                return 0, 0.5  # 默认中性
            
            # 标准化特征
            features_scaled = self.scaler.transform(features.reshape(1, -1))
            
            # 预测
            prediction = self.signal_quality_model.predict(features_scaled)[0]
            probabilities = self.signal_quality_model.predict_proba(features_scaled)[0]
            
            # 计算置信度
            confidence = np.max(probabilities)
            
            return int(prediction), float(confidence)
            
        except Exception as e:
            logger.warning(f"信号质量预测失败: {e}")
            return 0, 0.5
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()
            
            # SMA
            df[f'SMA_{self.sma_short}'] = df['CLOSE'].rolling(self.sma_short).mean()
            df[f'SMA_{self.sma_long}'] = df['CLOSE'].rolling(self.sma_long).mean()
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df[f'RSI_{self.rsi_period}'] = 100 - (100 / (1 + rs))
            
            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df[f'ATR_{self.atr_period}'] = true_range.rolling(self.atr_period).mean()
            
            # ADX (简化版)
            df[f'ADX_{self.adx_period}'] = df['CLOSE'].rolling(self.adx_period).std() / df['CLOSE'].rolling(self.adx_period).mean() * 100
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def save_models(self):
        """保存模型"""
        try:
            if self.signal_quality_model is not None:
                model_file = os.path.join(self.model_dir, "signal_quality_model.pkl")
                with open(model_file, 'wb') as f:
                    pickle.dump(self.signal_quality_model, f)
                
                scaler_file = os.path.join(self.model_dir, "feature_scaler.pkl")
                with open(scaler_file, 'wb') as f:
                    pickle.dump(self.scaler, f)
                
                logger.info("ML模型已保存")
                
        except Exception as e:
            logger.warning(f"保存模型失败: {e}")
    
    def load_models(self):
        """加载模型"""
        try:
            model_file = os.path.join(self.model_dir, "signal_quality_model.pkl")
            scaler_file = os.path.join(self.model_dir, "feature_scaler.pkl")
            
            if os.path.exists(model_file) and os.path.exists(scaler_file):
                with open(model_file, 'rb') as f:
                    self.signal_quality_model = pickle.load(f)
                
                with open(scaler_file, 'rb') as f:
                    self.scaler = pickle.load(f)
                
                self.is_model_trained = True
                logger.info("ML模型加载成功")
            else:
                logger.info("未找到预训练模型，将在有足够数据时训练")
                
        except Exception as e:
            logger.warning(f"加载模型失败: {e}")
    
    def generate_signals(self, data: pd.DataFrame, current_time: pd.Timestamp, 
                        portfolio_value: float, positions: Dict) -> List[Dict]:
        """生成交易信号"""
        signals = []
        
        try:
            if current_time not in data.index:
                return signals
            
            symbol = 'BTCUSDT'
            
            # 获取历史数据
            historical_data = data.loc[:current_time]
            if len(historical_data) < self.feature_lookback + 50:
                return signals
            
            # 计算技术指标
            historical_data = self.calculate_technical_indicators(historical_data)
            
            # 检查是否需要重新训练模型
            if (not self.is_model_trained or 
                len(historical_data) - self.model_performance.get('last_retrain', 0) > self.retrain_frequency):
                self.train_models(historical_data)
            
            current_idx = len(historical_data) - 1
            current_data = historical_data.iloc[current_idx]
            
            # 提取当前特征
            features = self.extract_features(historical_data, current_idx)
            if features is None:
                return signals
            
            # ML信号质量预测
            if self.use_ml_filter and self.is_model_trained:
                ml_signal, ml_confidence = self.predict_signal_quality(features)
                
                # 如果ML置信度不足，跳过信号
                if ml_confidence < self.ml_confidence_threshold:
                    return signals
            else:
                ml_signal = 1  # 默认买入倾向
                ml_confidence = 0.6
            
            # 基础技术分析
            close_price = current_data.get('CLOSE', 0)
            sma_short = current_data.get(f'SMA_{self.sma_short}', 0)
            sma_long = current_data.get(f'SMA_{self.sma_long}', 0)
            rsi = current_data.get(f'RSI_{self.rsi_period}', 50)
            atr = current_data.get(f'ATR_{self.atr_period}', 0)
            
            if close_price <= 0 or atr <= 0:
                return signals
            
            # 检查持仓
            current_position = positions.get(symbol, {})
            has_position = current_position.get('shares', 0) > 1e-9
            
            # 动态风险调整
            risk_multiplier = ml_confidence  # 基于ML置信度调整风险
            adjusted_risk = self.base_risk_per_trade * risk_multiplier
            adjusted_risk = np.clip(adjusted_risk, self.min_risk_per_trade, self.max_risk_per_trade)
            
            # 生成信号
            if not has_position:
                if (ml_signal == 1 and sma_short > sma_long and 
                    close_price > sma_short and rsi < 75):
                    
                    # 买入信号
                    stop_loss = close_price - (atr * 2.5)
                    take_profit = close_price + (atr * 5.0)
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'price': close_price,
                        'size': adjusted_risk,
                        'timestamp': current_time,
                        'signal_type': 'ml_enhanced_buy',
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'ml_confidence': ml_confidence,
                        'reason': f'ML增强买入信号 (置信度: {ml_confidence:.2f})'
                    })
            
            elif has_position:
                # 退出条件
                if (ml_signal == 0 or ml_confidence < 0.3 or 
                    sma_short < sma_long or rsi > 80):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'price': close_price,
                        'size': current_position.get('shares', 0),
                        'timestamp': current_time,
                        'signal_type': 'ml_enhanced_exit',
                        'reason': 'ML信号减弱或技术指标转向'
                    })
            
        except Exception as e:
            logger.error(f"ML增强信号生成失败: {e}")
        
        return signals
    
    def on_bar(self, current_bars_all_symbols: pd.DataFrame) -> List[Dict]:
        """回测引擎兼容接口"""
        try:
            if current_bars_all_symbols.empty:
                return []
            
            current_time = current_bars_all_symbols.index[0]
            
            # 获取组合信息
            if hasattr(self.backtester, 'portfolio'):
                if hasattr(self.backtester.portfolio, 'total_value'):
                    portfolio_value = self.backtester.portfolio.total_value
                else:
                    portfolio_value = 100000
                positions = self.backtester.portfolio.positions
            else:
                portfolio_value = 100000
                positions = {}
            
            # 获取历史数据
            if hasattr(self.backtester, 'all_historical_data'):
                try:
                    if isinstance(self.backtester.all_historical_data, dict):
                        historical_data = self.backtester.all_historical_data.get('BTCUSDT', current_bars_all_symbols)
                    else:
                        historical_data = self.backtester.all_historical_data.xs('BTCUSDT', level=0)
                    available_data = historical_data.loc[:current_time]
                except:
                    available_data = current_bars_all_symbols
            else:
                available_data = current_bars_all_symbols
            
            return self.generate_signals(available_data, current_time, portfolio_value, positions)
            
        except Exception as e:
            logger.error(f"ML增强on_bar失败: {e}")
            return []

# 注册策略
def get_strategy_class():
    return MLEnhancedTrendFollowingStrategy
