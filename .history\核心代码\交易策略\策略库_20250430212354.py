# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
from abc import ABC, abstractmethod
from typing import Optional
from backtesting import Strategy

logger = logging.getLogger(__name__)

class TradingStrategy(ABC):
    """交易策略基类"""
    def __init__(self):
        self._ready = False

    @abstractmethod
    def next(self):
        """执行策略逻辑"""
        pass

    def init(self):
        """初始化策略"""
        self._ready = True

class MeanReversionStrategy(Strategy):
    # 策略参数
    trade_frequency = '2W'
    position_size = 1.0
    stop_loss = 0.05
    take_profit = 0.1
    rsi_overbought = 70
    rsi_oversold = 30

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.parameters = {
            'trade_frequency': self.trade_frequency,
            'position_size': self.position_size,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'rsi_overbought': self.rsi_overbought,
            'rsi_oversold': self.rsi_oversold
        }

    # --- Parameters defining expected factor column names (Use correct case!) ---
    bbands_factor_prefix = 'BBands_Default' # Prefix used in factor_config key
    rsi_factor_name = 'RSI_14'
    atr_factor_name = 'ATR_14'
    macd_line_factor_name = 'MACD_Default_MACD_Line'
    macd_signal_factor_name = 'MACD_Default_Signal_Line' # Correct signal line name
    macd_hist_factor_name = 'MACD_Default_MACD_Hist'

    # ... (Strategy Logic Parameters) ...
    _ready: bool = False

    def init(self):
        logger.debug(f"Initializing {self.__class__.__name__}...")
        # --- Construct required column names WITH CORRECT CASE ---
        self.bb_lower_col = f"{self.bbands_factor_prefix}_BB_Lower" # Correct case
        self.bb_upper_col = f"{self.bbands_factor_prefix}_BB_Upper" # Correct case
        self.bb_middle_col = f"{self.bbands_factor_prefix}_BB_Middle"

        required_factors = [
            'Close', 
            self.bb_lower_col, 
            self.bb_upper_col, 
            self.bb_middle_col,
            self.rsi_factor_name,
            self.atr_factor_name,
            self.macd_line_factor_name,
            self.macd_signal_factor_name,
            # self.macd_hist_factor_name # Add if used
        ]

        # --- Check existence using self.data.df (which backtesting provides) ---
        if not hasattr(self, 'data') or not hasattr(self.data, 'df'):
            logger.error("Backtest 数据未正确初始化")
            self._ready = False
            return

        missing = [f for f in required_factors if f not in self.data.df.columns]
        if missing:
            logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing}")
            logger.debug(f"可用列: {self.data.df.columns.tolist()}")
            self._ready = False
            return

        logger.info(f"策略 '{self.__class__.__name__}' 初始化成功。")
        self._ready = True

    def next(self):
        if not self._ready: return

        try:
            # --- Access data using self.data.df ---
            price = self.data.df['Close'].iloc[-1].item()
            bb_low_val = self.data.df[self.bb_lower_col].iloc[-1].item()
            bb_up_val = self.data.df[self.bb_upper_col].iloc[-1].item()
            bb_mid_val = self.data.df[self.bb_middle_col].iloc[-1].item()
            rsi_val = self.data.df[self.rsi_factor_name].iloc[-1].item()
            macd_line_val = self.data.df[self.macd_line_factor_name].iloc[-1].item()
            macd_signal_val = self.data.df[self.macd_signal_factor_name].iloc[-1].item()

            if any(pd.isna(v) for v in [price, bb_low_val, bb_up_val, bb_mid_val, rsi_val, macd_line_val, macd_signal_val]):
                return
        except (IndexError, KeyError) as e:
             # This should ideally not happen if init check passed and data is sufficient
             return

        # ... (Get parameters: rsi_ob, rsi_os, sl_pct, tp_pct) ...
        rsi_ob = self.parameters.get('rsi_overbought', 70)
        rsi_os = self.parameters.get('rsi_oversold', 30)
        sl_pct = self.parameters.get('stop_loss_pct', 0.03)
        tp_pct = self.parameters.get('take_profit_pct', 0.08)

        sl_buy = price * (1 - sl_pct); tp_buy = price * (1 + tp_pct)
        sl_sell = price * (1 + sl_pct); tp_sell = price * (1 - tp_pct)

        # --- Trading Logic (using correct variables) ---
        macd_is_bullish = macd_line_val > macd_signal_val # Compare line and signal

        # Sell signal
        if price > bb_up_val and rsi_val > rsi_ob: # Example condition
            if not self.position: self.sell(sl=sl_sell, tp=tp_sell)

        # Buy signal
        elif price < bb_low_val and rsi_val < rsi_os: # Example condition
            if not self.position: self.buy(sl=sl_buy, tp=tp_buy)

        # Exit signal
        elif self.position:
            if self.position.is_long and price >= bb_mid_val: self.position.close()
            elif self.position.is_short and price <= bb_mid_val: self.position.close()

    # ... (generate_signals_dataframe needs similar case/name corrections) ...
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        target_data = data if data is not None else self._data
        # --- Construct correct column names ---
        bb_low_col = f"{self.bbands_factor_prefix}_BB_Lower"
        bb_up_col = f"{self.bbands_factor_prefix}_BB_Upper"
        rsi_col = self.rsi_factor_name
        required = ['Close', bb_low_col, bb_up_col, rsi_col] # Use correct case 'Close'
        # --- Validate using correct case ---
        if not validate_input(target_data, required):
             logger.error(f"MR信号生成缺少列: {required}")
             return pd.DataFrame({'Signal': 0}, index=getattr(target_data,'index',pd.Index([])))

        signals_df = pd.DataFrame(index=target_data.index)
        price = target_data['Close'] # Correct case
        bb_low = target_data[bb_low_col]
        bb_up = target_data[bb_up_col]
        rsi = target_data[rsi_col]
        rsi_ob = self.parameters.get('rsi_overbought', 70)
        rsi_os = self.parameters.get('rsi_oversold', 30)

        buy_condition = (price < bb_low) & (rsi < rsi_os)
        sell_condition = (price > bb_up) & (rsi > rsi_ob)

        signals_df['RawSignal'] = np.where(buy_condition, 1, np.where(sell_condition, -1, 0))
        signals_df['Signal'] = signals_df['RawSignal'].diff().fillna(0)
        signals_df['Signal'] = np.sign(signals_df['Signal']).astype(int)
        return signals_df[['Signal']]

from dataclasses import dataclass

@dataclass
class BacktestResult:
    """封装回测结果的类"""
    strategy_name: str
    returns: pd.Series
    trades: pd.DataFrame
    stats: dict

# 定义可用的策略
STRATEGIES = {
    'EnhancedMeanReversion': MeanReversionStrategy
}
