# -*- coding: utf-8 -*-
"""
策略对比分析报告
"""
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_strategy_comparison():
    """创建策略对比分析"""
    
    # 策略回测结果数据
    strategies_data = {
        'AlphaXInspiredStrategy': {
            '初始资金': 100000.0,
            '最终权益': 97910.74,
            '总收益': -2089.26,
            '总收益率': -2.09,
            '年化收益率': -66.99,
            '年化波动率': 5.78,
            '夏普比率': -1922.46,
            '最大回撤': 2095.52,
            '最大回撤率': 2.10,
            '总交易次数': 21,
            '盈利次数': 6,
            '亏损次数': 15,
            '胜率': 28.57,
            '盈亏比': 75.80
        },
        'TrendFollowingStrategy': {
            '初始资金': 100000.0,
            '最终权益': 86729.21,
            '总收益': -13270.79,
            '总收益率': -13.27,
            '年化收益率': -99.94,
            '年化波动率': 11.29,
            '夏普比率': -6415.07,
            '最大回撤': 13313.01,
            '最大回撤率': 13.31,
            '总交易次数': 146,
            '盈利次数': 44,
            '亏损次数': 102,
            '胜率': 30.14,
            '盈亏比': 89.40
        },
        'MeanReversionStrategy': {
            '初始资金': 100000.0,
            '最终权益': 91720.38,
            '总收益': -8279.62,
            '总收益率': -8.28,
            '年化收益率': -98.93,
            '年化波动率': 7.26,
            '夏普比率': -6295.49,
            '最大回撤': 8287.42,
            '最大回撤率': 8.29,
            '总交易次数': 110,
            '盈利次数': 44,
            '亏损次数': 66,
            '胜率': 40.00,
            '盈亏比': 48.79
        }
    }
    
    # 创建DataFrame
    df = pd.DataFrame(strategies_data).T
    
    print("=== 三大策略回测结果对比分析 ===")
    print("测试期间：2023-01-01 至 2023-01-07 (7天)")
    print("测试标的：BTCUSDT")
    print("初始资金：100,000 USDT")
    print()
    
    # 策略排名分析
    print("【策略表现排名】")
    print("按总收益率排名：")
    ranking_by_return = df.sort_values('总收益率', ascending=False)
    for i, (strategy, data) in enumerate(ranking_by_return.iterrows(), 1):
        print(f"  {i}. {strategy}: {data['总收益率']:.2f}%")
    
    print("\n按最大回撤排名（越小越好）：")
    ranking_by_drawdown = df.sort_values('最大回撤率', ascending=True)
    for i, (strategy, data) in enumerate(ranking_by_drawdown.iterrows(), 1):
        print(f"  {i}. {strategy}: {data['最大回撤率']:.2f}%")
    
    print("\n按胜率排名：")
    ranking_by_winrate = df.sort_values('胜率', ascending=False)
    for i, (strategy, data) in enumerate(ranking_by_winrate.iterrows(), 1):
        print(f"  {i}. {strategy}: {data['胜率']:.2f}%")
    
    # 详细分析
    print("\n【详细分析】")
    
    print("\n1. AlphaXInspiredStrategy（最佳表现）")
    print("   优势：")
    print("   - 最小亏损：-2.09%")
    print("   - 最低回撤：2.10%")
    print("   - 交易频率适中：21次/7天")
    print("   - 风险控制较好")
    print("   劣势：")
    print("   - 胜率最低：28.57%")
    print("   - 盈亏比不理想：75.80%")
    
    print("\n2. MeanReversionStrategy（中等表现）")
    print("   优势：")
    print("   - 胜率最高：40.00%")
    print("   - 回撤控制尚可：8.29%")
    print("   - 交易次数合理：110次/7天")
    print("   劣势：")
    print("   - 总亏损较大：-8.28%")
    print("   - 盈亏比最差：48.79%")
    
    print("\n3. TrendFollowingStrategy（最差表现）")
    print("   优势：")
    print("   - 胜率略高于AlphaX：30.14%")
    print("   - 盈亏比相对较好：89.40%")
    print("   劣势：")
    print("   - 最大亏损：-13.27%")
    print("   - 最大回撤：13.31%")
    print("   - 过度交易：146次/7天")
    
    # 问题分析
    print("\n【共同问题分析】")
    print("1. 所有策略都出现亏损，说明：")
    print("   - 当前市场环境不适合这些策略")
    print("   - 参数设置需要优化")
    print("   - 可能需要加入市场状态判断")
    
    print("\n2. 胜率普遍偏低（28-40%），说明：")
    print("   - 入场时机选择有问题")
    print("   - 技术指标组合需要调整")
    print("   - 可能需要更严格的信号过滤")
    
    print("\n3. 盈亏比不理想，说明：")
    print("   - 止损止盈比例设置不当")
    print("   - 需要动态调整风险管理参数")
    
    # 优化建议
    print("\n【优化建议】")
    print("1. 短期优化（基于AlphaXInspiredStrategy）：")
    print("   - 调整RSI阈值：35 → 25")
    print("   - 提高ADX要求：25 → 30")
    print("   - 增加ATR止损倍数：2.0 → 2.5")
    print("   - 延长信号间隔：120分钟 → 240分钟")
    
    print("\n2. 中期优化：")
    print("   - 添加市场状态过滤器")
    print("   - 引入成交量确认")
    print("   - 考虑时间段过滤")
    print("   - 添加连续亏损保护")
    
    print("\n3. 长期优化：")
    print("   - 开发组合策略")
    print("   - 机器学习参数优化")
    print("   - 多时间框架分析")
    print("   - 动态参数调整")
    
    return df

def create_comparison_chart(df):
    """创建对比图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('策略对比分析图表', fontsize=16, fontweight='bold')
    
    strategies = df.index.tolist()
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    # 1. 总收益率对比
    ax1 = axes[0, 0]
    returns = df['总收益率'].values
    bars1 = ax1.bar(strategies, returns, color=colors)
    ax1.set_title('总收益率对比 (%)')
    ax1.set_ylabel('收益率 (%)')
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    for i, v in enumerate(returns):
        ax1.text(i, v + 0.2 if v >= 0 else v - 0.5, f'{v:.2f}%', 
                ha='center', va='bottom' if v >= 0 else 'top')
    
    # 2. 最大回撤对比
    ax2 = axes[0, 1]
    drawdowns = df['最大回撤率'].values
    bars2 = ax2.bar(strategies, drawdowns, color=colors)
    ax2.set_title('最大回撤率对比 (%)')
    ax2.set_ylabel('回撤率 (%)')
    for i, v in enumerate(drawdowns):
        ax2.text(i, v + 0.2, f'{v:.2f}%', ha='center', va='bottom')
    
    # 3. 胜率对比
    ax3 = axes[1, 0]
    winrates = df['胜率'].values
    bars3 = ax3.bar(strategies, winrates, color=colors)
    ax3.set_title('胜率对比 (%)')
    ax3.set_ylabel('胜率 (%)')
    ax3.axhline(y=50, color='red', linestyle='--', alpha=0.7, label='50%基准线')
    for i, v in enumerate(winrates):
        ax3.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    ax3.legend()
    
    # 4. 交易次数对比
    ax4 = axes[1, 1]
    trade_counts = df['总交易次数'].values
    bars4 = ax4.bar(strategies, trade_counts, color=colors)
    ax4.set_title('交易次数对比')
    ax4.set_ylabel('交易次数')
    for i, v in enumerate(trade_counts):
        ax4.text(i, v + 2, f'{int(v)}', ha='center', va='bottom')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('策略对比分析图表.png', dpi=300, bbox_inches='tight')
    print(f"\n图表已保存为: 策略对比分析图表.png")
    
    plt.show()

if __name__ == '__main__':
    print("量化策略对比分析报告")
    print("=" * 50)
    
    # 创建对比分析
    df = create_strategy_comparison()
    
    # 创建对比图表
    create_comparison_chart(df)
    
    print("\n=== 结论 ===")
    print("在当前测试期间和参数设置下：")
    print("1. AlphaXInspiredStrategy表现最佳，亏损最小，风险控制最好")
    print("2. 所有策略都需要进一步优化才能实现盈利")
    print("3. 建议重点优化AlphaXInspiredStrategy，并考虑组合策略")
    print("4. 需要在更长时间周期和不同市场环境下验证策略稳定性")
