import pandas as pd
import os
import shutil

# 定义常量
SOURCE_FILE_PATH = r"D:\量化交易系统\核心代码\市场数据\市场_分钟\HKHKD-M1-No Session.csv"
OUTPUT_BASE_DIR = r"D:\量化交易系统\核心代码\市场数据\市场_分钟"
OUTPUT_SUBDIR = "按天分割"
OUTPUT_DIR = os.path.join(OUTPUT_BASE_DIR, OUTPUT_SUBDIR)

DATETIME_COLUMN = 'time'
# pandas的parse_dates通常能很好地处理标准日期时间格式，如 'YYYY-MM-DD HH:MM:SS'
OUTPUT_FILENAME_SUFFIX = '0005HKHKD-M1-No Session.csv'
CHUNK_SIZE = 100000  # 每次读取的行数，可以根据您的内存和文件大小调整

def ensure_dir(directory_path):
    """确保目录存在，如果不存在则创建它"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"目录已创建: {directory_path}")
    else:
        print(f"目录已存在: {directory_path}")

def split_csv_by_day():
    print(f"开始处理文件: {SOURCE_FILE_PATH}")
    ensure_dir(OUTPUT_DIR)

    # 用于跟踪哪些文件已经写入了表头，以避免在追加时重复写入
    header_written_files = set()

    # 检查源文件是否存在
    if not os.path.exists(SOURCE_FILE_PATH):
        print(f"错误：源文件未找到: {SOURCE_FILE_PATH}")
        return

    try:
        print(f"正在以每块 {CHUNK_SIZE} 行的大小读取CSV文件...")
        for i, chunk_df in enumerate(pd.read_csv(SOURCE_FILE_PATH, chunksize=CHUNK_SIZE, parse_dates=[DATETIME_COLUMN])):
            if chunk_df.empty:
                print(f"数据块 {i+1} 为空，跳过。")
                continue
            
            print(f"正在处理数据块 {i+1}，包含 {len(chunk_df)} 行...")

            # 确保datetime列已正确解析为datetime对象
            if not pd.api.types.is_datetime64_any_dtype(chunk_df[DATETIME_COLUMN]):
                 chunk_df[DATETIME_COLUMN] = pd.to_datetime(chunk_df[DATETIME_COLUMN])
                 print(f"数据块 {i+1} 中的 '{DATETIME_COLUMN}' 列已转换为datetime对象。")

            # 按天分组
            # .dt.date 会得到日期对象，可以用来分组
            for group_date, group_df in chunk_df.groupby(chunk_df[DATETIME_COLUMN].dt.date):
                if group_df.empty:
                    continue

                year = group_date.year
                month = group_date.month
                day = group_date.day

                # 构建输出文件名，例如：2009.1.50005HKHKD-M1-No Session.csv
                output_filename = f"{year}.{month}.{day}{OUTPUT_FILENAME_SUFFIX}"
                output_file_path = os.path.join(OUTPUT_DIR, output_filename)

                # 如果文件是第一次写入（或追加），则写入表头
                # 否则，以追加模式写入，不含表头
                if output_file_path not in header_written_files:
                    group_df.to_csv(output_file_path, index=False, mode='w', header=True) # 第一次用 'w' 模式
                    header_written_files.add(output_file_path)
                    # print(f"已创建文件 {output_file_path} 并写入 {len(group_df)} 行数据。")
                else:
                    group_df.to_csv(output_file_path, index=False, mode='a', header=False) # 后续用 'a' 模式
                    # print(f"已将 {len(group_df)} 行数据追加到 {output_file_path}。")
            print(f"数据块 {i+1} 处理完毕。")

        print(f"文件分割完成。分割后的小文件已保存到: {OUTPUT_DIR}")

    except FileNotFoundError:
        print(f"错误: 源文件 '{SOURCE_FILE_PATH}' 未找到。")
    except KeyError:
        print(f"错误: 在CSV文件中未找到日期时间列 '{DATETIME_COLUMN}'。请检查列名是否正确。")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    split_csv_by_day()
