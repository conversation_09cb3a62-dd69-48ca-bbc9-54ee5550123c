#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复后的交易信号测试
"""

import pandas as pd
import numpy as np

def test_signal_generation_fixed():
    print("="*60)
    print("修复后的交易信号测试")
    print("="*60)
    
    try:
        from 配置.系统配置 import Config
        from 核心代码.市场数据.数据获取器 import MarketData
        from 核心代码.因子计算.因子库 import calculate_factors
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        config = Config()
        market_data = MarketData(config)
        
        # 加载更多数据以找到合适的交易条件
        df = market_data.get_market_data("BTCUSDT", "2025-04-01", "2025-04-20", source='local')
        print(f"✅ 加载数据: {len(df)} 条记录")
        
        # 计算因子（正确的方式，不会产生重复列）
        df_with_factors = calculate_factors(df, config.factor_config)
        print(f"✅ 因子计算: {len(df_with_factors.columns)} 列")
        
        # 模拟引擎
        class MockEngine:
            def __init__(self):
                self.current_dt = None
            def get_position_size(self, symbol):
                return 0.0
            def get_portfolio_value(self, data):
                return {'total': 100000.0}
            @property
            def risk_manager(self):
                return MockRiskManager()
        
        class MockRiskManager:
            def calculate_trade_size(self, portfolio_value, price, risk_pct, stop_loss_price):
                if price <= 0 or stop_loss_price <= 0:
                    return 0.0
                risk_amount = portfolio_value * risk_pct
                price_diff = abs(price - stop_loss_price)
                if price_diff <= 0:
                    return 0.0
                size = risk_amount / price_diff
                return min(size, portfolio_value * 0.1 / price)
        
        mock_engine = MockEngine()
        
        # 测试AlphaXInspiredStrategy
        strategy_name = 'AlphaXInspiredStrategy'
        strategy_params = {'risk_per_trade_pct': 0.01, 'atr_sl_multiple': 2.0, 'atr_tp_multiple': 3.0}
        
        strategy_class = STRATEGIES[strategy_name]
        strategy = strategy_class(mock_engine, ['BTCUSDT'], strategy_params)
        print(f"✅ 策略初始化: {strategy_name}")
        
        # 寻找满足条件的时间点
        print(f"\n寻找满足交易条件的时间点...")
        
        signal_count = 0
        total_checked = 0
        
        # 从第100行开始检查，确保有足够的历史数据计算技术指标
        for i in range(100, len(df_with_factors), 100):  # 每100行检查一次，加快速度
            current_data = df_with_factors.iloc[i]
            mock_engine.current_dt = current_data.name
            
            # 检查关键条件
            price = current_data['CLOSE']
            sma_20 = current_data['SMA_20']
            sma_60 = current_data['SMA_60']
            rsi = current_data['RSI_14']
            adx = current_data['ADX_14']
            
            total_checked += 1
            
            # AlphaXInspiredStrategy的关键条件
            uptrend = sma_20 > sma_60  # 短期均线在长期均线上方
            price_above_sma = price > sma_20  # 价格在短期均线上方
            rsi_ok = rsi < 70  # RSI不超买
            strong_trend = adx > 25  # 强趋势
            
            if uptrend and price_above_sma and rsi_ok and strong_trend:
                print(f"\n✅ 找到满足条件的时间点: {current_data.name}")
                print(f"   价格: {price:.2f}")
                print(f"   SMA_20: {sma_20:.2f} > SMA_60: {sma_60:.2f} ✅")
                print(f"   价格 > SMA_20: {price:.2f} > {sma_20:.2f} ✅")
                print(f"   RSI: {rsi:.2f} < 70 ✅")
                print(f"   ADX: {adx:.2f} > 25 ✅")
                
                # 准备数据格式进行信号测试
                # 策略期望的格式：行是symbol，列是因子
                test_data = pd.DataFrame([current_data], index=['BTCUSDT'])
                
                try:
                    signals = strategy.on_bar(test_data)
                    if signals and len(signals) > 0:
                        signal_count += len(signals)
                        print(f"   🎯 生成 {len(signals)} 个交易信号:")
                        for j, signal in enumerate(signals):
                            print(f"     信号 {j+1}: {signal}")
                    else:
                        print(f"   ⚠️ 条件满足但未生成信号")
                        
                        # 深入分析为什么没有信号
                        if hasattr(strategy, '_is_strong_uptrend'):
                            uptrend_check = strategy._is_strong_uptrend(current_data)
                            print(f"     策略内部上升趋势检查: {uptrend_check}")
                        
                        if hasattr(strategy, '_is_buy_trigger'):
                            buy_trigger = strategy._is_buy_trigger(current_data)
                            print(f"     策略内部买入触发检查: {buy_trigger}")
                
                except Exception as e:
                    print(f"   ❌ 信号生成出错: {e}")
                
                # 只检查前几个满足条件的点
                if signal_count > 0 or total_checked > 20:
                    break
        
        print(f"\n检查结果:")
        print(f"  总共检查: {total_checked} 个时间点")
        print(f"  生成信号: {signal_count} 个")
        
        if signal_count == 0:
            print(f"\n⚠️ 未生成交易信号，可能的原因:")
            print(f"  1. 策略参数过于保守")
            print(f"  2. 市场条件不满足策略要求")
            print(f"  3. 策略内部还有其他条件未满足")
            
            # 尝试调整策略参数
            print(f"\n尝试调整策略参数...")
            relaxed_params = {
                'risk_per_trade_pct': 0.02,  # 增加风险比例
                'atr_sl_multiple': 1.5,      # 减小止损倍数
                'atr_tp_multiple': 2.0       # 减小止盈倍数
            }
            
            relaxed_strategy = strategy_class(mock_engine, ['BTCUSDT'], relaxed_params)
            print(f"  调整后参数: {relaxed_params}")
            
            # 重新测试最后一个满足基本条件的点
            for i in range(len(df_with_factors)-1, 100, -1):
                current_data = df_with_factors.iloc[i]
                price = current_data['CLOSE']
                sma_20 = current_data['SMA_20']
                sma_60 = current_data['SMA_60']
                
                if sma_20 > sma_60 and price > sma_20:  # 基本上升趋势
                    print(f"  测试时间点: {current_data.name}")
                    test_data = pd.DataFrame([current_data], index=['BTCUSDT'])
                    
                    try:
                        signals = relaxed_strategy.on_bar(test_data)
                        if signals and len(signals) > 0:
                            print(f"  ✅ 调整参数后生成 {len(signals)} 个信号!")
                            for signal in signals:
                                print(f"    {signal}")
                            break
                    except Exception as e:
                        print(f"  ❌ 调整参数后仍出错: {e}")
                    
                    break
        
        return signal_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_signal_generation_fixed()
    print(f"\n{'='*60}")
    print(f"测试结果: {'✅ 成功' if success else '❌ 需要进一步调试'}")
    print(f"{'='*60}")
