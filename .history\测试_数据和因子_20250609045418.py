#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据加载和因子计算是否正常
"""

import pandas as pd
import numpy as np
from 核心代码.市场数据.数据获取器 import MarketData
from 核心代码.因子计算.因子库 import calculate_factors
from 配置.系统配置 import Config

def test_data_and_factors():
    """测试数据加载和因子计算"""
    print("="*60)
    print("测试数据加载和因子计算")
    print("="*60)
    
    # 1. 初始化配置和数据管理器
    config = Config()
    market_data = MarketData(config)
    
    # 2. 加载BTCUSDT数据
    print("\n1. 加载BTCUSDT数据...")
    symbol = "BTCUSDT"
    start_date = "2025-04-01"
    end_date = "2025-04-29"
    
    df = market_data.get_market_data(symbol, start_date, end_date, source='local')
    
    if df is None or df.empty:
        print("❌ 数据加载失败！")
        return False
    
    print(f"✅ 数据加载成功: {len(df)} 条记录")
    print(f"   时间范围: {df.index[0]} 到 {df.index[-1]}")
    print(f"   列名: {list(df.columns)}")
    print(f"   数据样本:")
    print(df.head())
    
    # 3. 测试因子计算
    print("\n2. 测试因子计算...")
    
    # 使用最近1000条数据进行测试（避免计算时间过长）
    test_df = df.tail(1000).copy()
    print(f"   使用最近 {len(test_df)} 条数据进行因子计算")
    
    try:
        factors_df = calculate_factors(test_df, config.factor_config)
        
        if factors_df is None or factors_df.empty:
            print("❌ 因子计算失败！")
            return False
        
        print(f"✅ 因子计算成功: {len(factors_df)} 条记录")
        print(f"   因子列数: {len(factors_df.columns)}")
        print(f"   因子列名: {list(factors_df.columns)}")
        
        # 检查关键因子是否存在
        key_factors = ['SMA_20', 'SMA_60', 'RSI_14', 'ATR_14']
        missing_factors = [f for f in key_factors if f not in factors_df.columns]
        
        if missing_factors:
            print(f"⚠️  缺失关键因子: {missing_factors}")
        else:
            print("✅ 所有关键因子都存在")
        
        # 显示因子统计信息
        print(f"\n   因子统计信息:")
        for col in factors_df.columns[:10]:  # 只显示前10个因子
            non_null_count = factors_df[col].count()
            print(f"     {col}: {non_null_count}/{len(factors_df)} 非空值")
        
        # 显示最后几行数据
        print(f"\n   最新因子数据:")
        print(factors_df.tail())
        
        return True
        
    except Exception as e:
        print(f"❌ 因子计算出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_signals():
    """测试策略信号生成"""
    print("\n" + "="*60)
    print("测试策略信号生成")
    print("="*60)
    
    from 核心代码.交易策略.策略库 import STRATEGIES
    
    # 1. 准备测试数据
    config = Config()
    market_data = MarketData(config)
    
    symbol = "BTCUSDT"
    start_date = "2025-04-01"
    end_date = "2025-04-29"
    
    df = market_data.get_market_data(symbol, start_date, end_date, source='local')
    if df is None or df.empty:
        print("❌ 无法获取测试数据")
        return False
    
    # 使用最近1000条数据
    test_df = df.tail(1000).copy()
    factors_df = calculate_factors(test_df, config.factor_config)
    
    if factors_df is None or factors_df.empty:
        print("❌ 无法计算因子")
        return False
    
    # 合并价格和因子数据
    combined_df = pd.concat([test_df, factors_df], axis=1)
    
    print(f"✅ 准备测试数据: {len(combined_df)} 条记录")
    
    # 2. 测试每个策略
    for strategy_config in config.strategies:
        strategy_name = strategy_config['name']
        strategy_params = strategy_config['params']
        
        print(f"\n测试策略: {strategy_name}")
        print(f"参数: {strategy_params}")
        
        if strategy_name not in STRATEGIES:
            print(f"❌ 策略 {strategy_name} 不存在")
            continue
        
        try:
            # 初始化策略
            strategy_class = STRATEGIES[strategy_name]
            strategy = strategy_class(**strategy_params)
            
            # 生成信号
            signals = []
            signal_count = 0
            
            # 逐行测试信号生成（模拟实时数据）
            for i in range(100, len(combined_df)):  # 从第100行开始，确保有足够的历史数据
                current_data = combined_df.iloc[:i+1]
                latest_data = current_data.iloc[-1]
                
                signal = strategy.generate_signal(symbol, latest_data, current_data)
                
                if signal and signal.action != 'HOLD':
                    signals.append({
                        'timestamp': latest_data.name,
                        'action': signal.action,
                        'quantity': signal.quantity,
                        'price': latest_data['Close'],
                        'reason': getattr(signal, 'reason', 'N/A')
                    })
                    signal_count += 1
                    
                    if signal_count <= 5:  # 只显示前5个信号
                        print(f"  📈 信号 {signal_count}: {signal.action} {signal.quantity} @ {latest_data['Close']:.2f}")
            
            print(f"✅ 策略 {strategy_name} 测试完成: 生成 {signal_count} 个交易信号")
            
            if signal_count == 0:
                print(f"⚠️  策略 {strategy_name} 未生成任何交易信号")
                # 检查策略参数和数据
                print(f"   检查最新数据:")
                latest = combined_df.iloc[-1]
                print(f"     价格: {latest['Close']:.2f}")
                if 'RSI_14' in combined_df.columns:
                    print(f"     RSI_14: {latest.get('RSI_14', 'N/A')}")
                if 'SMA_20' in combined_df.columns:
                    print(f"     SMA_20: {latest.get('SMA_20', 'N/A')}")
                if 'SMA_60' in combined_df.columns:
                    print(f"     SMA_60: {latest.get('SMA_60', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 策略 {strategy_name} 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    return True

if __name__ == "__main__":
    print("开始系统诊断测试...")
    
    # 测试数据和因子
    success1 = test_data_and_factors()
    
    # 测试策略信号
    success2 = test_strategy_signals()
    
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"数据和因子测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"策略信号测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  存在问题需要修复")
