# -*- coding: utf-8 -*-
"""
数据检查和策略测试
1. 检查可用的BTCUSDT数据
2. 运行所有策略的对比测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from datetime import datetime
import logging
import glob

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s]: %(message)s'
)

logger = logging.getLogger(__name__)

def check_available_data():
    """检查可用的BTCUSDT数据"""
    
    print("📊 检查可用的BTCUSDT数据")
    print("=" * 60)
    
    data_dir = "数据/BTCUSDT"
    
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return None
    
    # 查找所有CSV文件
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
    
    if not csv_files:
        print(f"❌ 在 {data_dir} 中未找到CSV文件")
        return None
    
    print(f"✅ 找到 {len(csv_files)} 个数据文件:")
    
    data_summary = []
    
    for csv_file in sorted(csv_files):
        filename = os.path.basename(csv_file)
        print(f"\n📁 文件: {filename}")
        
        try:
            # 读取文件的前几行和后几行来确定数据范围
            df = pd.read_csv(csv_file)
            
            if len(df) == 0:
                print("  ⚠️ 文件为空")
                continue
            
            # 假设第一列是时间戳
            if 'timestamp' in df.columns:
                time_col = 'timestamp'
            elif len(df.columns) > 0:
                time_col = df.columns[0]
            else:
                print("  ❌ 无法识别时间列")
                continue
            
            # 转换时间戳
            if df[time_col].dtype == 'int64':
                # 毫秒时间戳
                df['datetime'] = pd.to_datetime(df[time_col], unit='ms')
            else:
                df['datetime'] = pd.to_datetime(df[time_col])
            
            start_time = df['datetime'].min()
            end_time = df['datetime'].max()
            total_records = len(df)
            
            print(f"  📅 时间范围: {start_time} 到 {end_time}")
            print(f"  📊 记录数量: {total_records:,}")
            print(f"  📈 数据列: {list(df.columns)}")
            
            # 检查数据质量
            missing_values = df.isnull().sum().sum()
            if missing_values > 0:
                print(f"  ⚠️ 缺失值: {missing_values}")
            else:
                print(f"  ✅ 数据完整")
            
            data_summary.append({
                'file': filename,
                'start_time': start_time,
                'end_time': end_time,
                'records': total_records,
                'missing_values': missing_values
            })
            
        except Exception as e:
            print(f"  ❌ 读取文件出错: {e}")
    
    if data_summary:
        print(f"\n📋 数据汇总:")
        print("-" * 60)
        overall_start = min(item['start_time'] for item in data_summary)
        overall_end = max(item['end_time'] for item in data_summary)
        total_records = sum(item['records'] for item in data_summary)
        
        print(f"总时间范围: {overall_start} 到 {overall_end}")
        print(f"总记录数: {total_records:,}")
        
        # 推荐测试时间段
        print(f"\n💡 推荐测试时间段:")
        for item in data_summary:
            if item['records'] > 10000:  # 足够的数据量
                month_start = item['start_time'].strftime('%Y-%m-01')
                month_end = item['end_time'].strftime('%Y-%m-%d')
                print(f"  • {month_start} 到 {month_end} ({item['records']:,} 条记录)")
    
    return data_summary

def get_strategy_list():
    """获取可用策略列表"""
    
    print("\n🔧 检查可用策略")
    print("=" * 60)
    
    try:
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        available_strategies = []
        unavailable_strategies = []
        
        for name, strategy_class in STRATEGIES.items():
            if strategy_class is not None:
                available_strategies.append(name)
                print(f"✅ {name}")
            else:
                unavailable_strategies.append(name)
                print(f"❌ {name} (未加载)")
        
        print(f"\n📊 策略统计:")
        print(f"可用策略: {len(available_strategies)}")
        print(f"不可用策略: {len(unavailable_strategies)}")
        
        return available_strategies
        
    except Exception as e:
        print(f"❌ 获取策略列表失败: {e}")
        return []

def run_quick_strategy_test():
    """运行快速策略测试"""
    
    print("\n🚀 运行快速策略测试")
    print("=" * 60)
    
    try:
        # 导入回测引擎和相关类
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 核心代码.交易策略.策略库 import STRATEGIES
        from 配置.系统配置 import Config

        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-05'
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 获取策略类
        strategy_name = 'AlphaXInspiredStrategy'
        strategy_class = STRATEGIES.get(strategy_name)

        if not strategy_class:
            print(f"❌ 策略 {strategy_name} 不可用")
            return False

        # 策略参数
        strategy_params = {
            'adx_threshold': 25.0,
            'rsi_oversold': 35.0,
            'risk_per_trade_pct': 0.01
        }

        print(f"测试策略: {strategy_name}")
        print(f"测试时间: {config.start_date} 到 {config.end_date}")

        # 创建回测引擎
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)

        # 运行回测
        results = backtester.run_backtest(config.start_date, config.end_date)
        
        if results:
            print("✅ 快速测试成功！")
            print(f"最终价值: {results.get('final_portfolio_value', 0):,.2f}")
            print(f"交易次数: {results.get('total_trades', 0)}")
            return True
        else:
            print("❌ 快速测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 快速测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    print("🎯 数据检查和策略测试工具")
    print("=" * 80)
    
    # 1. 检查数据
    data_summary = check_available_data()
    
    if not data_summary:
        print("\n❌ 没有可用数据，无法继续测试")
        return
    
    # 2. 检查策略
    strategies = get_strategy_list()
    
    if not strategies:
        print("\n❌ 没有可用策略，无法继续测试")
        return
    
    # 3. 运行快速测试
    test_success = run_quick_strategy_test()
    
    if not test_success:
        print("\n❌ 快速测试失败，请检查系统配置")
        return
    
    # 4. 询问是否运行完整测试
    print("\n" + "=" * 80)
    print("🎉 系统检查完成！")
    print(f"✅ 数据文件: {len(data_summary)} 个")
    print(f"✅ 可用策略: {len(strategies)} 个")
    print(f"✅ 系统测试: 通过")
    
    print("\n💡 建议的下一步操作:")
    print("1. 运行完整的策略对比测试")
    print("2. 选择特定策略进行详细分析")
    print("3. 使用前端界面进行交互式测试")
    
    # 显示可用的测试时间段
    print("\n📅 推荐的测试时间段:")
    for item in data_summary:
        if item['records'] > 20000:  # 足够的数据量
            print(f"  • {item['start_time'].strftime('%Y-%m-%d')} 到 {item['end_time'].strftime('%Y-%m-%d')}")
    
    print("\n🚀 要运行完整的策略对比测试，请执行:")
    print("python 策略全面对比测试_2024年4月.py")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
