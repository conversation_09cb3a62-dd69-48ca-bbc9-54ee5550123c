#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速诊断交易信号问题
"""

import pandas as pd
import numpy as np

def quick_test():
    print("快速诊断开始...")
    
    # 1. 测试数据加载
    try:
        from 配置.系统配置 import Config
        from 核心代码.市场数据.数据获取器 import MarketData
        
        config = Config()
        market_data = MarketData(config)
        
        # 只加载1天数据
        df = market_data.get_market_data("BTCUSDT", "2025-04-10", "2025-04-10", source='local')
        print(f"✅ 数据加载: {len(df)} 条记录")
        
        # 2. 测试因子计算
        from 核心代码.因子计算.因子库 import calculate_factors
        factors_df = calculate_factors(df, config.factor_config)
        print(f"✅ 因子计算: {len(factors_df.columns)} 个因子")
        
        # 合并数据
        combined_df = pd.concat([df, factors_df], axis=1)
        latest_data = combined_df.iloc[-1]

        print(f"\n数据结构检查:")
        print(f"  df columns: {list(df.columns)}")
        print(f"  factors_df columns: {list(factors_df.columns)}")
        print(f"  combined_df shape: {combined_df.shape}")
        print(f"  combined_df columns: {list(combined_df.columns)}")

        # 检查重复列
        duplicate_cols = combined_df.columns[combined_df.columns.duplicated()].tolist()
        if duplicate_cols:
            print(f"  ⚠️ 重复列: {duplicate_cols}")

        # 使用iloc访问避免重复列问题
        close_val = combined_df.iloc[-1, combined_df.columns.get_loc('CLOSE')]
        if isinstance(close_val, pd.Series):
            close_val = close_val.iloc[0]  # 取第一个值
        print(f"  close_val: {close_val} (type: {type(close_val)})")

        print(f"\n最新数据 ({latest_data.name}):")
        print(f"  价格: {close_val}")
        if 'SMA_20' in latest_data.index:
            print(f"  SMA_20: {latest_data['SMA_20']}")
        if 'SMA_60' in latest_data.index:
            print(f"  SMA_60: {latest_data['SMA_60']}")
        if 'RSI_14' in latest_data.index:
            print(f"  RSI_14: {latest_data['RSI_14']}")
        if 'ADX_14' in latest_data.index:
            print(f"  ADX_14: {latest_data['ADX_14']}")
        
        # 3. 测试策略初始化
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        strategy_name = 'AlphaXInspiredStrategy'
        strategy_params = {'risk_per_trade_pct': 0.01, 'atr_sl_multiple': 2.0, 'atr_tp_multiple': 3.0}
        
        strategy_class = STRATEGIES[strategy_name]
        strategy = strategy_class(None, ['BTCUSDT'], strategy_params)
        print(f"✅ 策略初始化成功: {strategy_name}")
        
        # 4. 检查策略条件
        print(f"\n策略条件检查:")
        
        # 检查趋势条件
        sma_20 = latest_data['SMA_20']
        sma_60 = latest_data['SMA_60']
        price = latest_data['CLOSE']
        rsi = latest_data['RSI_14']
        adx = latest_data['ADX_14']
        
        print(f"  SMA_20 > SMA_60: {sma_20 > sma_60} ({sma_20:.2f} vs {sma_60:.2f})")
        print(f"  价格 > SMA_20: {price > sma_20} ({price:.2f} vs {sma_20:.2f})")
        print(f"  RSI < 70: {rsi < 70} ({rsi:.2f})")
        print(f"  ADX > 25: {adx > 25} ({adx:.2f})")
        
        # 检查策略的具体方法
        if hasattr(strategy, '_is_strong_uptrend'):
            uptrend = strategy._is_strong_uptrend(latest_data)
            print(f"  强上升趋势: {uptrend}")
        
        if hasattr(strategy, '_is_buy_trigger'):
            buy_trigger = strategy._is_buy_trigger(latest_data)
            print(f"  买入触发: {buy_trigger}")
        
        # 5. 尝试生成信号
        print(f"\n尝试生成信号...")
        
        # 模拟引擎
        class MockEngine:
            def __init__(self):
                self.current_dt = latest_data.name
            def get_position_size(self, symbol):
                return 0.0
            def get_portfolio_value(self, data):
                return {'total': 100000.0}
            @property
            def risk_manager(self):
                return MockRiskManager()
        
        class MockRiskManager:
            def calculate_trade_size(self, portfolio_value, price, risk_pct, stop_loss_price):
                return 1.0  # 简单返回1个单位
        
        mock_engine = MockEngine()
        strategy.engine = mock_engine
        
        # 准备数据格式
        test_data = pd.DataFrame([latest_data]).T
        test_data.index = ['BTCUSDT']
        
        try:
            signals = strategy.on_bar(test_data)
            if signals and len(signals) > 0:
                print(f"✅ 生成 {len(signals)} 个信号:")
                for signal in signals:
                    print(f"  {signal}")
            else:
                print("⚠️ 未生成信号")
        except Exception as e:
            print(f"❌ 信号生成失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()
