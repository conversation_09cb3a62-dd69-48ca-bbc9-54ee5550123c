# -*- coding: utf-8 -*-
"""
P04实用盈利策略
基于现实市场条件，平衡风险与收益，追求稳定盈利
目标：月度盈利率>0, 年化收益>10%, 最大回撤<20%
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class PracticalProfitStrategy:
    """P04实用盈利策略 - 追求现实可行的稳定盈利"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 实用盈利策略参数
        self.strategy_params = {
            # 技术指标参数
            'ma_short': 10,                # 短期均线
            'ma_long': 30,                 # 长期均线
            'rsi_period': 14,              # RSI周期
            'rsi_oversold': 35,            # RSI超卖
            'rsi_overbought': 65,          # RSI超买
            'bb_period': 20,               # 布林带周期
            'bb_std': 2.0,                 # 布林带标准差
            
            # 仓位管理
            'base_position': 0.2,          # 基础仓位20%
            'max_position': 0.5,           # 最大仓位50%
            'position_increment': 0.1,     # 仓位递增10%
            
            # 风险控制
            'stop_loss': 0.025,            # 止损2.5%
            'take_profit': 0.05,           # 止盈5% (2:1盈亏比)
            'trailing_stop': True,         # 启用移动止损
            'max_daily_loss': 0.02,        # 日最大亏损2%
            
            # 交易控制
            'min_signal_gap': 60,          # 最小信号间隔60分钟
            'max_holding_hours': 24,       # 最大持仓24小时
            'volume_threshold': 0.8,       # 成交量阈值
            
            # 市场状态适应
            'trend_sensitivity': 0.8,      # 趋势敏感度
            'volatility_adjustment': True, # 波动率调整
            'market_hours_only': False,    # 仅交易时间
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()
            
            # 移动平均线
            df['MA_Short'] = df['CLOSE'].rolling(self.strategy_params['ma_short']).mean()
            df['MA_Long'] = df['CLOSE'].rolling(self.strategy_params['ma_long']).mean()
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 布林带
            df['BB_Middle'] = df['CLOSE'].rolling(self.strategy_params['bb_period']).mean()
            bb_std = df['CLOSE'].rolling(self.strategy_params['bb_period']).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * self.strategy_params['bb_std'])
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * self.strategy_params['bb_std'])
            df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
            
            # 价格位置
            df['Price_Position'] = (df['CLOSE'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
            
            # 动量指标
            df['Momentum'] = df['CLOSE'].pct_change(10)
            df['Price_Change'] = df['CLOSE'].pct_change()
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            # 趋势强度
            df['Trend_Strength'] = abs(df['MA_Short'] - df['MA_Long']) / df['CLOSE']
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        try:
            # 初始化信号
            df['Buy_Signal'] = 0
            df['Sell_Signal'] = 0
            df['Signal_Strength'] = 0
            
            # 买入信号条件
            buy_conditions = [
                # 趋势条件
                df['MA_Short'] > df['MA_Long'],  # 短期均线在长期均线上方
                df['CLOSE'] > df['MA_Short'],    # 价格在短期均线上方
                
                # RSI条件
                (df['RSI'] > self.strategy_params['rsi_oversold']) & 
                (df['RSI'] < self.strategy_params['rsi_overbought']),
                
                # 布林带条件
                df['Price_Position'] > 0.2,     # 不在布林带下轨附近
                df['Price_Position'] < 0.8,     # 不在布林带上轨附近
                
                # 动量条件
                df['Momentum'] > 0.002,          # 正动量
                df['Price_Change'] > 0,          # 价格上涨
                
                # 成交量条件
                df['Volume_Ratio'] > self.strategy_params['volume_threshold'],
                
                # 波动率条件
                df['Volatility'] > 0.005,        # 最小波动率
                df['Volatility'] < 0.1,          # 最大波动率
            ]
            
            # 组合买入信号
            buy_signal = pd.Series(True, index=df.index)
            for condition in buy_conditions:
                buy_signal = buy_signal & condition
            
            df.loc[buy_signal, 'Buy_Signal'] = 1
            
            # 计算信号强度
            df['Signal_Strength'] = (
                (df['Trend_Strength'] * 100) * 0.3 +
                (abs(df['RSI'] - 50) / 50) * 0.2 +
                (df['Momentum'] * 100) * 0.2 +
                (df['Volume_Ratio'] - 1) * 0.2 +
                (df['BB_Width'] * 100) * 0.1
            )
            
            # 卖出信号条件
            sell_conditions = [
                # 趋势转向
                df['MA_Short'] < df['MA_Long'],
                
                # RSI超买
                df['RSI'] > self.strategy_params['rsi_overbought'],
                
                # 价格接近布林带上轨
                df['Price_Position'] > 0.9,
                
                # 负动量
                df['Momentum'] < -0.002,
            ]
            
            # 任一卖出条件满足即卖出
            sell_signal = pd.Series(False, index=df.index)
            for condition in sell_conditions:
                sell_signal = sell_signal | condition
            
            df.loc[sell_signal, 'Sell_Signal'] = 1
            
            return df
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return df
    
    def simulate_practical_strategy(self, data: pd.DataFrame) -> dict:
        """模拟实用盈利策略"""
        try:
            print("🔄 模拟P04实用盈利策略...")
            
            # 计算技术指标
            data = self.calculate_indicators(data)
            data = self.generate_signals(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            # 交易控制变量
            last_trade_time = None
            entry_time = None
            entry_price = 0
            stop_loss_price = 0
            take_profit_price = 0
            trailing_stop_price = 0
            daily_pnl = {}
            
            for i in range(50, len(data)):  # 从第50个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']
                
                # 更新权益曲线
                current_equity = cash + position * current_price
                equity_curve.append(current_equity)
                
                # 检查数据有效性
                if pd.isna(current_price):
                    continue
                
                # 检查信号间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_signal_gap']:
                        continue
                
                # 无持仓时检查入场
                if position == 0:
                    buy_signal = current_data.get('Buy_Signal', 0)
                    signal_strength = current_data.get('Signal_Strength', 0)
                    
                    if buy_signal == 1 and signal_strength > 0.5:
                        # 根据信号强度确定仓位
                        if signal_strength > 2.0:
                            position_size = self.strategy_params['max_position']
                        elif signal_strength > 1.5:
                            position_size = self.strategy_params['base_position'] + self.strategy_params['position_increment']
                        else:
                            position_size = self.strategy_params['base_position']
                        
                        # 波动率调整
                        if self.strategy_params['volatility_adjustment']:
                            volatility = current_data.get('Volatility', 0.02)
                            if volatility > 0.05:
                                position_size *= 0.8  # 高波动率时减少仓位
                            elif volatility < 0.01:
                                position_size *= 1.2  # 低波动率时增加仓位
                        
                        position_size = min(position_size, self.strategy_params['max_position'])
                        
                        # 计算股数
                        position_value = cash * position_size
                        shares = position_value / current_price
                        
                        if shares > 0:
                            position = shares
                            cash -= shares * current_price * 1.0005  # 手续费
                            entry_time = current_time
                            entry_price = current_price
                            
                            # 设置止损止盈
                            stop_loss_price = current_price * (1 - self.strategy_params['stop_loss'])
                            take_profit_price = current_price * (1 + self.strategy_params['take_profit'])
                            trailing_stop_price = stop_loss_price
                            
                            trades.append({
                                'time': current_time,
                                'action': 'buy',
                                'price': current_price,
                                'shares': shares,
                                'signal_strength': signal_strength,
                                'position_size': position_size,
                                'stop_loss': stop_loss_price,
                                'take_profit': take_profit_price
                            })
                            
                            last_trade_time = current_time
                
                # 有持仓时检查出场
                elif position > 0:
                    sell_signal = current_data.get('Sell_Signal', 0)
                    
                    # 更新移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price > entry_price * 1.02:  # 盈利2%以上时启动移动止损
                            new_trailing_stop = current_price * (1 - self.strategy_params['stop_loss'])
                            trailing_stop_price = max(trailing_stop_price, new_trailing_stop)
                    
                    # 计算持仓时间
                    holding_hours = (current_time - entry_time).total_seconds() / 3600
                    
                    # 出场条件
                    should_exit = False
                    exit_reason = ""
                    
                    # 止损
                    if current_price <= trailing_stop_price:
                        should_exit = True
                        exit_reason = "移动止损" if trailing_stop_price > stop_loss_price else "止损"
                    # 止盈
                    elif current_price >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                    # 信号转向
                    elif sell_signal == 1:
                        should_exit = True
                        exit_reason = "信号转向"
                    # 超时
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "超时平仓"
                    # 趋势减弱
                    elif (holding_hours > 2 and 
                          current_data.get('Trend_Strength', 0) < 0.005):
                        should_exit = True
                        exit_reason = "趋势减弱"
                    
                    if should_exit:
                        cash += position * current_price * 0.9995  # 扣除手续费
                        
                        pnl = (current_price - entry_price) * position
                        pnl_pct = pnl / (entry_price * position) * 100
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': exit_reason,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'holding_hours': holding_hours
                        })
                        
                        position = 0
                        entry_time = None
                        last_trade_time = current_time
            
            # 期末平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                pnl = (final_price - entry_price) * position
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': pnl
                })
                position = 0
            
            final_equity = cash
            
            # 计算统计指标
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ P04实用盈利策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"P04实用盈利策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P04实用盈利策略测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    strategy = PracticalProfitStrategy()
    
    print("\n💡 P04实用盈利策略特点:")
    print("=" * 60)
    print("  1. 多指标组合确认")
    print("  2. 动态仓位管理")
    print("  3. 移动止损保护")
    print("  4. 波动率自适应")
    print("  5. 2:1盈亏比设计")
    print("=" * 60)
    
    # 测试2024年4月
    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_practical_strategy(data)
        
        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1
            
            print(f"\n📊 P04实用盈利策略测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"交易次数: {result['total_trades']}")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            
            # 实用目标检查
            print(f"\n🎯 实用目标达成检查:")
            print("-" * 40)
            profit_ok = result['total_return'] > 0
            annual_ok = annual_return >= 0.10  # 10%年化收益
            drawdown_ok = result['max_drawdown'] <= 0.20  # 20%最大回撤
            trades_ok = result['total_trades'] >= 5  # 至少5次交易
            
            print(f"月度盈利: {result['total_return']*100:+.2f}% {'✅' if profit_ok else '❌'} (目标: >0%)")
            print(f"年化收益率: {annual_return*100:+.2f}% {'✅' if annual_ok else '❌'} (目标: ≥10%)")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}% {'✅' if drawdown_ok else '❌'} (目标: ≤20%)")
            print(f"交易频率: {result['total_trades']}次 {'✅' if trades_ok else '❌'} (目标: ≥5次)")
            
            targets_met = sum([profit_ok, annual_ok, drawdown_ok, trades_ok])
            
            print(f"\n🏆 P04实用盈利策略评价:")
            if targets_met >= 3:
                print("🎉 实用可行! 策略表现符合实际预期!")
            elif targets_met == 2:
                print("✅ 基本可用! 策略有一定实用价值!")
            elif targets_met == 1:
                print("⚠️ 需要改进! 策略仍有优化空间!")
            else:
                print("❌ 不够实用! 需要重新设计策略!")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
