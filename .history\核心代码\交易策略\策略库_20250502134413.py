# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

class TradingStrategy(BASE_STRATEGY):  # 确保继承自 backtesting.Strategy 或其替身
    """交易策略基类"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功


    # --- !! 关键：修正 __init__ 签名 !! ---
    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 调用 backtesting.Strategy 的父类 __init__
        #    必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        #    父类 __init__ 会处理将 params 设置到实例属性
        if backtesting_available and isinstance(self, BacktestStrategy):
             super().__init__(broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) 调用子类的 init() - backtesting 库会自动调用
        #    但如果我们在基类 __init__ 中做了很多事，确保不要阻止它。
        #    实际上，backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init()。


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass
    # ... 其他自定义方法 ...

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略"""
    # --- 定义策略特定的默认参数 (这些会被 __init__ 读取) ---
    window = 20
    threshold = 2.0
    rsi_window = 14
    rsi_overbought = 70
    rsi_oversold = 30
    atr_window = 14 # 如果需要ATR
    trade_frequency = '2W'  # 交易频率
    position_size = 1.0  # 仓位大小
    stop_loss = 0.05  # 止损比例
    take_profit = 0.1  # 止盈比例
    rsi_factor_name = 'RSI'  # RSI 因子名称
    bbands_factor_prefix = 'BB'  # 布林带因子前缀
    macd_line_factor_name = 'MACD_Line'  # MACD 线因子名称
    macd_signal_factor_name = 'Signal_Line'  # MACD 信号线因子名称
    atr_factor_name = None  # ATR 因子名称
    # (从基类继承 stop_loss_pct, take_profit_pct)

    # --- !! 不需要重新定义 __init__ !! ---
    # 继承自 TradingStrategy 的 __init__ 已经处理了参数合并和调用父类
    # 如果你需要特殊的初始化逻辑，可以覆盖 __init__，但 *必须* 调用 super().__init__(broker, data, params)

    # --- 策略核心逻辑在 init 和 next 中 ---
    def init(self):
        # 这个 init 由 backtesting 库自动调用
        logger.debug(f"Initializing {self.__class__.__name__} with final params: {self.parameters}")

        # --- 检查必需列，忽略大小写 ---
        required_factors = ['CLOSE', 'BB_LOWER', 'BB_UPPER', 'BB_MIDDLE', 'RSI', 'MACD_LINE', 'SIGNAL_LINE']
        # 如果策略参数指定了 ATR 因子名称，则也检查它
        self.atr_factor_name = self.parameters.get('atr_factor_name', None) # 从参数获取，可能为 None
        if self.atr_factor_name:
            required_factors.append(self.atr_factor_name)

        # 直接检查 self.data 的列名，忽略大小写
        available_cols = [col.upper() for col in self.data.df.columns.tolist()]
        missing_factors = [f for f in required_factors if f.upper() not in available_cols]

        if missing_factors:
             logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing_factors}")
             logger.error(f"可用列: {self.data.df.columns.tolist()}")
             self._ready = False
             return # 阻止策略运行
        else:
             logger.info(f"策略 '{self.__class__.__name__}' 初始化成功，所有必需因子/列存在。")
             self._ready = True

    def next(self):
        if not self._ready: return

        try:
            # --- 使用大写列名访问数据 ---
            price = self.data.CLOSE[-1]
            bb_low_val = self.data.BB_LOWER[-1]
            bb_up_val = self.data.BB_UPPER[-1]
            bb_mid_val = self.data.BB_MIDDLE[-1]
            rsi_val = self.data.RSI[-1]
            macd_line_val = self.data.MACD_LINE[-1]
            macd_signal_val = self.data.SIGNAL_LINE[-1]
            # 检查所有值是否有效 (非 NaN)
            if not all(pd.notna(v) for v in [price, bb_low_val, bb_up_val, bb_mid_val, rsi_val, macd_line_val, macd_signal_val]):
                return # 如果任何关键数据点是 NaN，则跳过此周期
        except (IndexError, AttributeError, KeyError) as e: # 保持错误捕获
             return

        # --- 获取参数 ---
        rsi_ob = self.parameters.get('rsi_overbought', 70)
        rsi_os = self.parameters.get('rsi_oversold', 30)
        sl_pct = self.parameters.get('stop_loss_pct', 0.03)
        tp_pct = self.parameters.get('take_profit_pct', 0.08)

        sl_buy = price * (1 - sl_pct)
        tp_buy = price * (1 + tp_pct)
        sl_sell = price * (1 + sl_pct)
        tp_sell = price * (1 - tp_pct)

        # 打印调试信息
        logger.debug(f"Bar {len(self.data)}: CLOSE={price:.2f}, BB LOWER={bb_low_val:.2f}, BB UPPER={bb_up_val:.2f}, RSI={rsi_val:.2f}")
        logger.debug(f"Bar {len(self.data)}: 买入条件={price > bb_up_val}, 卖出条件={price < bb_low_val}")
        logger.debug(f"Bar {len(self.data)}: 当前持仓: {self.position}")

        # --- 增强的交易逻辑 ---
        # 计算 MACD 交叉
        macd_crossover_up = macd_line_val > macd_signal_val and self.data.MACD_LINE[-2] <= self.data.SIGNAL_LINE[-2]
        macd_crossover_down = macd_line_val < macd_signal_val and self.data.MACD_LINE[-2] >= self.data.SIGNAL_LINE[-2]

        # 简化的交易逻辑用于测试
        buy_condition = price > bb_up_val  # 突破上轨做多
        sell_condition = price < bb_low_val  # 突破下轨做空

        # 执行交易并进行额外检查
        if buy_condition:
            if not self.position:
                logger.info(f"Bar {len(self.data)}: 发起买入")
                self.buy(size=1)
        elif sell_condition:
            if self.position:
                logger.info(f"Bar {len(self.data)}: 发起卖出")
                self.sell(size=abs(self.position))

class TrendFollowingStrategy(TradingStrategy):
    """趋势跟踪策略"""
    # --- 定义策略特定的默认参数 ---
    trend_window = 50  # 趋势判断窗口
    entry_threshold = 0.05  # 入场阈值
    exit_threshold = 0.02  # 出场阈值
    stop_loss_pct = 0.015  # 止损比例
    take_profit_pct = 0.03  # 止盈比例
    risk_per_trade = 0.02  # 每笔交易风险资金比例
    position_size = 1      # 默认仓位规模
    volatility_adjustment = True  # 启用波动率调整
    momentum_filter = True  # 启用动量过滤
    rsi_filter = True       # 启用RSI过滤

    def init(self):
        # 计算EMA
        def calculate_ema(prices, span):
            alpha = 2 / (span + 1)
            ema = np.zeros_like(prices)
            ema[0] = prices[0]
            for i in range(1, len(prices)):
                ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
            return ema

        self.ema_short = self.I(lambda x: calculate_ema(x, self.trend_window//2), self.data.Close)
        self.ema_long = self.I(lambda x: calculate_ema(x, self.trend_window), self.data.Close)
        
        # 计算ATR
        def calculate_atr(prices, window):
            atr = np.zeros_like(prices)
            for i in range(window, len(prices)):
                # 确保切片不会产生空数组
                price_window = prices[i-window:i]
                price_prev_window = prices[i-window-1:i-1]
                if len(price_window) == window and len(price_prev_window) == window:
                    atr[i] = np.mean(np.abs(price_window - price_prev_window))
            return atr

        self.atr = self.I(lambda x: calculate_atr(x, self.trend_window), self.data.Close)
        logger.info(f"ATR 计算完成，窗口大小={self.trend_window}")
        
        # 计算动量
        def calculate_momentum(prices, window):
            momentum = np.zeros_like(prices)
            for i in range(window, len(prices)):
                momentum[i] = (prices[i] - prices[i-window]) / prices[i-window]
            return momentum

        self.momentum = self.I(lambda x: calculate_momentum(x, self.trend_window), self.data.Close)
        logger.info(f"动量计算完成，窗口大小={self.trend_window}")
        
        logger.info(f"趋势跟踪策略初始化完成，趋势窗口={self.trend_window}")

    def next(self):
        if len(self.data) < self.trend_window:
            return

        # 获取当前价格和指标
        price = self.data.Close[-1]
        ema_short = self.ema_short[-1]
        ema_long = self.ema_long[-1]
        atr = self.atr[-1]
        momentum = self.momentum[-1]
        rsi = self.data.RSI[-1] if hasattr(self.data, 'RSI') else 50

        # 计算趋势强度和价格偏离
        trend_strength = (ema_short - ema_long) / ema_long
        deviation = (price - ema_long) / ema_long

        # 动态仓位管理
        account_value = self._broker.equity
        position_size = max(1, int((account_value * self.risk_per_trade) / (price * self.stop_loss_pct)))

        # 波动率调整因子
        volatility_factor = max(0.5, min(2.0, atr / price)) if self.volatility_adjustment else 1.0
        
        # 动量过滤
        momentum_condition = momentum > 0 if self.momentum_filter else True
        
        # RSI过滤
        rsi_condition = (rsi > 30 and rsi < 70) if self.rsi_filter else True

        # 趋势跟踪逻辑
        if not self.position:
            # 动态调整入场阈值
            dynamic_entry_threshold = self.entry_threshold * volatility_factor
            
            # 多头入场条件
            if deviation > dynamic_entry_threshold and trend_strength > 0 and momentum > 0 and rsi_condition:
                if not self.position.is_long:  # 确保没有多头仓位
                    self.buy(size=position_size)
                    logger.info(f"多头入场: 价格={price:.2f}, 偏离={deviation:.4f}, 趋势强度={trend_strength:.4f}, 动量={momentum:.4f}")
            
            # 空头入场条件
            elif deviation < -dynamic_entry_threshold and trend_strength < 0 and momentum < 0 and rsi_condition:
                if not self.position.is_short:  # 确保没有空头仓位
                    self.sell(size=position_size)
                    logger.info(f"空头入场: 价格={price:.2f}, 偏离={deviation:.4f}, 趋势强度={trend_strength:.4f}, 动量={momentum:.4f}")

        # 出场逻辑
        if self.position:
            dynamic_exit_threshold = self.exit_threshold * volatility_factor
            
            if self.position.is_long:
                # 多头出场条件
                if (deviation < -dynamic_exit_threshold or 
                    (price - self.position.price) / self.position.price <= -self.stop_loss_pct or
                    (price - self.position.price) / self.position.price >= self.take_profit_pct):
                    self.position.close()
                    logger.info(f"多头出场: 价格={price:.2f}, 偏离={deviation:.4f}, 盈亏={(price-self.position.price)/self.position.price:.2%}")
                    
            elif self.position.is_short:
                # 空头出场条件
                if (deviation > dynamic_exit_threshold or 
                    (self.position.price - price) / self.position.price <= -self.stop_loss_pct or
                    (self.position.price - price) / self.position.price >= self.take_profit_pct):
                    self.position.close()
                    logger.info(f"空头出场: 价格={price:.2f}, 偏离={deviation:.4f}, 盈亏={(self.position.price-price)/self.position.price:.2%}")

# 所有可用策略的列表
STRATEGIES = [MeanReversionStrategy, TrendFollowingStrategy]
