# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

class TradingStrategy(BASE_STRATEGY):  # 确保继承自 backtesting.Strategy 或其替身
    """交易策略基类"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功
    performance_stats = {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_pnl': 0.0,
        'max_win': 0.0,
        'max_loss': 0.0,
        'long_trades': 0,
        'short_trades': 0
    }

    def update_performance_stats(self, pnl: float, trade_type: str):
        """
        更新绩效统计数据
        
        Args:
            pnl: 盈亏比例
            trade_type: 交易类型 ('long' 或 'short')
        """
        self.performance_stats['total_trades'] += 1
        self.performance_stats['total_pnl'] += pnl
        
        if trade_type == 'long':
            self.performance_stats['long_trades'] += 1
        else:
            self.performance_stats['short_trades'] += 1
            
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            if pnl > self.performance_stats['max_win']:
                self.performance_stats['max_win'] = pnl
        else:
            self.performance_stats['losing_trades'] += 1
            if pnl < self.performance_stats['max_loss']:
                self.performance_stats['max_loss'] = pnl
                
        logger.info(f"更新绩效统计: 总交易数={self.performance_stats['total_trades']}, 总盈亏={self.performance_stats['total_pnl']:.2%}")

    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 调用 backtesting.Strategy 的父类 __init__
        #    必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        #    父类 __init__ 会处理将 params 设置到实例属性
        if backtesting_available and isinstance(self, BacktestStrategy):
             super().__init__(broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) 调用子类的 init() - backtesting 库会自动调用
        #    但如果我们在基类 __init__ 中做了很多事，确保不要阻止它。
        #    实际上，backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init().


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略"""
    # --- 定义策略特定的默认参数 (这些会被 __init__ 读取) ---
    window = 20
    threshold = 2.0
    rsi_window = 14
    rsi_overbought = 70
    rsi_oversold = 30
    atr_window = 14 # 如果需要ATR
    trade_frequency = '2W'  # 交易频率
    position_size = 1.0  # 仓位大小
    stop_loss = 0.05  # 止损比例
    take_profit = 0.1  # 止盈比例
    rsi_factor_name = 'RSI'  # RSI 因子名称
    bbands_factor_prefix = 'BB'  # 布林带因子前缀
    macd_line_factor_name = 'MACD_Line'  # MACD 线因子名称
    macd_signal_factor_name = 'Signal_Line'  # MACD 信号线因子名称
    atr_factor_name = None  # ATR 因子名称
    # (从基类继承 stop_loss_pct, take_profit_pct)

    # --- !! 不需要重新定义 __init__ !! ---
    # 继承自 TradingStrategy 的 __init__ 已经处理了参数合并和调用父类
    # 如果你需要特殊的初始化逻辑，可以覆盖 __init__，但 *必须* 调用 super().__init__(broker, data, params)

    # --- 策略核心逻辑在 init 和 next 中 ---
    def init(self):
        # 这个 init 由 backtesting 库自动调用
        logger.debug(f"Initializing {self.__class__.__name__} with final params: {self.parameters}")

        # --- 检查必需列，忽略大小写 ---
        required_factors = ['CLOSE', 'BB_LOWER', 'BB_UPPER', 'BB_MIDDLE', 'RSI', 'MACD_LINE', 'SIGNAL_LINE']
        # 如果策略参数指定了 ATR 因子名称，则也检查它
        self.atr_factor_name = self.parameters.get('atr_factor_name', None) # 从参数获取，可能为 None
        if self.atr_factor_name:
            required_factors.append(self.atr_factor_name)

        # 直接检查 self.data 的列名，忽略大小写
        available_cols = [col.upper() for col in self.data.df.columns.tolist()]
        missing_factors = [f for f in required_factors if f.upper() not in available_cols]

        if missing_factors:
             logger.error(f"策略 '{self.__class__.__name__}' 初始化失败，数据缺少必需列: {missing_factors}")
             logger.error(f"可用列: {self.data.df.columns.tolist()}")
             self._ready = False
             return # 阻止策略运行
        else:
             logger.info(f"策略 '{self.__class__.__name__}' 初始化成功，所有必需因子/列存在。")
             self._ready = True

    def next(self):
        if not self._ready: return

        try:
            # --- 使用大写列名访问数据 ---
            price = self.data.CLOSE[-1]
            bb_low_val = self.data.BB_LOWER[-1]
            bb_up_val = self.data.BB_UPPER[-1]
            bb_mid_val = self.data.BB_MIDDLE[-1]
            rsi_val = self.data.RSI[-1]
            macd_line_val = self.data.MACD_LINE[-1]
            macd_signal_val = self.data.SIGNAL_LINE[-1]
            # 检查所有值是否有效 (非 NaN)
            if not all(pd.notna(v) for v in [price, bb_low_val, bb_up_val, bb_mid_val, rsi_val, macd_line_val, macd_signal_val]):
                return # 如果任何关键数据点是 NaN，则跳过此周期
        except (IndexError, AttributeError, KeyError) as e: # 保持错误捕获
             return

        # --- 获取参数 ---
        rsi_ob = self.parameters.get('rsi_overbought', 70)
        rsi_os = self.parameters.get('rsi_oversold', 30)
        sl_pct = self.parameters.get('stop_loss_pct', 0.03)
        tp_pct = self.parameters.get('take_profit_pct', 0.1)

        # --- 交易逻辑 ---
        if price <= bb_low_val and rsi_val <= rsi_os and macd_line_val > macd_signal_val:
            # 买入信号：价格低于布林带下轨，RSI 超卖，MACD 金叉
            if not self.position:
                self.buy(size=self.position_size)
                logger.info(f"买入信号触发：价格={price:.2f}, RSI={rsi_val:.2f}, MACD={macd_line_val:.2f}")
        elif price >= bb_up_val and rsi_val >= rsi_ob and macd_line_val < macd_signal_val:
            # 卖出信号：价格高于布林带上轨，RSI 超买，MACD 死叉
            if self.position:
                self.sell()
                logger.info(f"卖出信号触发：价格={price:.2f}, RSI={rsi_val:.2f}, MACD={macd_line_val:.2f}")

        # --- 止损止盈逻辑 ---
        if self.position:
            if self.position.pl_pct <= -sl_pct:
                self.position.close()
                logger.info(f"止损触发：当前盈亏={self.position.pl_pct:.2%}")
            elif self.position.pl_pct >= tp_pct:
                self.position.close()
                logger.info(f"止盈触发：当前盈亏={self.position.pl_pct:.2%}")
# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

class TradingStrategy(BASE_STRATEGY):  # 确保继承自 backtesting.Strategy 或其替身
    """交易策略基类"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功
    performance_stats = {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_pnl': 0.0,
        'max_win': 0.0,
        'max_loss': 0.0,
        'long_trades': 0,
        'short_trades': 0
    }

    def update_performance_stats(self, pnl: float, trade_type: str):
        """
        更新绩效统计数据
        
        Args:
            pnl: 盈亏比例
            trade_type: 交易类型 ('long' 或 'short')
        """
        self.performance_stats['total_trades'] += 1
        self.performance_stats['total_pnl'] += pnl
        
        if trade_type == 'long':
            self.performance_stats['long_trades'] += 1
        else:
            self.performance_stats['short_trades'] += 1
            
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            if pnl > self.performance_stats['max_win']:
                self.performance_stats['max_win'] = pnl
        else:
            self.performance_stats['losing_trades'] += 1
            if pnl < self.performance_stats['max_loss']:
                self.performance_stats['max_loss'] = pnl
                
        logger.info(f"更新绩效统计: 总交易数={self.performance_stats['total_trades']}, 总盈亏={self.performance_stats['total_pnl']:.2%}")

    def __init__(self, broker, data, params: Optional[Dict] = None):
        """
        初始化策略。必须接收 broker, data, params。

        Args:
            broker: backtesting 提供的 Broker 对象。
            data: backtesting 提供的 Data Feed 对象。
            params (Optional[Dict]): 通过 bt.run(**params) 或 bt.optimize() 传递的参数。
        """
        # 1. 处理参数合并 (将类属性、传入的params合并到 self.parameters)
        self.parameters = {}
        cls = self.__class__
        # 获取所有非内部、非方法、基本类型的类属性作为默认参数基础
        potential_params = [p for p in dir(cls) if not p.startswith('_') and \
                            not callable(getattr(cls,p)) and \
                            isinstance(getattr(cls, p), (int, float, str, bool))]
        for param_name in potential_params:
             self.parameters[param_name] = getattr(cls, param_name)

        # 用传入的 params 覆盖默认值
        params_input = params if params is not None else {}
        self.parameters.update(params_input)

        # 2. 将最终参数设置回实例属性 (backtesting 的 I 方法等需要)
        for key, value in self.parameters.items():
             if hasattr(self, key): # 只设置类中已定义的属性
                 setattr(self, key, value)
             else: # Log a warning for unexpected parameters passed in
                 logger.debug(f"Parameter '{key}' from params dict not found as class attribute in {cls.__name__}")


        # 3. 调用 backtesting.Strategy 的父类 __init__
        #    必须传递 broker, data, 和 *原始传入的 params* (或空字典)
        #    父类 __init__ 会处理将 params 设置到实例属性
        if backtesting_available and isinstance(self, BacktestStrategy):
             super().__init__(broker, data, params_input) # 使用原始传入的 params

        # 4. 设置自定义属性
        self.set_transaction_cost(self.parameters.get('transaction_cost_pct', 0.001))
        # self._data 可以在需要时通过 set_data 设置，或尝试从 self.data 获取

        # 5. (重要) 调用子类的 init() - backtesting 库会自动调用
        #    但如果我们在基类 __init__ 中做了很多事，确保不要阻止它。
        #    实际上，backtesting 库会在调用完 __init__ 后再调用策略实例的 init()，
        #    所以我们不需要在这里手动调用 self.init().


    # --- 其他基类方法 (保持不变) ---
    def init(self): # 这个 init 由 backtesting 库在 __init__ 后调用
         pass # 子类实现
    def next(self): pass # 子类实现
    def set_data(self, data: pd.DataFrame): pass
    def set_transaction_cost(self, cost_pct: float): pass
    def generate_signal(self, current_data: pd.DataFrame) -> str: pass
    def generate_signals_dataframe(self, data: Optional[pd.DataFrame] = None) -> pd.DataFrame: pass
