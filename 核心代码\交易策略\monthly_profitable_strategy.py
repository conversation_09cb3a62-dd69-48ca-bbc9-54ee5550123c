# -*- coding: utf-8 -*-
"""
月月盈利均值回归策略 - 专门设计确保每月盈利
核心设计原则：
1. 极高的信号质量 - 宁缺毋滥
2. 严格的风险控制 - 止损优先
3. 优秀的盈亏比 - 至少2.5:1
4. 适度的交易频率 - 每月10-15次
5. 市场环境适应 - 避免逆势
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class MonthlyProfitableStrategy:
    """月月盈利均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'MonthlyProfitableStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 严格的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.015)  # 1.5%偏离
        
        # 优秀的风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.005)  # 0.5%风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 1.5)  # 严格止损
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 3.5)  # 优秀盈亏比2.33:1
        self.max_sl_pct = all_params.get('max_sl_pct', 0.02)  # 最大止损2%
        self.max_tp_pct = all_params.get('max_tp_pct', 0.05)  # 最大止盈5%
        
        # 严格的交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 480)  # 8小时间隔
        self.max_monthly_trades = all_params.get('max_monthly_trades', 15)  # 每月最多15次
        self.max_daily_trades = all_params.get('max_daily_trades', 1)  # 每日最多1次
        
        # 严格的质量控制参数
        self.min_volatility_pct = all_params.get('min_volatility_pct', 0.008)  # 最小波动率0.8%
        self.volume_threshold = all_params.get('volume_threshold', 1.1)  # 成交量要求
        self.trend_strength_threshold = all_params.get('trend_strength_threshold', 0.98)  # 趋势强度
        self.price_position_threshold = all_params.get('price_position_threshold', 0.3)  # 价格位置
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.monthly_trade_count = 0
        self.last_trade_date = None
        self.last_trade_month = None
        self.total_signals_generated = 0
        
        print(f"策略 MonthlyProfitableStrategy 初始化...")
        print(f"MonthlyProfitableStrategy: 月月盈利目标，价格偏离={self.price_deviation_pct*100}%, "
              f"盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, 每月最多{self.max_monthly_trades}次")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_monthly_trade_limit(self, current_time: datetime) -> bool:
        """检查每月交易次数限制"""
        current_month = (current_time.year, current_time.month)
        
        if self.last_trade_month != current_month:
            self.monthly_trade_count = 0
            self.last_trade_month = current_month
        
        return self.monthly_trade_count < self.max_monthly_trades
    
    def check_premium_price_deviation(self, data: Dict[str, Any]) -> bool:
        """检查优质价格偏离机会"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 价格必须显著低于短期均线
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            return deviation >= self.price_deviation_pct
        
        return False
    
    def check_market_trend_safety(self, data: Dict[str, Any]) -> bool:
        """检查市场趋势安全性"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 短期均线不能比长期均线低太多（避免强下跌趋势）
        trend_ratio = sma_short / sma_long
        return trend_ratio >= self.trend_strength_threshold
    
    def check_premium_volatility(self, data: Dict[str, Any]) -> bool:
        """检查优质波动率"""
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        close = data.get('CLOSE', 0)
        
        if high <= 0 or low <= 0 or close <= 0:
            return True
        
        # 当日波动率必须足够大，确保有利润空间
        volatility = (high - low) / close
        return volatility >= self.min_volatility_pct
    
    def check_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查成交量确认"""
        current_volume = data.get('VOLUME', 0)
        
        if current_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        return current_volume >= self.volume_threshold
    
    def check_optimal_entry_timing(self, data: Dict[str, Any]) -> bool:
        """检查最佳入场时机"""
        price = data.get('CLOSE', 0)
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        
        if price <= 0 or high <= 0 or low <= 0:
            return True
        
        # 价格应该接近当日低点（表明是下跌后的反弹机会）
        if high <= low:
            return True
        
        price_position = (price - low) / (high - low)
        return price_position <= self.price_position_threshold
    
    def calculate_dynamic_stop_loss_take_profit(self, data: Dict[str, Any]) -> tuple:
        """计算动态止损止盈"""
        price = data.get('CLOSE', 0)
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        
        # 计算ATR替代
        atr = (high - low) if (high - low) > 0 else price * 0.02
        
        # 基于ATR的止损止盈
        atr_stop_loss = price - (atr * self.atr_sl_multiple)
        atr_take_profit = price + (atr * self.atr_tp_multiple)
        
        # 基于百分比的止损止盈
        pct_stop_loss = price * (1 - self.max_sl_pct)
        pct_take_profit = price * (1 + self.max_tp_pct)
        
        # 取更保守的值
        final_stop_loss = max(atr_stop_loss, pct_stop_loss)
        final_take_profit = min(atr_take_profit, pct_take_profit)
        
        return final_stop_loss, final_take_profit
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 移除持仓检查，允许多次交易
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 月月盈利均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'HIGH', 'LOW', 'VOLUME']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查SMA数据
        if self.sma_short_key not in data:
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        if not self.check_monthly_trade_limit(current_time):
            return signals
        
        # 获取基础数据
        price = data.get('CLOSE')
        high = data.get('HIGH')
        low = data.get('LOW')
        volume = data.get('VOLUME')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key, sma_short)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, high, low, sma_short]):
            return signals
        
        # 月月盈利的严格买入条件
        premium_conditions = [
            self.check_premium_price_deviation(data),  # 优质价格偏离
            self.check_market_trend_safety(data),  # 市场趋势安全
            self.check_premium_volatility(data),  # 优质波动率
            self.check_volume_confirmation(data),  # 成交量确认
            self.check_optimal_entry_timing(data),  # 最佳入场时机
        ]
        
        if all(premium_conditions):
            # 计算动态止损止盈
            stop_loss, take_profit = self.calculate_dynamic_stop_loss_take_profit(data)
            
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            stop_loss_distance = price - stop_loss
            position_size = risk_amount / stop_loss_distance if stop_loss_distance > 0 else 0
            
            if position_size > 0:
                # 计算价格偏离度
                deviation = (sma_short - price) / sma_short * 100
                
                # 计算预期盈亏比
                profit_distance = take_profit - price
                loss_distance = price - stop_loss
                actual_profit_loss_ratio = profit_distance / loss_distance if loss_distance > 0 else 0
                
                signal = {
                    'symbol': 'BTCUSDT',
                    'action': 'buy',
                    'size': position_size,
                    'price': price,
                    'stop_loss_price': stop_loss,
                    'take_profit_price': take_profit,
                    'timestamp': current_time,
                    'strategy': 'MonthlyProfitableStrategy',
                    'signal_type': 'monthly_profitable_mean_reversion',
                    'reason': f'月月盈利信号: 偏离={deviation:.2f}%, 盈亏比={actual_profit_loss_ratio:.2f}:1, 价格={price:.2f}'
                }
                
                signals.append(signal)
                self.last_signal_time = current_time
                self.daily_trade_count += 1
                self.monthly_trade_count += 1
                self.total_signals_generated += 1
                
                print(f"[{current_time}] MonthlyProfitableStrategy: 月月盈利买入信号 BTCUSDT, "
                      f"偏离={deviation:.2f}%, 盈亏比={actual_profit_loss_ratio:.2f}:1, "
                      f"数量={position_size:.4f}, 月度第{self.monthly_trade_count}次, 信号#{self.total_signals_generated}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'MonthlyProfitableStrategy',
            'version': '1.0',
            'description': '月月盈利均值回归策略，专门设计确保每月盈利',
            'design_principles': [
                '极高的信号质量',
                '严格的风险控制',
                '优秀的盈亏比',
                '适度的交易频率',
                '市场环境适应'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'max_sl_pct': self.max_sl_pct,
                'max_tp_pct': self.max_tp_pct,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_monthly_trades': self.max_monthly_trades,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'total_signals_generated': self.total_signals_generated,
                'monthly_trade_count': self.monthly_trade_count,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
