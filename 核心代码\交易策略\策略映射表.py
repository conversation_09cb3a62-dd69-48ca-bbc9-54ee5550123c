# -*- coding: utf-8 -*-
"""
策略映射表 - 新旧策略名称对照
自动生成，请勿手动修改
"""

# 策略映射字典: 原名 -> 新信息
STRATEGY_MAPPING = {
    'MeanReversionStrategy': {
        'new_id': 'B01',
        'new_name': 'B01_均值回归',
        'simple_name': '均值回归',
        'category': '基础策略',
        'description': '基于布林带和RSI的均值回归策略'
    },
    'TrendFollowingStrategy': {
        'new_id': 'B02',
        'new_name': 'B02_趋势跟踪',
        'simple_name': '趋势跟踪',
        'category': '基础策略',
        'description': '基于移动平均和ADX的趋势跟踪策略'
    },
    'OptimizedMeanReversionStrategy': {
        'new_id': 'O01',
        'new_name': 'O01_优化均值回归',
        'simple_name': '优化均值回归',
        'category': '优化策略',
        'description': '参数优化的均值回归策略'
    },
    'BalancedMeanReversionStrategy': {
        'new_id': 'O02',
        'new_name': 'O02_平衡均值回归',
        'simple_name': '平衡均值回归',
        'category': '优化策略',
        'description': '平衡风险收益的均值回归策略'
    },
    'ImprovedTrendFollowingStrategy': {
        'new_id': 'O03',
        'new_name': 'O03_改进趋势跟踪',
        'simple_name': '改进趋势跟踪',
        'category': '优化策略',
        'description': '信号过滤和动态止损的改进趋势策略'
    },
    'PracticalMeanReversionStrategy': {
        'new_id': 'O04',
        'new_name': 'O04_实用均值回归',
        'simple_name': '实用均值回归',
        'category': '优化策略',
        'description': '实用性优化的均值回归策略'
    },
    'OptimizedTrendStrategyAI': {
        'new_id': 'A01',
        'new_name': 'A01_AI趋势预测',
        'simple_name': 'AI趋势预测',
        'category': 'AI策略',
        'description': '使用机器学习预测趋势的策略'
    },
    'AIOptimizedAlphaXStrategy': {
        'new_id': 'A02',
        'new_name': 'A02_AI优化阿尔法',
        'simple_name': 'AI优化阿尔法',
        'category': 'AI策略',
        'description': 'AI优化的阿尔法策略'
    },
    'MLEnhancedTrendFollowingStrategy': {
        'new_id': 'A03',
        'new_name': 'A03_ML增强趋势',
        'simple_name': 'ML增强趋势',
        'category': 'AI策略',
        'description': '机器学习增强的趋势跟踪策略'
    },
    'SimplifiedAIAlphaXStrategy': {
        'new_id': 'A04',
        'new_name': 'A04_简化AI阿尔法',
        'simple_name': '简化AI阿尔法',
        'category': 'AI策略',
        'description': '简化版AI阿尔法策略'
    },
    'AlphaXInspiredStrategy': {
        'new_id': 'H01',
        'new_name': 'H01_阿尔法X',
        'simple_name': '阿尔法X',
        'category': '高级策略',
        'description': '分批建仓和动态止损的高级策略'
    },
    'MultiTimeframeTrendFollowingStrategy': {
        'new_id': 'H02',
        'new_name': 'H02_多时间框架',
        'simple_name': '多时间框架',
        'category': '高级策略',
        'description': '多时间框架趋势确认策略'
    },
    'OptimizedAlphaXStrategy': {
        'new_id': 'H03',
        'new_name': 'H03_优化阿尔法X',
        'simple_name': '优化阿尔法X',
        'category': '高级策略',
        'description': '优化版阿尔法X策略'
    },
    'EnhancedAlphaXStrategyV2': {
        'new_id': 'H04',
        'new_name': 'H04_增强阿尔法V2',
        'simple_name': '增强阿尔法V2',
        'category': '高级策略',
        'description': '增强版阿尔法策略V2'
    },
    'UltraConservativeStrategy': {
        'new_id': 'C01',
        'new_name': 'C01_超保守',
        'simple_name': '超保守',
        'category': '保守策略',
        'description': '极低风险的保守策略'
    },
    'AggressiveMeanReversionStrategy': {
        'new_id': 'G01',
        'new_name': 'G01_激进均值回归',
        'simple_name': '激进均值回归',
        'category': '激进策略',
        'description': '高风险高收益的激进均值回归策略'
    },
    'FinalProfitableStrategy': {
        'new_id': 'S01',
        'new_name': 'S01_最终盈利',
        'simple_name': '最终盈利',
        'category': '特殊策略',
        'description': '专门优化盈利能力的策略'
    },
    'MonthlyProfitableStrategy': {
        'new_id': 'S02',
        'new_name': 'S02_月月盈利',
        'simple_name': '月月盈利',
        'category': '特殊策略',
        'description': '专门针对月度盈利优化的策略'
    },
    'FixedMeanReversionStrategy': {
        'new_id': 'S03',
        'new_name': 'S03_修复均值回归',
        'simple_name': '修复均值回归',
        'category': '特殊策略',
        'description': '修复版均值回归策略'
    },

}

# 反向映射: 新名 -> 原名
REVERSE_MAPPING = {
    'B01': 'MeanReversionStrategy',
    '均值回归': 'MeanReversionStrategy',
    'B01_均值回归': 'MeanReversionStrategy',
    'B02': 'TrendFollowingStrategy',
    '趋势跟踪': 'TrendFollowingStrategy',
    'B02_趋势跟踪': 'TrendFollowingStrategy',
    'O01': 'OptimizedMeanReversionStrategy',
    '优化均值回归': 'OptimizedMeanReversionStrategy',
    'O01_优化均值回归': 'OptimizedMeanReversionStrategy',
    'O02': 'BalancedMeanReversionStrategy',
    '平衡均值回归': 'BalancedMeanReversionStrategy',
    'O02_平衡均值回归': 'BalancedMeanReversionStrategy',
    'O03': 'ImprovedTrendFollowingStrategy',
    '改进趋势跟踪': 'ImprovedTrendFollowingStrategy',
    'O03_改进趋势跟踪': 'ImprovedTrendFollowingStrategy',
    'O04': 'PracticalMeanReversionStrategy',
    '实用均值回归': 'PracticalMeanReversionStrategy',
    'O04_实用均值回归': 'PracticalMeanReversionStrategy',
    'A01': 'OptimizedTrendStrategyAI',
    'AI趋势预测': 'OptimizedTrendStrategyAI',
    'A01_AI趋势预测': 'OptimizedTrendStrategyAI',
    'A02': 'AIOptimizedAlphaXStrategy',
    'AI优化阿尔法': 'AIOptimizedAlphaXStrategy',
    'A02_AI优化阿尔法': 'AIOptimizedAlphaXStrategy',
    'A03': 'MLEnhancedTrendFollowingStrategy',
    'ML增强趋势': 'MLEnhancedTrendFollowingStrategy',
    'A03_ML增强趋势': 'MLEnhancedTrendFollowingStrategy',
    'A04': 'SimplifiedAIAlphaXStrategy',
    '简化AI阿尔法': 'SimplifiedAIAlphaXStrategy',
    'A04_简化AI阿尔法': 'SimplifiedAIAlphaXStrategy',
    'H01': 'AlphaXInspiredStrategy',
    '阿尔法X': 'AlphaXInspiredStrategy',
    'H01_阿尔法X': 'AlphaXInspiredStrategy',
    'H02': 'MultiTimeframeTrendFollowingStrategy',
    '多时间框架': 'MultiTimeframeTrendFollowingStrategy',
    'H02_多时间框架': 'MultiTimeframeTrendFollowingStrategy',
    'H03': 'OptimizedAlphaXStrategy',
    '优化阿尔法X': 'OptimizedAlphaXStrategy',
    'H03_优化阿尔法X': 'OptimizedAlphaXStrategy',
    'H04': 'EnhancedAlphaXStrategyV2',
    '增强阿尔法V2': 'EnhancedAlphaXStrategyV2',
    'H04_增强阿尔法V2': 'EnhancedAlphaXStrategyV2',
    'C01': 'UltraConservativeStrategy',
    '超保守': 'UltraConservativeStrategy',
    'C01_超保守': 'UltraConservativeStrategy',
    'G01': 'AggressiveMeanReversionStrategy',
    '激进均值回归': 'AggressiveMeanReversionStrategy',
    'G01_激进均值回归': 'AggressiveMeanReversionStrategy',
    'S01': 'FinalProfitableStrategy',
    '最终盈利': 'FinalProfitableStrategy',
    'S01_最终盈利': 'FinalProfitableStrategy',
    'S02': 'MonthlyProfitableStrategy',
    '月月盈利': 'MonthlyProfitableStrategy',
    'S02_月月盈利': 'MonthlyProfitableStrategy',
    'S03': 'FixedMeanReversionStrategy',
    '修复均值回归': 'FixedMeanReversionStrategy',
    'S03_修复均值回归': 'FixedMeanReversionStrategy',

}

def get_original_name(new_name: str) -> str:
    """根据新名称获取原始名称"""
    return REVERSE_MAPPING.get(new_name, new_name)

def get_new_info(original_name: str) -> dict:
    """根据原始名称获取新信息"""
    return STRATEGY_MAPPING.get(original_name, {})
