# -*- coding: utf-8 -*-
"""
策略选择器 - 用户友好的策略选择界面
帮助用户快速找到合适的交易策略
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from typing import Dict, List, Any, Optional
import logging

# 导入策略管理器
from 核心代码.交易策略.策略库管理器 import StrategyLibraryManager

logger = logging.getLogger(__name__)

class StrategySelector:
    """策略选择器"""
    
    def __init__(self):
        self.manager = StrategyLibraryManager()
        self.user_profile = {}
        
    def welcome(self):
        """欢迎界面"""
        print("🎯 量化交易策略选择器")
        print("=" * 60)
        print("帮助您快速找到最适合的交易策略！")
        print()
        
    def collect_user_profile(self):
        """收集用户画像"""
        print("📋 请回答几个问题，帮助我们为您推荐合适的策略:")
        print("-" * 50)
        
        # 经验水平
        print("\n1. 您的量化交易经验水平？")
        print("   a) 新手 - 刚开始接触量化交易")
        print("   b) 有经验 - 有一定的交易和编程基础")
        print("   c) 专家 - 资深量化交易者")
        
        while True:
            choice = input("请选择 (a/b/c): ").lower().strip()
            if choice == 'a':
                self.user_profile['experience'] = '新手'
                break
            elif choice == 'b':
                self.user_profile['experience'] = '有经验'
                break
            elif choice == 'c':
                self.user_profile['experience'] = '专家'
                break
            else:
                print("请输入有效选项 (a/b/c)")
        
        # 风险偏好
        print("\n2. 您的风险承受能力？")
        print("   a) 低 - 希望稳健保值，不愿承受大幅波动")
        print("   b) 中 - 可以接受适度风险以获得更好收益")
        print("   c) 高 - 愿意承受较高风险追求高收益")
        
        while True:
            choice = input("请选择 (a/b/c): ").lower().strip()
            if choice == 'a':
                self.user_profile['risk_tolerance'] = '低'
                break
            elif choice == 'b':
                self.user_profile['risk_tolerance'] = '中'
                break
            elif choice == 'c':
                self.user_profile['risk_tolerance'] = '高'
                break
            else:
                print("请输入有效选项 (a/b/c)")
        
        # 投资目标
        print("\n3. 您的主要投资目标？")
        print("   a) 稳健 - 追求稳定的长期收益")
        print("   b) 收益 - 希望获得较高的投资回报")
        print("   c) 技术 - 想要尝试最新的AI技术")
        print("   d) 月度 - 希望每月都有盈利")
        
        while True:
            choice = input("请选择 (a/b/c/d): ").lower().strip()
            if choice == 'a':
                self.user_profile['goal'] = '稳健'
                break
            elif choice == 'b':
                self.user_profile['goal'] = '收益'
                break
            elif choice == 'c':
                self.user_profile['goal'] = '技术'
                break
            elif choice == 'd':
                self.user_profile['goal'] = '月度'
                break
            else:
                print("请输入有效选项 (a/b/c/d)")
        
        print(f"\n✅ 用户画像收集完成:")
        print(f"   经验水平: {self.user_profile['experience']}")
        print(f"   风险偏好: {self.user_profile['risk_tolerance']}")
        print(f"   投资目标: {self.user_profile['goal']}")
        
    def show_recommendations(self):
        """显示推荐策略"""
        print(f"\n🎯 基于您的画像，为您推荐以下策略:")
        print("=" * 60)
        
        recommendations = self.manager.recommend_strategies(self.user_profile)
        
        if not recommendations:
            print("抱歉，没有找到合适的策略推荐。")
            return
        
        for i, strategy in enumerate(recommendations, 1):
            print(f"\n{i}. 📌 {strategy['简名']} ({strategy['id']})")
            print(f"   描述: {strategy['描述']}")
            print(f"   难度: {strategy['难度']} | 风险: {strategy['风险']} | 收益潜力: {strategy['收益潜力']}")
            print(f"   稳定性: {strategy['稳定性']} | 分类: {strategy['category']}")
            print(f"   标签: {', '.join(strategy['标签'][:4])}")
            
            if i <= 3:  # 只显示前3个的详细信息
                print(f"   💡 推荐理由: 匹配度分数 {strategy['score']}")
        
        return recommendations
    
    def show_strategy_details(self, strategy_id: str):
        """显示策略详细信息"""
        strategy = self.manager.get_strategy_info(strategy_id)
        if not strategy:
            print(f"❌ 未找到策略: {strategy_id}")
            return
        
        print(f"\n📋 策略详细信息: {strategy['简名']}")
        print("=" * 50)
        print(f"策略ID: {strategy['id']}")
        print(f"原始名称: {strategy['原名']}")
        print(f"完整名称: {strategy['全名']}")
        print(f"描述: {strategy['描述']}")
        print(f"难度等级: {strategy['难度']}")
        print(f"风险等级: {strategy['风险']}")
        print(f"收益潜力: {strategy['收益潜力']}")
        print(f"稳定性: {strategy['稳定性']}")
        print(f"策略标签: {', '.join(strategy['标签'])}")
        
    def browse_by_category(self):
        """按分类浏览策略"""
        print(f"\n📚 按分类浏览策略:")
        print("=" * 50)
        
        categories = list(self.manager.strategy_catalog.keys())
        for i, category in enumerate(categories, 1):
            info = self.manager.strategy_catalog[category]
            print(f"{i}. {category} ({info['代码']}类) - {info['描述']}")
        
        while True:
            try:
                choice = input(f"\n请选择分类 (1-{len(categories)}) 或输入 'q' 退出: ").strip()
                if choice.lower() == 'q':
                    return
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(categories):
                    selected_category = categories[choice_idx]
                    self.show_category_strategies(selected_category)
                    break
                else:
                    print(f"请输入 1-{len(categories)} 之间的数字")
            except ValueError:
                print("请输入有效的数字")
    
    def show_category_strategies(self, category: str):
        """显示分类下的策略"""
        print(f"\n📂 {category} 策略列表:")
        print("-" * 40)
        
        strategies = self.manager.list_strategies(category=category)
        for i, strategy in enumerate(strategies, 1):
            print(f"{i}. {strategy['简名']} ({strategy['id']}) - {strategy['描述']}")
            print(f"   难度: {strategy['难度']} | 风险: {strategy['风险']} | 收益: {strategy['收益潜力']}")
        
        # 允许用户选择查看详情
        while True:
            choice = input(f"\n输入策略编号查看详情 (1-{len(strategies)}) 或 'b' 返回: ").strip()
            if choice.lower() == 'b':
                return
            
            try:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(strategies):
                    selected_strategy = strategies[choice_idx]
                    self.show_strategy_details(selected_strategy['id'])
                    break
                else:
                    print(f"请输入 1-{len(strategies)} 之间的数字")
            except ValueError:
                print("请输入有效的数字")
    
    def search_strategies(self):
        """搜索策略"""
        print(f"\n🔍 策略搜索:")
        print("-" * 30)
        
        keyword = input("请输入搜索关键词 (如: AI, 均值回归, 趋势): ").strip()
        if not keyword:
            print("搜索关键词不能为空")
            return
        
        results = self.manager.search_strategies(keyword)
        
        if not results:
            print(f"❌ 没有找到包含 '{keyword}' 的策略")
            return
        
        print(f"\n🎯 找到 {len(results)} 个相关策略:")
        print("-" * 40)
        
        for i, strategy in enumerate(results, 1):
            print(f"{i}. {strategy['简名']} ({strategy['id']}) - {strategy['category']}")
            print(f"   {strategy['描述']}")
            print(f"   标签: {', '.join(strategy['标签'][:3])}")
            print()
    
    def compare_strategies(self):
        """策略对比"""
        print(f"\n📊 策略对比:")
        print("-" * 30)
        
        strategy_ids = []
        print("请输入要对比的策略ID (如: B01, O01, A01)，用空格分隔:")
        ids_input = input("策略ID: ").strip()
        
        if not ids_input:
            print("请输入至少一个策略ID")
            return
        
        strategy_ids = ids_input.split()
        
        # 验证策略ID
        valid_ids = []
        for sid in strategy_ids:
            if self.manager.get_strategy_info(sid):
                valid_ids.append(sid)
            else:
                print(f"⚠️ 策略ID '{sid}' 不存在")
        
        if len(valid_ids) < 2:
            print("❌ 需要至少2个有效的策略ID进行对比")
            return
        
        # 显示对比表
        comparison = self.manager.get_strategy_comparison(valid_ids)
        print(f"\n📋 策略对比结果:")
        print("=" * 80)
        print(comparison.to_string(index=False))
    
    def main_menu(self):
        """主菜单"""
        while True:
            print(f"\n🏠 主菜单:")
            print("-" * 30)
            print("1. 🎯 智能推荐 (基于用户画像)")
            print("2. 📚 按分类浏览")
            print("3. 🔍 搜索策略")
            print("4. 📊 策略对比")
            print("5. 📋 查看完整目录")
            print("6. ❓ 帮助")
            print("0. 🚪 退出")
            
            choice = input("\n请选择功能 (0-6): ").strip()
            
            if choice == '1':
                if not self.user_profile:
                    self.collect_user_profile()
                self.show_recommendations()
            elif choice == '2':
                self.browse_by_category()
            elif choice == '3':
                self.search_strategies()
            elif choice == '4':
                self.compare_strategies()
            elif choice == '5':
                self.manager.print_strategy_catalog()
            elif choice == '6':
                self.show_help()
            elif choice == '0':
                print("👋 感谢使用策略选择器！")
                break
            else:
                print("❌ 请输入有效的选项 (0-6)")
    
    def show_help(self):
        """显示帮助信息"""
        print(f"\n❓ 策略选择器帮助:")
        print("=" * 50)
        print("📌 策略分类说明:")
        print("  B类 - 基础策略: 适合新手，简单易懂")
        print("  O类 - 优化策略: 经过参数优化，适合有经验用户")
        print("  A类 - AI策略: 使用机器学习，适合高级用户")
        print("  H类 - 高级策略: 复杂策略，适合专业用户")
        print("  C类 - 保守策略: 低风险，适合稳健投资")
        print("  S类 - 特殊策略: 特定目标，如月度盈利")
        print()
        print("📌 使用建议:")
        print("  1. 新手建议从B类或C类策略开始")
        print("  2. 有经验用户可以尝试O类策略")
        print("  3. 技术爱好者可以探索A类AI策略")
        print("  4. 专业用户可以使用H类高级策略")
        print()
        print("📌 策略ID格式: 分类字母 + 两位数字 (如: B01, O03, A02)")
    
    def run(self):
        """运行策略选择器"""
        self.welcome()
        self.main_menu()

def main():
    """主函数"""
    selector = StrategySelector()
    selector.run()

if __name__ == "__main__":
    main()
