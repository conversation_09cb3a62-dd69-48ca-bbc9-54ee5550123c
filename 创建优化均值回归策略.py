# -*- coding: utf-8 -*-
"""
创建优化版均值回归策略
"""
import sys
import os

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def create_optimized_mean_reversion_strategy():
    """创建优化版均值回归策略"""
    
    strategy_code = '''# -*- coding: utf-8 -*-
"""
优化版均值回归策略 - 解决过度交易和盈亏比问题
主要改进：
1. 更严格的入场条件（布林带2.5倍标准差，RSI≤20）
2. 更好的盈亏比（止损2.0ATR，止盈4.0ATR）
3. 趋势过滤（ADX<25震荡市过滤）
4. 交易频率控制（信号间隔180分钟）
5. 成交量确认和连续亏损保护
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class OptimizedMeanReversionStrategy:
    """优化版均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'OptimizedMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 优化后的布林带参数
        self.bbands_period = all_params.get('bbands_period', 30)  # 增加周期减少噪音
        self.bbands_std_dev = all_params.get('bbands_std_dev', 2.5)  # 更严格的条件
        
        # 优化后的RSI参数
        self.rsi_period = all_params.get('rsi_period', 14)
        self.rsi_oversold = all_params.get('rsi_oversold', 20)  # 更极端的超卖
        
        # 趋势过滤参数
        self.adx_period = all_params.get('adx_period', 14)
        self.adx_max_threshold = all_params.get('adx_max_threshold', 25)  # 震荡市过滤
        
        # 优化后的风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.005)  # 降低单笔风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 2.0)  # 增加止损空间
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 4.0)  # 提高盈亏比
        
        # 交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 180)  # 3小时间隔
        self.max_daily_trades = all_params.get('max_daily_trades', 3)  # 每日最多3次
        
        # 新增过滤条件
        self.volume_threshold = all_params.get('volume_threshold', 1.2)  # 成交量确认
        self.max_consecutive_losses = all_params.get('max_consecutive_losses', 3)  # 连续亏损保护
        self.price_near_bb_pct = all_params.get('price_near_bb_pct', 0.002)  # 价格接近布林带下轨
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.consecutive_losses = 0
        
        # 构建指标键名
        self.bb_lower_key = f"BBands_{self.bbands_period}_{str(self.bbands_std_dev).replace('.', '_')}_BB_Lower"
        self.bb_middle_key = f"BBands_{self.bbands_period}_{str(self.bbands_std_dev).replace('.', '_')}_BB_Middle"
        self.rsi_key = f"RSI_{self.rsi_period}"
        self.adx_key = f"ADX_{self.adx_period}"
        self.atr_key = 'ATR_14'
        
        print(f"策略 OptimizedMeanReversionStrategy 初始化...")
        print(f"OptimizedMeanReversionStrategy: 布林带={self.bbands_period}期{self.bbands_std_dev}倍, "
              f"RSI≤{self.rsi_oversold}, ADX≤{self.adx_max_threshold}, 盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_consecutive_loss_protection(self) -> bool:
        """检查连续亏损保护"""
        return self.consecutive_losses < self.max_consecutive_losses
    
    def check_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查成交量确认"""
        current_volume = data.get('VOLUME', 0)
        avg_volume = data.get('VOLUME_SMA_20', current_volume)
        
        if avg_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        volume_ratio = current_volume / avg_volume
        return volume_ratio >= self.volume_threshold
    
    def check_price_near_bb_lower(self, data: Dict[str, Any]) -> bool:
        """检查价格是否真正接近布林带下轨"""
        price = data.get('CLOSE', 0)
        bb_lower = data.get(self.bb_lower_key, 0)
        
        if bb_lower <= 0:
            return True
        
        # 价格必须非常接近或低于布林带下轨
        price_diff_pct = (price - bb_lower) / bb_lower
        return price_diff_pct <= self.price_near_bb_pct
    
    def check_market_regime(self, data: Dict[str, Any]) -> bool:
        """检查市场状态是否适合均值回归"""
        adx = data.get(self.adx_key, 0)
        
        # 只在震荡市（ADX较低）时进行均值回归交易
        return adx <= self.adx_max_threshold
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 优化版均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.bb_lower_key, self.rsi_key, self.adx_key, self.atr_key]
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        if not self.check_consecutive_loss_protection():
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        bb_lower = data.get(self.bb_lower_key)
        bb_middle = data.get(self.bb_middle_key, 0)
        rsi = data.get(self.rsi_key)
        adx = data.get(self.adx_key)
        atr = data.get(self.atr_key)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, bb_lower, rsi, adx, atr]):
            return signals
        
        # 优化后的买入条件（更严格）
        buy_conditions = [
            price <= bb_lower,  # 价格触及或跌破布林带下轨
            rsi <= self.rsi_oversold,  # RSI极度超卖
            self.check_market_regime(data),  # 震荡市环境
            self.check_volume_confirmation(data),  # 成交量确认
            self.check_price_near_bb_lower(data),  # 价格真正接近下轨
        ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'OptimizedMeanReversionStrategy',
                'signal_type': 'optimized_mean_reversion',
                'reason': f'优化均值回归: RSI={rsi:.2f}, ADX={adx:.2f}, 价格={price:.2f}≤BB下轨={bb_lower:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] OptimizedMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"RSI={rsi:.2f}, ADX={adx:.2f}, 价格={price:.2f}≤BB下轨={bb_lower:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def on_trade_closed(self, trade_result: Dict[str, Any]):
        """交易结束回调，用于更新连续亏损计数"""
        pnl = trade_result.get('pnl', 0)
        
        if pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0  # 盈利后重置连续亏损计数
        
        print(f"OptimizedMeanReversionStrategy: 交易结束，PnL={pnl:.2f}, 连续亏损次数={self.consecutive_losses}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'OptimizedMeanReversionStrategy',
            'version': '1.0',
            'description': '优化版均值回归策略，解决过度交易和盈亏比问题',
            'improvements': [
                '更严格的入场条件',
                '更好的盈亏比（2:1）',
                '趋势过滤机制',
                '交易频率控制',
                '成交量确认',
                '连续亏损保护'
            ],
            'parameters': {
                'bbands_period': self.bbands_period,
                'bbands_std_dev': self.bbands_std_dev,
                'rsi_oversold': self.rsi_oversold,
                'adx_max_threshold': self.adx_max_threshold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'consecutive_losses': self.consecutive_losses,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
'''
    
    # 保存策略文件
    strategy_file_path = os.path.join(CORE_CODE_PATH, '交易策略', 'optimized_mean_reversion_strategy.py')
    
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.write(strategy_code)
    
    print(f"优化均值回归策略已保存到: {strategy_file_path}")
    return strategy_file_path

def update_strategy_library_mean_reversion():
    """更新策略库，添加优化均值回归策略"""
    
    strategy_lib_path = os.path.join(CORE_CODE_PATH, '交易策略', '策略库.py')
    
    # 读取现有策略库文件
    with open(strategy_lib_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加优化均值回归策略导入
    import_line = "from .optimized_mean_reversion_strategy import OptimizedMeanReversionStrategy"
    
    if import_line not in content:
        # 在文件末尾添加
        additional_content = f'''

# 优化均值回归策略导入
{import_line}

# 更新策略字典
STRATEGIES['OptimizedMeanReversionStrategy'] = OptimizedMeanReversionStrategy
'''
        content += additional_content
        
        # 写回文件
        with open(strategy_lib_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"策略库已更新: {strategy_lib_path}")

if __name__ == '__main__':
    print("创建优化版均值回归策略")
    print("=" * 50)
    
    # 创建优化策略
    create_optimized_mean_reversion_strategy()
    update_strategy_library_mean_reversion()
    
    print("\n✅ 优化均值回归策略创建完成！")
    print("\n🔧 主要优化：")
    print("• 布林带：20期2.0倍 → 30期2.5倍（减少假信号）")
    print("• RSI阈值：30 → 20（更极端超卖）")
    print("• 盈亏比：1.67:1 → 2:1（提高盈利潜力）")
    print("• 信号间隔：无 → 180分钟（减少过度交易）")
    print("• 新增ADX≤25震荡市过滤")
    print("• 新增成交量确认机制")
    print("• 新增连续亏损保护")
    print("• 降低单笔风险：1% → 0.5%")
    
    print("\n📊 预期改进：")
    print("• 交易次数：544 → 150-200次/月（减少60-70%）")
    print("• 交易成本：27.2% → 7.5-10%（大幅降低）")
    print("• 盈亏比：1.67:1 → 2:1（提升20%）")
    print("• 预期收益：-33.60% → +5%~+15%（扭亏为盈）")
    
    print("\n🧪 测试命令：")
    print("./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2024-04-01 --end_date 2024-04-30 --strategy OptimizedMeanReversionStrategy")
