# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import logging
import time
from typing import Dict, List, Any, Type, Optional

# 使用明确的相对导入
from ..交易策略.策略库 import TradingStrategy, STRATEGIES
from ..优化器.差分进化优化 import StrategyOptimizer
from ..风险管理.风险控制器 import DefaultRiskManager
from ..市场数据.数据获取器 import MarketData
from ..资产组合.组合管理器 import Portfolio
from ..因子计算.因子库 import calculate_factors # <--- 新增导入
# 导入监控指标和初始化函数
try:
    from ..监控与报告.实时监控指标 import ACCOUNT_VALUE, POSITION_EXPOSURE, TRADE_COUNT, REALIZED_PNL_DAILY, SYSTEM_ERRORS, init_prometheus_exporter, prometheus_enabled
except ImportError:
    prometheus_enabled = False
    logging.warning("实时监控模块导入失败")
    
    class DummyMetric: # 定义假的指标
        def set(self, v):
            pass
            
        def labels(self, *a, **kw):
            return self
            
        def inc(self, v=1):
            pass
            
    ACCOUNT_VALUE = DummyMetric()
    POSITION_EXPOSURE = DummyMetric()
    TRADE_COUNT = DummyMetric()
    REALIZED_PNL_DAILY = DummyMetric()
    SYSTEM_ERRORS = DummyMetric()
    
    def init_prometheus_exporter(p=8000):
        return False

logger = logging.getLogger(__name__)

class EnhancedQuantSystem:
    """增强型量化交易系统基础类"""
    def __init__(self, config: Any):
        self.config_obj = config
        self.config_dict = config if isinstance(config, dict) else config.__dict__
        self.initial_capital: float = float(self.config_dict.get('initial_capital', 100000.0))
        self.live_trading: bool = bool(self.config_dict.get('live_trading', False))
        self._last_processed_time: Dict[str, float] = {}
        self._last_hourly_report_time: Optional[float] = None
        self._last_daily_reset_time: Optional[pd.Timestamp] = None # 使用 Timestamp 存储日期
        self.stop_requested = False # 用于优雅停止循环

        logger.info(f"初始化交易系统: InitCapital={self.initial_capital:.2f}, Live={self.live_trading}")

        # --- 初始化核心组件 ---
        self.risk_manager: RiskManager = DefaultRiskManager(self.config_dict)
        self.market_data_handler: MarketData = MarketData(self.config_obj)
        self.portfolio: Portfolio = Portfolio(self.config_obj)

        # --- 加载策略 ---
        self.strategies: List[TradingStrategy] = []
        strategies_config_list = getattr(self.config_obj, 'strategies', []) if not isinstance(config, dict) else self.config_dict.get('strategies', [])
        if not strategies_config_list:
            logger.warning("配置中无 'strategies' 定义")
        else:
            for strategy_conf in strategies_config_list:
                name = strategy_conf.get('name')
                params = strategy_conf.get('params', {})
                if name in STRATEGIES:
                    try:
                        # 创建策略实例，传递 broker 和 data 参数
                        # 策略应负责的标的列表，可以从全局配置或策略特定配置中获取
                        # 此处简化为使用系统配置中的所有标的
                        strategy_symbols = list(self.portfolio.all_symbols) # 或者更细致的配置
                        
                        instance = STRATEGIES[name](engine=self.portfolio,
                                                   symbol_list=strategy_symbols,
                                                   params=params)
                        # instance.set_transaction_cost(self.portfolio.transaction_cost_pct) # 交易成本由Portfolio处理
                        self.strategies.append(instance)
                        logger.info(f"已加载策略: {name} for symbols: {strategy_symbols}, Params: {params}")
                    except Exception as e:
                        logger.error(f"加载策略 {name} 失败: {e}", exc_info=True)
                else:
                    logger.warning(f"策略 '{name}' 不在库中: {list(STRATEGIES.keys())}")

        # --- 初始化交易执行器 ---
        self.executor = None
        if self.live_trading:
            logger.info("实盘模式启动，初始化实盘执行器...")
            try:
                from ..订单执行.QMTExecutor import QMTExecutor
                qmt_config = self.config_dict.get('qmt', {})
                self.executor = QMTExecutor(
                    api_key=qmt_config.get('api_key'),
                    api_secret=qmt_config.get('api_secret'),
                    account_id=qmt_config.get('account_id'),
                    server_address=qmt_config.get('server_address')
                )
                self.executor.connect()
                if not self.executor.check_connection():
                    raise ConnectionError("连接QMT/Ptrade失败")
                logger.info("QMT/Ptrade 实盘执行器初始化并连接。")
            except Exception as e:
                logger.critical(f"初始化实盘执行器失败: {e}", exc_info=True)
                raise RuntimeError("无法启动实盘执行器") from e
        else:
            logger.info("模拟模式启动，使用内部 Portfolio。")

        # --- 初始化监控 ---
        if prometheus_enabled:
            init_prometheus_exporter()

    def get_market_data(self, symbol: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        return self.market_data_handler.get_market_data(symbol, start_date, end_date)

    def request_stop(self):
        """请求停止主循环"""
        logger.info("收到停止请求，将在当前循环结束后退出。")
        self.stop_requested = True

    def run(self, interval_seconds: int = 60):
        """运行主交易循环"""
        logger.info(f"交易系统主循环启动... 处理间隔: {interval_seconds} 秒")
        try:
            while not self.stop_requested: # 检查停止标志
                loop_start_time = time.monotonic()
                logger.info(f"====== 循环开始: {pd.Timestamp.now(tz='Asia/Shanghai')} ======") # 添加时区
                try:
                    self.process_tick_or_bar()
                    self._run_periodic_tasks()
                except Exception as loop_error:
                    logger.exception("主循环迭代错误:")
                    SYSTEM_ERRORS.labels(component='main_loop').inc() # 记录系统错误
                loop_end_time = time.monotonic()
                elapsed = loop_end_time - loop_start_time
                sleep_time = max(0, interval_seconds - elapsed)
                logger.info(f"====== 循环结束, 耗时: {elapsed:.2f}s, 休眠: {sleep_time:.2f}s ======\n")
                if sleep_time > 0:
                    time.sleep(sleep_time) # 等待剩余时间
        except KeyboardInterrupt:
            logger.info("用户中断 (Ctrl+C)，关闭系统...")
        except Exception as e:
            logger.critical("主循环严重错误!", exc_info=True)
            SYSTEM_ERRORS.labels(component='system_crash').inc()
        finally:
            self.shutdown()

    def process_tick_or_bar(self):
        """处理一个时间单位的数据和交易逻辑"""
        logger.debug("开始处理时间步...")
        now = time.time()
        now_dt = pd.Timestamp.now()
        try: # 获取组合价值
            current_portfolio_info = self.portfolio.get_portfolio_value(self.market_data_handler)
            current_portfolio_value = current_portfolio_info['total']
            current_cash = current_portfolio_info.get('cash', 0)
            logger.info(f"当前PV: {current_portfolio_value:.2f}, Cash: {current_cash:.2f}, RealPnL: {current_portfolio_info.get('realized_pnl', 0):.2f}, UnrealPnL: {current_portfolio_info.get('unrealized_pnl', 0):.2f}")
            self.update_monitoring_metrics(current_portfolio_info)
        except Exception as e:
            logger.error(f"获取组合价值失败: {e}", exc_info=True)
            return

        # --- 1. 获取当前市场数据快照 (所有相关品种的最新bar) ---
        # 假设 market_data_handler 可以提供一个包含所有监控品种最新bar数据的DataFrame
        # 这个DataFrame的索引应该是 MultiIndex (Symbol, Datetime)
        # 并且列中包含 OHLCV 和策略可能需要的因子数据
        # 此处简化为循环获取，理想情况下应有批量获取接口
        all_symbols = list(self.portfolio.all_symbols)
        market_data_frames = []
        current_prices_dict = {} # 用于 get_portfolio_value 的快照

        for symbol in all_symbols:
            # 优化：减少不必要的重复获取，例如使用 _last_processed_time
            # process_interval = 30 # 可以根据需要调整
            # last_processed = self._last_processed_time.get(symbol, 0)
            # if now - last_processed < process_interval:
            #     # TODO: 需要一种方式来获取这些品种的最新价格用于估值，即使不为它们运行策略
            #     continue

            end_date_str = now_dt.strftime('%Y-%m-%d %H:%M:%S') # 需要精确到分钟或秒
            # 策略通常需要一段历史数据来计算指标，但 on_bar 只需要最新的bar和其上的指标值
            # MarketData.get_latest_bar_with_factors(symbol, now_dt) 可能是更合适的接口
            # 这里暂时假设 get_market_data 返回的数据中，最后一行是最新的bar
            # 并且因子已经预计算并包含在内
            # TODO: 优化数据获取，避免每次都取很长的历史数据
            start_date_str = (now_dt - pd.Timedelta(days=90)).strftime('%Y-%m-%d') # 假设因子计算最多需要90天
            
            # 模拟 MarketData 返回带有因子的最新bar数据
            # 在真实场景中，MarketDataHandler 需要提供这样的功能
            # df_symbol_latest_bar = self.market_data_handler.get_latest_bar_with_factors(symbol, now_dt)
            # 为了演示，我们继续使用 get_market_data 并取最后一行
            df_symbol_ohlcv = self.market_data_handler.get_market_data(symbol, start_date_str, end_date_str)

            if df_symbol_ohlcv is not None and not df_symbol_ohlcv.empty:
                # --- BEGIN 因子计算集成 ---
                # 构建该品种在此次循环中需要计算的因子配置
                # 这个配置可以基于所有活动策略对该 symbol 的综合需求
                # 此处简化：假设我们知道 MeanReversionStrategy 的需求
                # TODO: 更通用的做法是遍历策略，收集因子需求
                factors_to_calc_for_symbol = {}
                for strat_instance in self.strategies:
                    # 假设策略实例有 parameters 属性，并且其中包含因子配置信息
                    if hasattr(strat_instance, 'parameters'):
                        # For MeanReversionStrategy example from logs:
                        # params: {'bbands_prefix': 'Bollinger Bands', 'rsi_factor': 'RSI', ...}
                        # It expects columns 'RSI' and 'Bollinger Bands_LowerBand' etc.
                        
                        # RSI
                        rsi_col_name = strat_instance.parameters.get('rsi_factor', 'RSI') # Default name strategy expects
                        rsi_window = strat_instance.parameters.get('rsi_window', 14) # Default window
                        if rsi_col_name not in factors_to_calc_for_symbol: # Avoid redundant config
                             factors_to_calc_for_symbol[rsi_col_name] = {"function": "RSI", "params": {"window": rsi_window}}
                        
                        # Bollinger Bands
                        bb_prefix = strat_instance.parameters.get('bbands_prefix', 'Bollinger Bands')
                        bb_window = strat_instance.parameters.get('bb_window', 20)
                        bb_std_dev = strat_instance.parameters.get('bb_std_dev', 2.0)
                        if bb_prefix not in factors_to_calc_for_symbol:
                             factors_to_calc_for_symbol[bb_prefix] = {"function": "Bollinger Bands", 
                                                                    "params": {"window": bb_window, "num_std_dev": bb_std_dev}}
                
                        # --- BEGIN Add VWAP Factor Configuration ---
                        # Check if strategy requires VWAP (e.g., MeanReversionStrategy does)
                        vwap_key = strat_instance.parameters.get('vwap_key', None)
                        if vwap_key and vwap_key not in factors_to_calc_for_symbol:
                            vwap_window = strat_instance.parameters.get('vwap_window', 20)
                            factors_to_calc_for_symbol[vwap_key] = {"function": "VWAP", "params": {"window": vwap_window}}
                        # --- END Add VWAP Factor Configuration ---
                
                # 1. 创建一个用于因子计算的副本，保持原始列名格式
                df_for_factors = df_symbol_ohlcv.copy()

                # 标准化列名以确保与因子库的期望一致
                short_to_long_map = {
                    'O': 'OPEN', 'H': 'HIGH', 'L': 'LOW', 'C': 'CLOSE', 'V': 'VOLUME',
                    'DATETIME': 'DATETIME', 'TIMESTAMP': 'TIMESTAMP', 'TIME': 'TIMESTAMP', # 常见时间列的兼容
                    'SYMBOL': 'SYMBOL', 'CODE': 'SYMBOL' # 常见代码列的兼容
                }
                # 创建实际需要重命名的映射，只针对DataFrame中存在的、且在short_to_long_map中定义的短名称列
                cols_to_rename = {
                    short_col: long_col 
                    for short_col, long_col in short_to_long_map.items() 
                    if short_col in df_for_factors.columns and df_for_factors.columns.tolist().count(long_col) == 0 # 避免重复重命名或覆盖已存在的长名称
                }
                if cols_to_rename:
                    logger.info(f"为 {symbol} 标准化列名（因子计算前）: {cols_to_rename}")
                    df_for_factors.rename(columns=cols_to_rename, inplace=True)
                    logger.info(f"为 {symbol} 标准化后的列名: {df_for_factors.columns.tolist()}")

                # 2. 构建因子配置 (这部分逻辑之前分析过是正确的)
                # factors_to_calc_for_symbol 已在上层构建好

                # 3. 计算因子
                df_symbol_processed = df_for_factors # 默认情况下，如果没有因子要计算，或者计算失败，就使用这个
                if factors_to_calc_for_symbol:
                    # 确保 df_for_factors 包含 'CLOSE' 列 (全大写)
                    if 'CLOSE' not in df_for_factors.columns:
                        logger.error(f"因子计算前，{symbol} 的数据缺少 'CLOSE' 列 (标准化后)。可用列: {df_for_factors.columns.tolist()}")
                        # 如果没有CLOSE列，大多数因子无法计算，但我们仍将 df_for_factors 传递下去
                        # calculate_factors 内部应该能处理这种情况或其调用的函数会处理
                        df_symbol_processed = calculate_factors(df_for_factors.copy(), factors_to_calc_for_symbol) # 传递副本
                    else:
                        df_symbol_processed = calculate_factors(df_for_factors.copy(), factors_to_calc_for_symbol) # 传递副本
                        # df_symbol_processed 现在应该包含大写OHLCV + 因子列 (如 RSI, Bollinger Bands_MiddleBand)
                else: # 如果 factors_to_calc_for_symbol 为空
                    logger.debug(f"没有为 {symbol} 配置特定因子计算，使用（可能已标准化的）OHLCV 数据。")
                    # df_symbol_processed 已经是 df_for_factors (列名已大写)

                # --- END 因子计算集成 ---

                # 4. 准备策略输入 (策略期望 'CLOSE', 'RSI', 'Bollinger Bands_LowerBand' 等)
                # df_symbol_processed 应该已经满足这个要求了（包含大写CLOSE和因子列名）
                
                latest_bar_df = df_symbol_processed.iloc[-1:].copy() # 取最后一行形成DataFrame (仍然是 DatetimeIndex)


                # --- 添加 MultiIndex 转换逻辑 ---
                # 创建 MultiIndex，第一层是当前品种 Symbol，第二层是原始的 DatetimeIndex
                # names 参数很重要，要匹配下游期望的 MultiIndex 名 ('Symbol', 'datetime')
                multi_index = pd.MultiIndex.from_product([[symbol], latest_bar_df.index], names=['Symbol', 'datetime'])

                # 将单行 DataFrame 重新索引到新的 MultiIndex
                # latest_bar_with_multiindex = latest_bar_df.set_index(multi_index) # set_index 不适合这里，直接替换索引
                latest_bar_with_multiindex = latest_bar_df.copy()
                latest_bar_with_multiindex.index = multi_index
                # --- MultiIndex 转换逻辑结束 ---

                # 验证 latest_bar_with_multiindex 是否确实有预期的 MultiIndex 结构 (可选，用于调试)
                if not (isinstance(latest_bar_with_multiindex.index, pd.MultiIndex) and \
                        len(latest_bar_with_multiindex.index.names) == 2 and \
                        latest_bar_with_multiindex.index.names[0] == 'Symbol' and \
                        latest_bar_with_multiindex.index.names[1] == 'datetime'):
                    logger.error(f"DEBUG: 为 {symbol} 生成的带有 MultiIndex 的最新bar结构仍然不正确。实际索引: {latest_bar_with_multiindex.index}")
                    # 如果转换后仍然有问题，跳过这个品种
                    continue

                # 将带有 MultiIndex 的最新bar添加到列表中
                market_data_frames.append(latest_bar_with_multiindex)

                # 确定收盘价列名，用于 current_prices_dict
                close_col_name_for_price_snapshot = 'CLOSE' # 假设经过因子计算，CLOSE 列已存在并大写
                if close_col_name_for_price_snapshot in latest_bar_df.columns: # 使用原始 DatetimeIndex 的 latest_bar_df 获取价格
                    current_prices_dict[symbol] = latest_bar_df[close_col_name_for_price_snapshot].iloc[0]
                elif 'Close' in latest_bar_df.columns:  # 尝试查找小写版本
                    current_prices_dict[symbol] = latest_bar_df['Close'].iloc[0]
                    # 为策略处理添加大写版本
                    latest_bar_df['CLOSE'] = latest_bar_df['Close']
                    latest_bar_with_multiindex['CLOSE'] = latest_bar_df['Close']
                else:
                     logger.error(f"传递给策略前，{symbol} 的数据中缺少可识别的收盘价列。可用列: {latest_bar_df.columns.tolist()}")
                     # 如果没有价格，也不需要更新 prices_dict
                self._last_processed_time[symbol] = now # 更新处理时间
            else:
                logger.warning(f"无 {symbol} 的最新市场数据。")

        if not market_data_frames:
            logger.info("没有获取到任何品种的市场数据，跳过策略处理。")
            return

        # 合并所有品种的最新bar数据到一个DataFrame
        try:
            current_market_data_snapshot = pd.concat(market_data_frames)
        except Exception as e_concat:
            logger.error(f"合并市场数据快照时出错: {e_concat}. Frames: {market_data_frames}")
            return
        
        # 使用快照更新组合价值 (确保使用最新的价格)
        try:
            current_prices_series = pd.Series(current_prices_dict)
            current_portfolio_info = self.portfolio.get_portfolio_value(self.market_data_handler, current_prices_snapshot=current_prices_series)
            current_portfolio_value = current_portfolio_info['total']
            logger.info(f"更新后PV: {current_portfolio_value:.2f} (基于快照价格)")
        except Exception as e:
            logger.error(f"使用价格快照更新组合价值失败: {e}", exc_info=True)
            # 回退到不使用快照的方式
            current_portfolio_info = self.portfolio.get_portfolio_value(self.market_data_handler)
            current_portfolio_value = current_portfolio_info['total']


        # --- 2. 遍历策略，生成信号 ---
        all_generated_signals: List[Dict] = []
        for strategy_instance in self.strategies:
            logger.debug(f"应用策略 {strategy_instance.strategy_name}...")
            try:
                # Add debug logs before calling on_bar
                logger.debug(f"传递给策略 {strategy_instance.strategy_name} 的数据列: {current_market_data_snapshot.columns.tolist()}")
                logger.debug(f"传递给策略 {strategy_instance.strategy_name} 的数据索引名: {current_market_data_snapshot.index.names}")
                
                # 策略的 on_bar 方法现在接收包含所有相关品种最新bar的DataFrame
                # 并返回一个信号字典列表
                signals_from_strategy = strategy_instance.on_bar(current_market_data_snapshot)
                
                if signals_from_strategy:
                    logger.info(f"策略 {strategy_instance.strategy_name} 生成 {len(signals_from_strategy)} 个信号: {signals_from_strategy}")
                    all_generated_signals.extend(signals_from_strategy)
                else:
                    logger.debug(f"策略 {strategy_instance.strategy_name} 无信号生成。")
            except Exception as e:
                logger.error(f"处理策略 {strategy_instance.strategy_name} 出错: {e}", exc_info=True)
                SYSTEM_ERRORS.labels(component='strategy_on_bar').inc()
        
        # --- 3. （可选）信号后处理/过滤/合并 ---
        # 例如，如果多个策略对同一标的产生冲突信号，可以在这里处理
        # final_signals_to_execute = self.process_and_filter_signals(all_generated_signals)
        final_signals_to_execute = all_generated_signals # 简化：直接执行所有信号

        # --- 4. 执行交易 ---
        if not final_signals_to_execute:
            logger.debug("无最终信号需要执行。")
        else:
            logger.info(f"准备执行 {len(final_signals_to_execute)} 个最终信号...")

        for signal_dict in final_signals_to_execute:
            try:
                symbol = signal_dict.get('symbol')
                action = signal_dict.get('action')
                logger.info(f"执行信号: {action} {signal_dict.get('size_pct', signal_dict.get('size'))} {symbol} @ {signal_dict.get('price')} from {signal_dict.get('strategy')}")

                # 实盘逻辑 (如果适用)
                if self.live_trading and self.executor:
                    logger.debug(f"发送信号到实盘执行器: {signal_dict}")
                    # 注意: QMTExecutor.execute_order 可能需要不同的参数格式
                    # 需要适配 self.executor.execute_order 的接口
                    # result = self.executor.execute_order(signal_dict) 
                    # logger.info(f"实盘订单结果: {result}")
                    # 暂时跳过实盘执行的具体实现细节
                    pass 
                
                # 模拟交易执行 (Portfolio.execute_trade)
                # Portfolio.execute_trade 内部会进行风险检查
                # 它需要 risk_manager 和 current_portfolio_value
                trade_success = self.portfolio.execute_trade(signal_dict, self.risk_manager, current_portfolio_value)
                
                if trade_success:
                    logger.info(f"模拟交易成功: {action} {symbol}")
                    # 交易成功后，组合价值会变化，需要重新获取以供下一个信号使用（如果严格按顺序执行）
                    # 或者假设信号是基于同一时间点的状态生成的，批量执行后再更新组合价值
                    # 为简化，这里在每个交易后都更新一下（可能影响性能，但逻辑更清晰）
                    current_portfolio_info = self.portfolio.get_portfolio_value(self.market_data_handler, current_prices_snapshot=pd.Series(current_prices_dict))
                    current_portfolio_value = current_portfolio_info['total']
                    TRADE_COUNT.inc()
                    
                    # 更新持仓风险暴露指标
                    if 'positions' in current_portfolio_info:
                        for sym, pos_detail in current_portfolio_info['positions'].items():
                            if 'market_value' in pos_detail:
                                POSITION_EXPOSURE.labels(symbol=sym).set(pos_detail['market_value'])
                else:
                    logger.warning(f"模拟交易失败: {action} {symbol} (信号: {signal_dict})")

            except Exception as e:
                logger.error(f"执行信号 {signal_dict} 时出错: {e}", exc_info=True)
                SYSTEM_ERRORS.labels(component='trade_execution').inc()
            
        logger.debug("时间步处理完成。")

    def _run_periodic_tasks(self):
        now = time.time()
        now_dt = pd.Timestamp.now(tz='Asia/Shanghai')
        hourly_interval = 3600
        if self._last_hourly_report_time is None or (now - self._last_hourly_report_time >= hourly_interval):
            self._hourly_report()
            self._last_hourly_report_time = now
            
        daily_reset_hour = 0
        today_str = now_dt.strftime('%Y-%m-%d')
        last_reset_date_str = self._last_daily_reset_time.strftime('%Y-%m-%d') if self._last_daily_reset_time else None
        if now_dt.hour == daily_reset_hour and last_reset_date_str != today_str:
            logger.info(f"执行每日重置 @ {now_dt}")
            self.risk_manager.reset_daily_state()
            self._last_daily_reset_time = now_dt

    def _hourly_report(self):
        logger.info("="*20 + f" 每小时报告 ({pd.Timestamp.now(tz='Asia/Shanghai')}) " + "="*20)
        try:
            info = self.portfolio.get_portfolio_value(self.market_data_handler)
            logger.info(f"  总资产: {info['total']:.2f}")
            logger.info(f"  现金: {info['cash']:.2f}")
            logger.info(f"  持仓市值: {info['positions_value']:.2f}")
            logger.info(f"  当日已实现PnL(RM): {self.risk_manager.daily_pnl:.2f}")
            logger.info(f"  累计已实现PnL(PF): {info['realized_pnl']:.2f}")
            logger.info(f"  未实现PnL: {info['unrealized_pnl']:.2f}")
            if info['positions']:
                logger.info("  持仓:")
                for s, d in info['positions'].items():
                    logger.info(f"    - {s}: Qty={d['shares']:.4f}, AvgPx={d['avg_price']:.4f}, Val={d['market_value']:.2f}, UnrealPnL={d['unrealized_pnl']:.2f}")
            else:
                logger.info("  当前无持仓")
        except Exception as e:
            logger.error(f"生成小时报告出错: {e}")
        logger.info("="*68) # 调整分隔符长度

    def shutdown(self):
        logger.info("开始关闭交易系统...")
        if self.live_trading and self.executor and hasattr(self.executor, 'disconnect'):
            try:
                self.executor.disconnect()
                logger.info("实盘执行器已断开")
            except Exception as e:
                logger.error(f"断开实盘执行器出错: {e}")
        logger.info("交易系统已关闭")

    def update_monitoring_metrics(self, portfolio_info: Dict):
        if not prometheus_enabled:
            return
        try:
            ACCOUNT_VALUE.set(portfolio_info.get("total", 0))
            REALIZED_PNL_DAILY.set(self.risk_manager.daily_pnl)
        except Exception as e:
            logger.error(f"更新监控指标出错: {e}", exc_info=False)
            
        symbols_in_portfolio = set(portfolio_info.get("positions", {}).keys())
        for symbol, position in portfolio_info.get("positions", {}).items():
            try:
                POSITION_EXPOSURE.labels(symbol=symbol).set(position.get("market_value", 0))
            except Exception as le: # pragma: no cover
                logger.error(f"设置 Posi Exposure 出错 ({symbol}): {le}")


class AdaptiveTradingSystem(EnhancedQuantSystem):
    def __init__(self, config: Any):
        super().__init__(config)
        logger.info("初始化自适应系统特性...")
        self.optimizer_config = self.config_dict.get('optimization')
        self.optimizer = None # Optimizer instance
        if self.optimizer_config:
            logger.info(f"优化配置已加载，目标指标: {self.optimizer_config.get('metric_to_optimize')}")
            # 根据配置初始化 StrategyOptimizer
            # self.optimizer = StrategyOptimizer(...) # 这部分逻辑在 run_optimization 中
        else:
            logger.info("未找到优化配置，参数优化功能将不可用。")

    def initialize(self): # 这个方法似乎没有被外部调用，可以考虑移除或整合
        """初始化系统组件"""
        logger.info("初始化自适应交易系统...")
        # 验证所有必要组件是否已正确初始化
        if not self.market_data_handler:
            raise RuntimeError("市场数据处理器未初始化")
        if not self.portfolio:
            raise RuntimeError("组合管理器未初始化")
        if not self.risk_manager:
            raise RuntimeError("风险控制器未初始化")
        if not self.strategies:
            logger.warning("未加载任何交易策略")
            
        # 初始化监控指标
        if prometheus_enabled:
            init_prometheus_exporter()
            
        logger.info("自适应交易系统初始化完成")

    def run_optimization(self):
        if not self.optimizer_config:
            logger.warning("无优化配置")
            return None
            
        logger.info("开始参数优化...")
        strategy_name = self.optimizer_config.get('target_strategy')
        param_ranges_cfg = self.optimizer_config.get('param_grid')
        metric = self.optimizer_config.get('metric', 'Sharpe Ratio')
        maximize = self.optimizer_config.get('maximize', True)
        
        if not self.optimizer_config:
            logger.warning("无优化配置，无法运行优化。")
            return None
            
        logger.info("开始参数优化...")
        
        target_strategy_name = self.optimizer_config.get('target_strategy_name')
        param_grid_config = self.optimizer_config.get('param_grid') # e.g. {'MeanReversion': {'rsi_ob': [60, 80, 5], ...}}
        metric_to_optimize = self.optimizer_config.get('metric_to_optimize', 'Sharpe Ratio') # Metric from backtesting stats
        optimization_maximize = self.optimizer_config.get('maximize', True)
        
        if not (target_strategy_name and param_grid_config and target_strategy_name in param_grid_config):
            logger.error("优化配置不完整或目标策略参数范围未定义。")
            return None
        
        if target_strategy_name not in STRATEGIES:
            logger.error(f"目标优化策略 '{target_strategy_name}' 未在策略库中找到。")
            return None
            
        strategy_class_to_optimize = STRATEGIES[target_strategy_name]
        optimization_parameter_ranges = param_grid_config[target_strategy_name]

        # --- 定义评估函数 (用于backtesting) ---
        def backtesting_evaluation_function(params_tuple: Tuple) -> float:
            # 将元组参数转换回字典
            current_eval_params = {}
            param_names = list(optimization_parameter_ranges.keys())
            for i, value in enumerate(params_tuple):
                current_eval_params[param_names[i]] = value
            
            logger.debug(f"优化评估参数: {current_eval_params}")
            try:
                # 确保 backtesting 库可用
                if not backtesting_available:
                    logger.error("backtesting库不可用，无法执行优化评估。")
                    return float('-inf') if optimization_maximize else float('inf')
                
                from backtesting import Backtest # type: ignore
                
                # 获取优化所需的数据 (应可配置)
                # TODO: 使优化数据源可配置
                opt_data_symbol = self.optimizer_config.get('optimization_data_symbol', 'AAPL')
                opt_data_start = self.optimizer_config.get('optimization_data_start', '2023-01-01')
                opt_data_end = self.optimizer_config.get('optimization_data_end', '2023-06-30')
                
                # 使用 MarketDataHandler 获取数据
                optimization_data = self.market_data_handler.get_market_data(
                    opt_data_symbol, opt_data_start, opt_data_end
                )
                if optimization_data is None or optimization_data.empty:
                    logger.error(f"无法加载用于优化的数据: {opt_data_symbol} from {opt_data_start} to {opt_data_end}")
                    return float('-inf') if optimization_maximize else float('inf')

                # backtesting 要求列名大写开头
                optimization_data.columns = [col.capitalize() for col in optimization_data.columns]
                if not all(c in optimization_data.columns for c in ['Open', 'High', 'Low', 'Close']):
                    logger.error(f"优化数据缺少OHLC列: {optimization_data.columns}")
                    return float('-inf') if optimization_maximize else float('inf')

                # 创建 Backtest 实例
                # 注意: strategy_class_to_optimize 的 __init__ 需要能处理 backtesting 的 broker, data 参数
                # TradingStrategy 基类需要修改以支持这种双模式初始化
                bt = Backtest(optimization_data, strategy_class_to_optimize, 
                              cash=self.config_dict.get('optimization_initial_cash', 100000), 
                              commission=self.config_dict.get('optimization_commission_pct', 0.001),
                              params=current_eval_params) # 传递参数给策略
                
                stats = bt.run() # run() 不再接受 **params，参数通过 Backtest 构造函数传递
                
                metric_value = stats.get(metric_to_optimize, np.nan)
                
                # 处理 NaN 或无效值
                if pd.isna(metric_value):
                    logger.warning(f"指标 '{metric_to_optimize}' 在回测结果中为 NaN for params {current_eval_params}")
                    return float('-inf') if optimization_maximize else float('inf')
                
                logger.debug(f"参数 {current_eval_params} -> {metric_to_optimize}: {metric_value:.4f}")
                return float(metric_value)

            except Exception as e:
                logger.error(f"优化评估函数出错 for params {current_eval_params}: {e}", exc_info=True)
                return float('-inf') if optimization_maximize else float('inf')

        # --- 准备优化器参数 ---
        # StrategyOptimizer 需要参数范围列表，每个元素是 (min, max, step) 或 (min, max) for float
        # 这里的 optimization_parameter_ranges 是 {'param_name': [min, max, step_or_type]}
        # 需要转换
        param_bounds_for_optimizer = []
        for param_name, range_config in optimization_parameter_ranges.items():
            if not (isinstance(range_config, list) and len(range_config) >= 2):
                logger.error(f"参数 {param_name} 的范围配置格式错误: {range_config}")
                return None
            
            p_min, p_max = range_config[0], range_config[1]
            p_step = range_config[2] if len(range_config) > 2 else None # step 对差分进化可能不是直接使用

            # 差分进化通常处理连续参数，或需要特定方式处理离散/整数参数
            # 这里简化为传递 (min, max) 对，具体类型由评估函数内部或策略参数处理逻辑决定
            param_bounds_for_optimizer.append((p_min, p_max))


        # --- 初始化并运行优化器 ---
        try:
            if self.optimizer is None: # 确保只初始化一次，或根据需要重新初始化
                 self.optimizer = StrategyOptimizer(
                    objective_func=backtesting_evaluation_function,
                    param_bounds=param_bounds_for_optimizer, # [(min1,max1), (min2,max2), ...]
                    maximize=optimization_maximize,
                    population_size=self.optimizer_config.get('population_size', 20),
                    max_generations=self.optimizer_config.get('max_generations', 50),
                    mutation_factor=self.optimizer_config.get('mutation_factor', 0.5),
                    crossover_rate=self.optimizer_config.get('crossover_rate', 0.7),
                    seed=self.optimizer_config.get('seed') 
                )
            
            best_params_tuple, best_score = self.optimizer.optimize()
            
            # 将最优参数元组转换回字典
            best_params_dict = {}
            param_names = list(optimization_parameter_ranges.keys())
            for i, value in enumerate(best_params_tuple):
                best_params_dict[param_names[i]] = value

            logger.info(f"参数优化完成。最优指标值 ({metric_to_optimize}): {best_score:.4f}")
            logger.info(f"最优参数 ({target_strategy_name}): {best_params_dict}")
            
            # TODO: 可以选择将最优参数应用回配置或活动的策略实例
            # self.apply_optimized_parameters(target_strategy_name, best_params_dict)
            
            return {'best_score': best_score, 'best_params': best_params_dict}

        except Exception as e:
            logger.exception("执行参数优化过程中发生严重错误:")
            return None
