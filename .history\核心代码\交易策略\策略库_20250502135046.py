# -*- coding: utf-8 -*-
# In 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import field
try:
    from backtesting import Strategy as BacktestStrategy
    from backtesting.lib import crossover
    backtesting_available = True
except ImportError:
    class BacktestStrategy:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}
    class crossover:  # 替身类
        def __init__(self, a, b):
            pass
    backtesting_available = False

logger = logging.getLogger(__name__)

class BacktestResult:
    """回测结果类，用于封装回测统计数据"""
    def __init__(self, stats: dict):
        self.stats = stats

    def __getattr__(self, name):
        if name in self.stats:
            return self.stats[name]
        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def __repr__(self):
        return f"<BacktestResult: {self.stats}>"

try:
    from backtesting import Strategy as BASE_STRATEGY
except ImportError:
    class BASE_STRATEGY:  # 替身类
        def __init__(self, broker, data, params=None):
            self.broker = broker
            self.data = data
            self.params = params or {}

class TradingStrategy(BASE_STRATEGY):  # 确保继承自 backtesting.Strategy 或其替身
    """交易策略基类"""
    # --- 定义类属性作为 backtesting 可以识别和优化的参数 ---
    stop_loss_pct = 0.05
    take_profit_pct = 0.10
    # 其他通用参数...

    # --- 自定义属性 ---
    parameters: Dict[str, Any] = field(default_factory=dict) # 存储最终参数
    transaction_cost_pct: float = 0.001 # 用于自定义回测
    risk_manager = None
    _data = None # 用于自定义回测
    _ready: bool = False # 用于标记初始化是否成功
    performance_stats = {
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_pnl': 0.0,
        'max_win': 0.0,
        'max_loss': 0.0,
        'long_trades': 0,
        'short_trades': 0
    }

    def update_performance_stats(self, pnl: float, trade_type: str):
        """
        更新绩效统计数据
        
        Args:
            pnl: 盈亏比例
            trade_type: 交易类型 ('long' 或 'short')
        """
        self.performance_stats['total_trades'] += 1
        self.performance_stats['total_pnl'] += pnl
        
        if trade_type == 'long':
            self.performance_stats['long_trades'] += 1
        else:
            self.performance_stats['short_trades'] += 1
            
        if pnl > 0:
            self.performance_stats['winning_trades'] += 1
            if pnl > self.performance_stats['max_win']:
                self.performance_stats['max_win'] = pnl
        else:
            self.performance_stats['losing_trades'] += 1
            if pnl < self.performance_stats['max_loss']:
                self.performance_stats['max_loss'] = pnl
                
        logger.info(f"更新绩效统计: 总交易数={self.performance_stats['total_trades']}, 总盈亏={self.performance_stats['total_pnl']:.2%}")
