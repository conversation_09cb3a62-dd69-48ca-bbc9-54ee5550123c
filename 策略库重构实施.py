# -*- coding: utf-8 -*-
"""
策略库重构实施脚本
将现有的混乱策略库重构为规范化的新版本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import shutil
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class StrategyLibraryRefactor:
    """策略库重构器"""
    
    def __init__(self):
        self.strategy_mapping = self._create_strategy_mapping()
        self.backup_dir = "策略库备份"
        
    def _create_strategy_mapping(self) -> Dict[str, Dict[str, str]]:
        """创建策略映射表"""
        return {
            # 基础策略
            'MeanReversionStrategy': {
                'new_id': 'B01',
                'new_name': 'B01_均值回归',
                'simple_name': '均值回归',
                'category': '基础策略',
                'description': '基于布林带和RSI的均值回归策略'
            },
            'TrendFollowingStrategy': {
                'new_id': 'B02',
                'new_name': 'B02_趋势跟踪',
                'simple_name': '趋势跟踪',
                'category': '基础策略',
                'description': '基于移动平均和ADX的趋势跟踪策略'
            },
            
            # 优化策略
            'OptimizedMeanReversionStrategy': {
                'new_id': 'O01',
                'new_name': 'O01_优化均值回归',
                'simple_name': '优化均值回归',
                'category': '优化策略',
                'description': '参数优化的均值回归策略'
            },
            'BalancedMeanReversionStrategy': {
                'new_id': 'O02',
                'new_name': 'O02_平衡均值回归',
                'simple_name': '平衡均值回归',
                'category': '优化策略',
                'description': '平衡风险收益的均值回归策略'
            },
            'ImprovedTrendFollowingStrategy': {
                'new_id': 'O03',
                'new_name': 'O03_改进趋势跟踪',
                'simple_name': '改进趋势跟踪',
                'category': '优化策略',
                'description': '信号过滤和动态止损的改进趋势策略'
            },
            'PracticalMeanReversionStrategy': {
                'new_id': 'O04',
                'new_name': 'O04_实用均值回归',
                'simple_name': '实用均值回归',
                'category': '优化策略',
                'description': '实用性优化的均值回归策略'
            },
            
            # AI策略
            'OptimizedTrendStrategyAI': {
                'new_id': 'A01',
                'new_name': 'A01_AI趋势预测',
                'simple_name': 'AI趋势预测',
                'category': 'AI策略',
                'description': '使用机器学习预测趋势的策略'
            },
            'AIOptimizedAlphaXStrategy': {
                'new_id': 'A02',
                'new_name': 'A02_AI优化阿尔法',
                'simple_name': 'AI优化阿尔法',
                'category': 'AI策略',
                'description': 'AI优化的阿尔法策略'
            },
            'MLEnhancedTrendFollowingStrategy': {
                'new_id': 'A03',
                'new_name': 'A03_ML增强趋势',
                'simple_name': 'ML增强趋势',
                'category': 'AI策略',
                'description': '机器学习增强的趋势跟踪策略'
            },
            'SimplifiedAIAlphaXStrategy': {
                'new_id': 'A04',
                'new_name': 'A04_简化AI阿尔法',
                'simple_name': '简化AI阿尔法',
                'category': 'AI策略',
                'description': '简化版AI阿尔法策略'
            },
            
            # 高级策略
            'AlphaXInspiredStrategy': {
                'new_id': 'H01',
                'new_name': 'H01_阿尔法X',
                'simple_name': '阿尔法X',
                'category': '高级策略',
                'description': '分批建仓和动态止损的高级策略'
            },
            'MultiTimeframeTrendFollowingStrategy': {
                'new_id': 'H02',
                'new_name': 'H02_多时间框架',
                'simple_name': '多时间框架',
                'category': '高级策略',
                'description': '多时间框架趋势确认策略'
            },
            'OptimizedAlphaXStrategy': {
                'new_id': 'H03',
                'new_name': 'H03_优化阿尔法X',
                'simple_name': '优化阿尔法X',
                'category': '高级策略',
                'description': '优化版阿尔法X策略'
            },
            'EnhancedAlphaXStrategyV2': {
                'new_id': 'H04',
                'new_name': 'H04_增强阿尔法V2',
                'simple_name': '增强阿尔法V2',
                'category': '高级策略',
                'description': '增强版阿尔法策略V2'
            },
            
            # 保守策略
            'UltraConservativeStrategy': {
                'new_id': 'C01',
                'new_name': 'C01_超保守',
                'simple_name': '超保守',
                'category': '保守策略',
                'description': '极低风险的保守策略'
            },
            
            # 激进策略
            'AggressiveMeanReversionStrategy': {
                'new_id': 'G01',
                'new_name': 'G01_激进均值回归',
                'simple_name': '激进均值回归',
                'category': '激进策略',
                'description': '高风险高收益的激进均值回归策略'
            },
            
            # 特殊策略
            'FinalProfitableStrategy': {
                'new_id': 'S01',
                'new_name': 'S01_最终盈利',
                'simple_name': '最终盈利',
                'category': '特殊策略',
                'description': '专门优化盈利能力的策略'
            },
            'MonthlyProfitableStrategy': {
                'new_id': 'S02',
                'new_name': 'S02_月月盈利',
                'simple_name': '月月盈利',
                'category': '特殊策略',
                'description': '专门针对月度盈利优化的策略'
            },
            'FixedMeanReversionStrategy': {
                'new_id': 'S03',
                'new_name': 'S03_修复均值回归',
                'simple_name': '修复均值回归',
                'category': '特殊策略',
                'description': '修复版均值回归策略'
            }
        }
    
    def backup_current_library(self):
        """备份当前策略库"""
        print("📦 备份当前策略库...")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        strategy_dir = "核心代码/交易策略"
        if os.path.exists(strategy_dir):
            backup_path = os.path.join(self.backup_dir, "策略库_备份")
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            shutil.copytree(strategy_dir, backup_path)
            print(f"✅ 策略库已备份到: {backup_path}")
        else:
            print("❌ 策略库目录不存在")
    
    def generate_strategy_mapping_file(self):
        """生成策略映射文件"""
        print("📝 生成策略映射文件...")
        
        mapping_content = """# -*- coding: utf-8 -*-
\"\"\"
策略映射表 - 新旧策略名称对照
自动生成，请勿手动修改
\"\"\"

# 策略映射字典: 原名 -> 新信息
STRATEGY_MAPPING = {
"""
        
        for original_name, info in self.strategy_mapping.items():
            mapping_content += f"""    '{original_name}': {{
        'new_id': '{info['new_id']}',
        'new_name': '{info['new_name']}',
        'simple_name': '{info['simple_name']}',
        'category': '{info['category']}',
        'description': '{info['description']}'
    }},
"""
        
        mapping_content += """
}

# 反向映射: 新名 -> 原名
REVERSE_MAPPING = {
"""
        
        for original_name, info in self.strategy_mapping.items():
            mapping_content += f"    '{info['new_id']}': '{original_name}',\n"
            mapping_content += f"    '{info['simple_name']}': '{original_name}',\n"
            mapping_content += f"    '{info['new_name']}': '{original_name}',\n"
        
        mapping_content += """
}

def get_original_name(new_name: str) -> str:
    \"\"\"根据新名称获取原始名称\"\"\"
    return REVERSE_MAPPING.get(new_name, new_name)

def get_new_info(original_name: str) -> dict:
    \"\"\"根据原始名称获取新信息\"\"\"
    return STRATEGY_MAPPING.get(original_name, {})
"""
        
        with open("核心代码/交易策略/策略映射表.py", "w", encoding="utf-8") as f:
            f.write(mapping_content)
        
        print("✅ 策略映射文件已生成")
    
    def create_strategy_index(self):
        """创建策略索引"""
        print("📚 创建策略索引...")
        
        # 按分类整理策略
        categories = {}
        for original_name, info in self.strategy_mapping.items():
            category = info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(info)
        
        # 生成索引文件
        index_content = """# 📚 量化交易策略库索引

## 策略分类总览

"""
        
        for category, strategies in categories.items():
            index_content += f"### {category}\n\n"
            for strategy in strategies:
                index_content += f"- **{strategy['simple_name']}** (`{strategy['new_id']}`) - {strategy['description']}\n"
            index_content += "\n"
        
        index_content += """
## 快速查找

### 按难度分类
- **初级**: B01, B02, C01 - 适合新手
- **中级**: O01, O02, O03, O04 - 适合有经验用户  
- **高级**: A01, H01, G01 - 适合高级用户
- **专家**: A02, A03, H02, H04 - 适合专业用户

### 按风险分类
- **低风险**: B01, B02, O01, O02, C01
- **中风险**: O03, A01, A02, A03, H01, H02, S01, S02
- **高风险**: G01

### 按策略类型分类
- **均值回归**: B01, O01, O02, O04, G01, S03
- **趋势跟踪**: B02, O03, A01, A03, H02
- **AI策略**: A01, A02, A03, A04
- **特殊目标**: S01, S02, S03

## 使用建议

1. **新手用户**: 建议从 B01(均值回归) 或 B02(趋势跟踪) 开始
2. **稳健投资**: 推荐 C01(超保守) 或 O02(平衡均值回归)
3. **追求收益**: 可尝试 H01(阿尔法X) 或 A02(AI优化阿尔法)
4. **技术爱好者**: 探索 A01(AI趋势预测) 或 H02(多时间框架)
5. **月度目标**: 使用 S02(月月盈利) 或 S01(最终盈利)
"""
        
        with open("策略库索引.md", "w", encoding="utf-8") as f:
            f.write(index_content)
        
        print("✅ 策略索引已创建")
    
    def generate_migration_guide(self):
        """生成迁移指南"""
        print("📖 生成迁移指南...")
        
        guide_content = """# 策略库重构迁移指南

## 概述

为了解决策略库命名混乱、分类不清、使用不便的问题，我们对策略库进行了全面重构。

## 主要改进

1. **规范化命名**: 采用分类前缀 + 编号的方式 (如: B01, O03, A02)
2. **清晰分类**: 按功能和复杂度分为6大类
3. **用户友好**: 提供简短的中文名称
4. **智能推荐**: 根据用户画像推荐合适策略

## 策略分类体系

- **B类 (基础策略)**: 适合新手，简单易懂
- **O类 (优化策略)**: 经过参数优化，适合有经验用户
- **A类 (AI策略)**: 使用机器学习，适合高级用户
- **H类 (高级策略)**: 复杂策略，适合专业用户
- **C类 (保守策略)**: 低风险，适合稳健投资
- **G类 (激进策略)**: 高风险高收益
- **S类 (特殊策略)**: 特定目标策略

## 迁移对照表

| 原策略名称 | 新策略ID | 新策略名称 | 分类 |
|-----------|---------|-----------|------|
"""
        
        for original_name, info in self.strategy_mapping.items():
            guide_content += f"| {original_name} | {info['new_id']} | {info['simple_name']} | {info['category']} |\n"
        
        guide_content += """
## 使用方法

### 1. 使用策略选择器 (推荐)
```bash
python 策略选择器.py
```

### 2. 使用策略管理器
```python
from 核心代码.交易策略.策略库管理器 import StrategyLibraryManager

manager = StrategyLibraryManager()

# 搜索策略
results = manager.search_strategies('AI')

# 获取推荐
recommendations = manager.recommend_strategies({
    'experience': '新手',
    'risk_tolerance': '低', 
    'goal': '稳健'
})
```

### 3. 向后兼容
原有的策略名称仍然可以使用，系统会自动映射到新的策略。

## 注意事项

1. 建议逐步迁移到新的命名体系
2. 新项目请使用新的策略ID和名称
3. 如有问题，请参考策略映射表
"""
        
        with open("策略库迁移指南.md", "w", encoding="utf-8") as f:
            f.write(guide_content)
        
        print("✅ 迁移指南已生成")
    
    def create_strategy_comparison_table(self):
        """创建策略对比表"""
        print("📊 创建策略对比表...")
        
        comparison_data = []
        for original_name, info in self.strategy_mapping.items():
            comparison_data.append({
                '原策略名称': original_name,
                '新策略ID': info['new_id'],
                '简化名称': info['simple_name'],
                '策略分类': info['category'],
                '策略描述': info['description']
            })
        
        df = pd.DataFrame(comparison_data)
        df.to_csv("策略对比表.csv", index=False, encoding="utf-8-sig")
        
        print("✅ 策略对比表已保存为 CSV 文件")
        print(f"📋 策略总数: {len(comparison_data)}")
        
        # 统计各分类数量
        category_counts = df['策略分类'].value_counts()
        print("\n📊 各分类策略数量:")
        for category, count in category_counts.items():
            print(f"  {category}: {count}个")
    
    def run_refactor(self):
        """执行重构"""
        print("🚀 开始策略库重构")
        print("=" * 60)
        
        # 1. 备份当前策略库
        self.backup_current_library()
        
        # 2. 生成策略映射文件
        self.generate_strategy_mapping_file()
        
        # 3. 创建策略索引
        self.create_strategy_index()
        
        # 4. 生成迁移指南
        self.generate_migration_guide()
        
        # 5. 创建对比表
        self.create_strategy_comparison_table()
        
        print("\n✅ 策略库重构完成!")
        print("=" * 60)
        print("📁 生成的文件:")
        print("  - 核心代码/交易策略/策略映射表.py")
        print("  - 策略库索引.md")
        print("  - 策略库迁移指南.md")
        print("  - 策略对比表.csv")
        print("  - 策略库备份/ (备份目录)")
        print("\n🎯 下一步:")
        print("  1. 运行 'python 策略选择器.py' 体验新的策略选择界面")
        print("  2. 查看迁移指南了解如何使用新的策略体系")
        print("  3. 逐步将现有代码迁移到新的命名体系")

def main():
    """主函数"""
    refactor = StrategyLibraryRefactor()
    refactor.run_refactor()

if __name__ == "__main__":
    main()
