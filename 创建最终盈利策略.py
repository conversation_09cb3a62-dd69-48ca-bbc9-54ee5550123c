# -*- coding: utf-8 -*-
"""
创建最终盈利均值回归策略 - 基于PracticalMeanReversionStrategy的优化
"""
import sys
import os

# 添加项目路径
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
CORE_CODE_PATH = os.path.join(PROJECT_ROOT, '核心代码')
if CORE_CODE_PATH not in sys.path: 
    sys.path.insert(0, CORE_CODE_PATH)
    sys.path.insert(0, PROJECT_ROOT)

def create_final_profitable_strategy():
    """创建最终盈利策略"""
    
    strategy_code = '''# -*- coding: utf-8 -*-
"""
最终盈利均值回归策略 - 基于实测数据优化参数
核心改进：
1. 提高盈亏比至2:1
2. 增加更严格的入场条件
3. 优化止损止盈设置
4. 保持每天1次交易频率
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class FinalProfitableStrategy:
    """最终盈利均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'FinalProfitableStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 优化的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.012)  # 提高至1.2%
        
        # 优化的风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.008)  # 降低风险至0.8%
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 1.8)  # 缩小止损
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 3.6)  # 提高盈亏比至2:1
        
        # 优化的交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 480)  # 8小时间隔
        self.max_daily_trades = all_params.get('max_daily_trades', 2)  # 每日最多2次
        
        # 增强的质量控制参数
        self.volume_threshold = all_params.get('volume_threshold', 1.1)  # 轻微成交量要求
        self.min_volatility_pct = all_params.get('min_volatility_pct', 0.008)  # 提高波动率要求
        self.trend_strength_threshold = all_params.get('trend_strength_threshold', 0.99)  # 更严格的趋势要求
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.total_signals_generated = 0
        
        print(f"策略 FinalProfitableStrategy 初始化...")
        print(f"FinalProfitableStrategy: 价格偏离={self.price_deviation_pct*100}%, "
              f"盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, "
              f"信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_enhanced_price_deviation(self, data: Dict[str, Any]) -> bool:
        """检查增强的价格偏离条件"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 价格必须显著低于短期均线
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            return deviation >= self.price_deviation_pct
        
        return False
    
    def check_enhanced_volume_condition(self, data: Dict[str, Any]) -> bool:
        """检查增强的成交量条件"""
        current_volume = data.get('VOLUME', 0)
        
        if current_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        # 成交量不能太低
        return current_volume >= self.volume_threshold
    
    def check_enhanced_volatility_condition(self, data: Dict[str, Any]) -> bool:
        """检查增强的波动率条件"""
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        close = data.get('CLOSE', 0)
        
        if high <= 0 or low <= 0 or close <= 0:
            return True
        
        # 要求更高的波动率
        volatility = (high - low) / close
        return volatility >= self.min_volatility_pct
    
    def check_enhanced_trend_condition(self, data: Dict[str, Any]) -> bool:
        """检查增强的趋势条件"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 更严格的趋势检查：短期均线不能比长期均线低太多
        trend_ratio = sma_short / sma_long
        return trend_ratio >= self.trend_strength_threshold
    
    def check_market_timing(self, data: Dict[str, Any]) -> bool:
        """检查市场时机（新增）"""
        price = data.get('CLOSE', 0)
        high = data.get('HIGH', 0)
        low = data.get('LOW', 0)
        
        if price <= 0 or high <= 0 or low <= 0:
            return True
        
        # 价格应该接近当前K线的低点（表明是下跌后的反弹机会）
        price_position = (price - low) / (high - low) if high > low else 0.5
        return price_position <= 0.3  # 价格在K线下方30%以内
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法 - 移除持仓检查"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 移除持仓检查，允许多次交易
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 最终优化均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', 'HIGH', 'LOW', 'VOLUME']
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查SMA数据
        if self.sma_short_key not in data:
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取基础数据
        price = data.get('CLOSE')
        high = data.get('HIGH')
        low = data.get('LOW')
        volume = data.get('VOLUME')
        sma_short = data.get(self.sma_short_key)
        sma_long = data.get(self.sma_long_key, sma_short)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, high, low, sma_short]):
            return signals
        
        # 计算优化的ATR
        atr = (high - low) if (high - low) > 0 else price * 0.02
        
        # 最终优化的买入条件（更严格）
        buy_conditions = [
            self.check_enhanced_price_deviation(data),  # 增强的价格偏离
            self.check_enhanced_volume_condition(data),  # 增强的成交量条件
            self.check_enhanced_volatility_condition(data),  # 增强的波动率条件
            self.check_enhanced_trend_condition(data),  # 增强的趋势条件
            self.check_market_timing(data),  # 市场时机
        ]
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'FinalProfitableStrategy',
                'signal_type': 'final_profitable_mean_reversion',
                'reason': f'最终盈利均值回归: 偏离={deviation:.2f}%, 价格={price:.2f}<SMA={sma_short:.2f}, 波动={atr:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            self.total_signals_generated += 1
            
            print(f"[{current_time}] FinalProfitableStrategy: 买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, 价格={price:.2f}<SMA={sma_short:.2f}, "
                  f"数量={position_size:.4f}, 信号#{self.total_signals_generated}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'FinalProfitableStrategy',
            'version': '1.0',
            'description': '最终盈利均值回归策略，基于实测数据优化',
            'improvements': [
                '提高盈亏比至2:1',
                '增强入场条件',
                '优化止损止盈',
                '保持理想交易频率'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'total_signals_generated': self.total_signals_generated,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
'''
    
    # 保存策略文件
    strategy_file_path = os.path.join(CORE_CODE_PATH, '交易策略', 'final_profitable_strategy.py')
    
    with open(strategy_file_path, 'w', encoding='utf-8') as f:
        f.write(strategy_code)
    
    print(f"最终盈利策略已保存到: {strategy_file_path}")
    return strategy_file_path

def update_strategy_library_final():
    """更新策略库，添加最终盈利策略"""
    
    strategy_lib_path = os.path.join(CORE_CODE_PATH, '交易策略', '策略库.py')
    
    # 读取现有策略库文件
    with open(strategy_lib_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加最终盈利策略导入
    import_line = "from .final_profitable_strategy import FinalProfitableStrategy"
    
    if import_line not in content:
        # 在文件末尾添加
        additional_content = f'''

# 最终盈利策略导入
{import_line}

# 更新策略字典
STRATEGIES['FinalProfitableStrategy'] = FinalProfitableStrategy
'''
        content += additional_content
        
        # 写回文件
        with open(strategy_lib_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"策略库已更新: {strategy_lib_path}")

if __name__ == '__main__':
    print("创建最终盈利均值回归策略")
    print("=" * 60)
    
    # 创建最终策略
    create_final_profitable_strategy()
    update_strategy_library_final()
    
    print("\n✅ 最终盈利策略创建完成！")
    print("\n🎯 核心优化：")
    print("• 提高价格偏离阈值：1.0% → 1.2%（更严格）")
    print("• 提高盈亏比：1.5:1 → 2:1（3.6ATR止盈，1.8ATR止损）")
    print("• 降低单笔风险：1.0% → 0.8%（更安全）")
    print("• 延长信号间隔：6小时 → 8小时（更精选）")
    print("• 增强质量控制：波动率、趋势、市场时机")
    
    print("\n📊 目标设定：")
    print("• 交易频率：20-25次/月（平均每天1次）")
    print("• 胜率目标：≥50%")
    print("• 盈亏比：2:1")
    print("• 年化收益：≥10%")
    print("• 最大回撤：≤8%")
    
    print("\n🧪 测试命令：")
    print("./quant_env/Scripts/python.exe 模拟回测引擎_分钟级.py --start_date 2024-04-01 --end_date 2024-04-30 --strategy FinalProfitableStrategy")
