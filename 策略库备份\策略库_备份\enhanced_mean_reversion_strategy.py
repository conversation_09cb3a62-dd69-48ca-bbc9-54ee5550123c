# -*- coding: utf-8 -*-
"""
增强版均值回归策略 - 在保持质量的同时提高交易频率
主要改进：
1. 降低价格偏离阈值：2.5% → 2.0%
2. 放宽RSI条件：30 → 35
3. 缩短信号间隔：120 → 90分钟
4. 增加交易机会：每日最多6次
5. 添加多重确认机制
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class EnhancedMeanReversionStrategy:
    """增强版均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'EnhancedMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 增强的技术指标参数
        self.sma_short_key = all_params.get('sma_short_key', 'SMA_20')
        self.sma_long_key = all_params.get('sma_long_key', 'SMA_60')
        self.price_deviation_pct = all_params.get('price_deviation_pct', 0.02)  # 降低至2.0%
        
        # 放宽的RSI参数
        self.rsi_key = all_params.get('rsi_key', 'RSI_14')
        self.rsi_oversold = all_params.get('rsi_oversold', 35)  # 放宽至35
        
        # 风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.008)  # 略微降低风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 1.8)  # 缩小止损
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 2.8)  # 缩小止盈，保持盈亏比
        
        # 增强的交易频率控制
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 90)  # 缩短至90分钟
        self.max_daily_trades = all_params.get('max_daily_trades', 6)  # 增加至6次
        
        # 新增多重确认
        self.volume_threshold = all_params.get('volume_threshold', 1.05)  # 轻微成交量要求
        self.trend_strength_threshold = all_params.get('trend_strength_threshold', 0.08)  # 放宽趋势强度
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        print(f"策略 EnhancedMeanReversionStrategy 初始化...")
        print(f"EnhancedMeanReversionStrategy: 价格偏离={self.price_deviation_pct*100}%, "
              f"RSI≤{self.rsi_oversold}, 盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}, "
              f"信号间隔={self.min_signal_interval_minutes}分钟")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_price_deviation(self, data: Dict[str, Any]) -> bool:
        """检查价格是否偏离均线足够远（降低阈值）"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        
        if price <= 0 or sma_short <= 0:
            return False
        
        # 价格低于短期均线一定百分比时，认为是均值回归机会
        deviation = (sma_short - price) / sma_short
        return deviation >= self.price_deviation_pct
    
    def check_trend_not_too_strong(self, data: Dict[str, Any]) -> bool:
        """检查趋势不要太强（放宽条件）"""
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if sma_short <= 0 or sma_long <= 0:
            return True  # 如果没有数据，跳过检查
        
        # 短期均线和长期均线差距不要太大（放宽至8%）
        trend_strength = abs(sma_short - sma_long) / sma_long
        return trend_strength <= self.trend_strength_threshold
    
    def check_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查成交量确认（轻微要求）"""
        current_volume = data.get('VOLUME', 0)
        avg_volume = data.get('VOLUME_SMA_20', current_volume)
        
        if avg_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        volume_ratio = current_volume / avg_volume
        return volume_ratio >= self.volume_threshold
    
    def check_additional_confirmation(self, data: Dict[str, Any]) -> bool:
        """额外确认条件"""
        price = data.get('CLOSE', 0)
        sma_short = data.get(self.sma_short_key, 0)
        sma_long = data.get(self.sma_long_key, 0)
        
        if price <= 0 or sma_short <= 0 or sma_long <= 0:
            return True
        
        # 价格在两个均线之间或略低于短期均线时，增加机会
        if sma_long <= price <= sma_short:
            return True
        
        # 价格低于短期均线但不超过3%
        if price < sma_short:
            deviation = (sma_short - price) / sma_short
            return deviation <= 0.03
        
        return False
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 增强版均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.sma_short_key]
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        sma_short = data.get(self.sma_short_key)
        rsi = data.get(self.rsi_key, 50)  # 默认中性RSI
        atr = data.get('ATR_14', price * 0.02)  # 默认2%的ATR
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, sma_short]):
            return signals
        
        # 增强的买入条件（更宽松）
        buy_conditions = [
            self.check_price_deviation(data) or self.check_additional_confirmation(data),  # 主要条件或额外确认
            self.check_trend_not_too_strong(data),  # 趋势不要太强
            self.check_volume_confirmation(data),  # 成交量确认
        ]
        
        # RSI条件（如果有数据）
        if not pd.isna(rsi) and rsi != 50:
            buy_conditions.append(rsi <= self.rsi_oversold)
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            # 计算价格偏离度
            deviation = (sma_short - price) / sma_short * 100
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'EnhancedMeanReversionStrategy',
                'signal_type': 'enhanced_mean_reversion',
                'reason': f'增强均值回归: 偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}vs SMA={sma_short:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] EnhancedMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"偏离={deviation:.2f}%, RSI={rsi:.2f}, 价格={price:.2f}vs SMA={sma_short:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'EnhancedMeanReversionStrategy',
            'version': '1.0',
            'description': '增强版均值回归策略，在保持质量的同时提高交易频率',
            'enhancements': [
                '降低价格偏离阈值至2.0%',
                '放宽RSI条件至35',
                '缩短信号间隔至90分钟',
                '增加每日交易机会至6次',
                '添加多重确认机制'
            ],
            'parameters': {
                'price_deviation_pct': self.price_deviation_pct,
                'rsi_oversold': self.rsi_oversold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
