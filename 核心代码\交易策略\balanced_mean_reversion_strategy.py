# -*- coding: utf-8 -*-
"""
平衡版均值回归策略 - 在减少过度交易和保持交易机会之间找到平衡
主要改进：
1. 适度严格的入场条件（布林带2.2倍标准差，RSI≤25）
2. 更好的盈亏比（止损1.8ATR，止盈3.5ATR）
3. 适度的交易频率控制（信号间隔120分钟）
4. 保留成交量确认但降低要求
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class BalancedMeanReversionStrategy:
    """平衡版均值回归策略"""
    
    def __init__(self, engine=None, symbol_list=None, params=None, **kwargs):
        """初始化策略参数"""
        self.engine = engine
        self.symbol_list = symbol_list or ['BTCUSDT']
        self.strategy_name = 'BalancedMeanReversionStrategy'
        
        # 合并参数
        all_params = {}
        if params:
            all_params.update(params)
        all_params.update(kwargs)
        
        # 平衡后的布林带参数
        self.bbands_period = all_params.get('bbands_period', 25)  # 适度增加周期
        self.bbands_std_dev = all_params.get('bbands_std_dev', 2.2)  # 适度严格
        
        # 平衡后的RSI参数
        self.rsi_period = all_params.get('rsi_period', 14)
        self.rsi_oversold = all_params.get('rsi_oversold', 25)  # 适度严格
        
        # 趋势过滤参数（放宽）
        self.adx_period = all_params.get('adx_period', 14)
        self.adx_max_threshold = all_params.get('adx_max_threshold', 30)  # 放宽震荡市条件
        
        # 平衡后的风险管理参数
        self.risk_per_trade_pct = all_params.get('risk_per_trade_pct', 0.008)  # 适度风险
        self.atr_sl_multiple = all_params.get('atr_sl_multiple', 1.8)  # 适度止损
        self.atr_tp_multiple = all_params.get('atr_tp_multiple', 3.5)  # 良好盈亏比
        
        # 交易频率控制（适度）
        self.min_signal_interval_minutes = all_params.get('min_signal_interval_minutes', 120)  # 2小时间隔
        self.max_daily_trades = all_params.get('max_daily_trades', 4)  # 每日最多4次
        
        # 适度的过滤条件
        self.volume_threshold = all_params.get('volume_threshold', 1.1)  # 降低成交量要求
        self.max_consecutive_losses = all_params.get('max_consecutive_losses', 4)  # 适度保护
        
        # 状态变量
        self.last_signal_time = None
        self.daily_trade_count = 0
        self.last_trade_date = None
        self.consecutive_losses = 0
        
        # 构建指标键名
        self.bb_lower_key = f"BBands_{self.bbands_period}_{str(self.bbands_std_dev).replace('.', '_')}_BB_Lower"
        self.bb_middle_key = f"BBands_{self.bbands_period}_{str(self.bbands_std_dev).replace('.', '_')}_BB_Middle"
        self.rsi_key = f"RSI_{self.rsi_period}"
        self.adx_key = f"ADX_{self.adx_period}"
        self.atr_key = 'ATR_14'
        
        print(f"策略 BalancedMeanReversionStrategy 初始化...")
        print(f"BalancedMeanReversionStrategy: 布林带={self.bbands_period}期{self.bbands_std_dev}倍, "
              f"RSI≤{self.rsi_oversold}, ADX≤{self.adx_max_threshold}, 盈亏比={self.atr_tp_multiple}:{self.atr_sl_multiple}")
    
    def check_signal_interval(self, current_time: datetime) -> bool:
        """检查信号间隔"""
        if self.last_signal_time is None:
            return True
        
        time_diff = (current_time - self.last_signal_time).total_seconds() / 60
        return time_diff >= self.min_signal_interval_minutes
    
    def check_daily_trade_limit(self, current_time: datetime) -> bool:
        """检查每日交易次数限制"""
        current_date = current_time.date()
        
        if self.last_trade_date != current_date:
            self.daily_trade_count = 0
            self.last_trade_date = current_date
        
        return self.daily_trade_count < self.max_daily_trades
    
    def check_consecutive_loss_protection(self) -> bool:
        """检查连续亏损保护"""
        return self.consecutive_losses < self.max_consecutive_losses
    
    def check_volume_confirmation(self, data: Dict[str, Any]) -> bool:
        """检查成交量确认（降低要求）"""
        current_volume = data.get('VOLUME', 0)
        avg_volume = data.get('VOLUME_SMA_20', current_volume)
        
        if avg_volume <= 0:
            return True  # 如果没有成交量数据，跳过检查
        
        volume_ratio = current_volume / avg_volume
        return volume_ratio >= self.volume_threshold
    
    def check_market_regime(self, data: Dict[str, Any]) -> bool:
        """检查市场状态（放宽条件）"""
        adx = data.get(self.adx_key, 0)
        
        # 在适度震荡市时进行均值回归交易
        return adx <= self.adx_max_threshold
    
    def on_bar(self, current_bar_data):
        """回测引擎接口方法"""
        signals = []
        
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index:
                continue
                
            # 检查是否已有持仓
            if hasattr(self.engine, 'get_position_size') and abs(self.engine.get_position_size(symbol)) > 1e-9:
                continue
            
            data = current_bar_data.loc[symbol].to_dict()
            current_time = self.engine.current_dt if self.engine else datetime.now()
            
            symbol_signals = self.generate_signals(data, current_time)
            signals.extend(symbol_signals)
        
        return signals
    
    def generate_signals(self, data: Dict[str, Any], current_time: datetime) -> List[Dict[str, Any]]:
        """生成交易信号 - 平衡版均值回归"""
        signals = []
        
        # 检查基础数据完整性
        required_keys = ['CLOSE', self.bb_lower_key, self.rsi_key, self.atr_key]
        if not all(key in data for key in required_keys):
            return signals
        
        # 检查各种限制条件
        if not self.check_signal_interval(current_time):
            return signals
        
        if not self.check_daily_trade_limit(current_time):
            return signals
        
        if not self.check_consecutive_loss_protection():
            return signals
        
        # 获取技术指标
        price = data.get('CLOSE')
        bb_lower = data.get(self.bb_lower_key)
        rsi = data.get(self.rsi_key)
        adx = data.get(self.adx_key, 0)  # ADX可选
        atr = data.get(self.atr_key)
        
        # 检查数据有效性
        if any(x is None or pd.isna(x) or x <= 0 for x in [price, bb_lower, rsi, atr]):
            return signals
        
        # 平衡后的买入条件
        buy_conditions = [
            price <= bb_lower * 1.005,  # 价格接近或低于布林带下轨（允许5‰误差）
            rsi <= self.rsi_oversold,  # RSI超卖
            self.check_volume_confirmation(data),  # 成交量确认（降低要求）
        ]
        
        # 可选的市场状态过滤
        if not pd.isna(adx) and adx > 0:
            buy_conditions.append(self.check_market_regime(data))
        
        if all(buy_conditions):
            # 计算仓位大小
            portfolio_value = 100000  # 假设组合价值
            risk_amount = portfolio_value * self.risk_per_trade_pct
            position_size = risk_amount / (atr * self.atr_sl_multiple)
            
            # 计算止损止盈价格
            stop_loss = price - (atr * self.atr_sl_multiple)
            take_profit = price + (atr * self.atr_tp_multiple)
            
            signal = {
                'symbol': 'BTCUSDT',
                'action': 'buy',
                'size': position_size,
                'price': price,
                'stop_loss_price': stop_loss,
                'take_profit_price': take_profit,
                'timestamp': current_time,
                'strategy': 'BalancedMeanReversionStrategy',
                'signal_type': 'balanced_mean_reversion',
                'reason': f'平衡均值回归: RSI={rsi:.2f}, ADX={adx:.2f}, 价格={price:.2f}≤BB下轨={bb_lower:.2f}'
            }
            
            signals.append(signal)
            self.last_signal_time = current_time
            self.daily_trade_count += 1
            
            print(f"[{current_time}] BalancedMeanReversionStrategy: 买入信号 BTCUSDT, "
                  f"RSI={rsi:.2f}, ADX={adx:.2f}, 价格={price:.2f}≤BB下轨={bb_lower:.2f}, 数量={position_size:.4f}")
        
        return signals
    
    def on_trade_closed(self, trade_result: Dict[str, Any]):
        """交易结束回调，用于更新连续亏损计数"""
        pnl = trade_result.get('pnl', 0)
        
        if pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0  # 盈利后重置连续亏损计数
        
        print(f"BalancedMeanReversionStrategy: 交易结束，PnL={pnl:.2f}, 连续亏损次数={self.consecutive_losses}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'BalancedMeanReversionStrategy',
            'version': '1.0',
            'description': '平衡版均值回归策略，在减少过度交易和保持交易机会之间找到平衡',
            'balance_points': [
                '适度严格的入场条件',
                '良好的盈亏比（1.94:1）',
                '适度的交易频率控制',
                '灵活的市场状态过滤',
                '合理的风险管理'
            ],
            'parameters': {
                'bbands_period': self.bbands_period,
                'bbands_std_dev': self.bbands_std_dev,
                'rsi_oversold': self.rsi_oversold,
                'adx_max_threshold': self.adx_max_threshold,
                'risk_per_trade_pct': self.risk_per_trade_pct,
                'atr_sl_multiple': self.atr_sl_multiple,
                'atr_tp_multiple': self.atr_tp_multiple,
                'min_signal_interval_minutes': self.min_signal_interval_minutes,
                'max_daily_trades': self.max_daily_trades
            },
            'status': {
                'consecutive_losses': self.consecutive_losses,
                'daily_trade_count': self.daily_trade_count,
                'last_signal_time': self.last_signal_time
            }
        }
