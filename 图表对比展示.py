# -*- coding: utf-8 -*-
"""
图表对比展示 - 新旧回测报告图表对比
"""
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import os
from datetime import datetime

def show_chart_comparison():
    """展示新旧图表对比"""
    
    print("📊 回测报告图表升级对比")
    print("=" * 60)
    
    # 检查专业报告文件是否存在
    professional_report = "专业回测报告_AlphaXInspiredStrategy.png"
    
    if os.path.exists(professional_report):
        print(f"✅ 专业回测报告已生成: {professional_report}")
        
        # 显示文件信息
        file_size = os.path.getsize(professional_report) / 1024  # KB
        file_time = datetime.fromtimestamp(os.path.getmtime(professional_report))
        
        print(f"📁 文件大小: {file_size:.1f} KB")
        print(f"🕒 生成时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建对比图表
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
            
            # 加载专业报告图片
            img = mpimg.imread(professional_report)
            ax1.imshow(img)
            ax1.set_title("🎨 新版专业回测报告", fontsize=16, fontweight='bold', color='#2e7d32')
            ax1.axis('off')
            
            # 在右侧显示特点说明
            ax2.axis('off')
            
            features_text = """
🎯 专业回测报告特点

📊 顶部指标面板
• 策略收益、年化收益、超额收益
• 夏普比率、贝塔、信息比率
• 胜率、波动率、最大回撤
• 专业的颜色编码（红绿区分）

📈 主图表区域
• 蓝色线：策略净值曲线
• 红色线：基准对比曲线
• 专业的网格线和标注
• 清晰的时间轴和百分比刻度

📋 详细指标面板
• 日均收益、最大收益
• 盈利次数、亏损次数
• 各种风险指标
• 回撤区间信息

🎛️ 时间范围选择器
• 多种时间范围选项
• 对数坐标选项
• 日期范围显示

📊 底部统计信息
• 交易统计汇总
• 持仓时间分析
• 连续亏损统计

🎨 专业设计风格
• 仿照专业量化平台
• 清晰的布局和配色
• 丰富的信息展示
• 便于客户展示
            """
            
            ax2.text(0.05, 0.95, features_text, transform=ax2.transAxes, 
                    fontsize=12, verticalalignment='top', fontfamily='monospace',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor='#f5f5f5', alpha=0.8))
            
            plt.suptitle("回测报告图表升级对比", fontsize=18, fontweight='bold', y=0.95)
            plt.tight_layout()
            
            # 保存对比图
            comparison_file = "回测报告图表升级对比.png"
            plt.savefig(comparison_file, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            
            print(f"✅ 对比图表已保存: {comparison_file}")
            plt.show()
            
        except Exception as e:
            print(f"❌ 创建对比图表失败: {e}")
    
    else:
        print(f"❌ 专业回测报告文件不存在: {professional_report}")
    
    print("\n🎉 图表升级总结:")
    print("   • 从简单的单线图升级为专业的多面板布局")
    print("   • 增加了丰富的量化指标展示")
    print("   • 添加了策略vs基准对比功能")
    print("   • 采用了专业的配色和样式")
    print("   • 提供了更好的用户体验")
    
    print(f"\n📄 升级完成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def show_integration_summary():
    """显示集成总结"""
    
    print("\n🔧 集成到回测引擎总结")
    print("=" * 60)
    
    integration_summary = """
✅ 成功集成专业回测报告模块到现有回测引擎

🔄 集成方式：
1. 创建了 ProfessionalBacktestReporter 类
2. 修改了 MinuteEventBacktester._plot_results 方法
3. 添加了错误处理和回退机制
4. 保持了向后兼容性

📁 文件结构：
• 专业回测报告图表模块.py - 核心报告生成器
• 模拟回测引擎_分钟级.py - 已集成新报告功能

🎯 使用方法：
1. 运行回测引擎时自动生成专业报告
2. 如果专业报告生成失败，自动回退到简单图表
3. 报告保存在指定目录，便于查看和分享

🚀 优势：
• 无需修改现有策略代码
• 自动适配所有策略类型
• 提供了专业级的可视化效果
• 便于客户展示和分析
    """
    
    print(integration_summary)

if __name__ == '__main__':
    print("回测报告图表升级展示")
    print("=" * 60)
    
    # 显示图表对比
    show_chart_comparison()
    
    # 显示集成总结
    show_integration_summary()
    
    print("\n💡 下一步建议：")
    print("   • 可以进一步定制化指标面板")
    print("   • 添加更多基准对比选项")
    print("   • 支持多策略对比功能")
    print("   • 添加交互式图表功能")
