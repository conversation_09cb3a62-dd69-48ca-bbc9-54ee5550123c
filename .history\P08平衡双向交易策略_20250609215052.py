# -*- coding: utf-8 -*-
"""
P08平衡双向交易策略
在信号质量和交易频率之间找到最佳平衡点
确保既有足够的交易机会，又保持较高的信号质量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class BalancedBidirectionalStrategy:
    """P08平衡双向交易策略 - 质量与频率的最佳平衡"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 平衡优化的策略参数
        self.strategy_params = {
            # 技术指标参数 (适中设置)
            'ema_fast': 12,                # 快速EMA
            'ema_slow': 26,                # 慢速EMA
            'rsi_period': 14,              # RSI周期
            'macd_signal': 9,              # MACD信号线
            
            # 平衡的信号阈值
            'long_rsi_min': 35,            # 做多RSI最小值
            'long_rsi_max': 75,            # 做多RSI最大值
            'long_momentum_min': 0.003,    # 做多最小动量
            'long_volume_min': 1.0,        # 做多最小成交量比
            
            'short_rsi_min': 25,           # 做空RSI最小值
            'short_rsi_max': 65,           # 做空RSI最大值
            'short_momentum_max': -0.003,  # 做空最大动量
            'short_volume_min': 1.0,       # 做空最小成交量比
            
            # 信号质量控制 (放宽)
            'min_signal_strength': 0.8,    # 最小信号强度 (降低)
            'signal_confirmation_bars': 2, # 信号确认K线数 (减少)
            'max_signals_per_day': 8,      # 每日最大信号数
            
            # 仓位管理
            'base_position': 0.2,          # 基础仓位
            'max_position': 0.4,           # 最大仓位
            'risk_per_trade': 0.02,        # 每笔风险
            
            # 风控参数
            'stop_loss_base': 0.025,       # 基础止损2.5%
            'take_profit_base': 0.05,      # 基础止盈5%
            'trailing_stop': True,         # 移动止损
            
            # 交易控制
            'min_signal_gap': 60,          # 最小信号间隔1小时
            'max_holding_hours': 12,       # 最大持仓12小时
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_balanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算平衡的技术指标"""
        try:
            df = data.copy()
            
            # EMA系统
            df['EMA_Fast'] = df['CLOSE'].ewm(span=self.strategy_params['ema_fast']).mean()
            df['EMA_Slow'] = df['CLOSE'].ewm(span=self.strategy_params['ema_slow']).mean()
            
            # MACD
            df['MACD'] = df['EMA_Fast'] - df['EMA_Slow']
            df['MACD_Signal'] = df['MACD'].ewm(span=self.strategy_params['macd_signal']).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 动量指标
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            return df
            
        except Exception as e:
            logger.warning(f"计算平衡指标失败: {e}")
            return data
    
    def generate_balanced_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成平衡的交易信号"""
        try:
            # 初始化信号
            df['Long_Signal'] = 0
            df['Short_Signal'] = 0
            df['Signal_Quality'] = 0
            
            # 做多信号 (简化但有效)
            long_conditions = (
                # 基础趋势
                (df['EMA_Fast'] > df['EMA_Slow']) &
                (df['MACD'] > df['MACD_Signal']) &
                
                # RSI条件
                (df['RSI'] >= self.strategy_params['long_rsi_min']) &
                (df['RSI'] <= self.strategy_params['long_rsi_max']) &
                
                # 动量条件
                (df['Momentum_5'] > self.strategy_params['long_momentum_min']) &
                
                # 成交量条件
                (df['Volume_Ratio'] > self.strategy_params['long_volume_min']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.005) & (df['Volatility'] < 0.1)
            )
            
            # 做空信号 (简化但有效)
            short_conditions = (
                # 基础趋势
                (df['EMA_Fast'] < df['EMA_Slow']) &
                (df['MACD'] < df['MACD_Signal']) &
                
                # RSI条件
                (df['RSI'] >= self.strategy_params['short_rsi_min']) &
                (df['RSI'] <= self.strategy_params['short_rsi_max']) &
                
                # 动量条件
                (df['Momentum_5'] < self.strategy_params['short_momentum_max']) &
                
                # 成交量条件
                (df['Volume_Ratio'] > self.strategy_params['short_volume_min']) &
                
                # 波动率过滤
                (df['Volatility'] > 0.005) & (df['Volatility'] < 0.1)
            )
            
            # 简化的信号确认
            confirm_bars = self.strategy_params['signal_confirmation_bars']
            
            # 做多信号确认
            for i in range(confirm_bars, len(df)):
                if long_conditions.iloc[i] and long_conditions.iloc[i-1]:
                    df.iloc[i, df.columns.get_loc('Long_Signal')] = 1
            
            # 做空信号确认
            for i in range(confirm_bars, len(df)):
                if short_conditions.iloc[i] and short_conditions.iloc[i-1]:
                    df.iloc[i, df.columns.get_loc('Short_Signal')] = 1
            
            # 计算信号质量 (简化)
            df['Signal_Quality'] = (
                abs(df['MACD_Histogram']) * 500 * 0.4 +
                abs(df['Momentum_5']) * 100 * 0.3 +
                (df['Volume_Ratio'] - 1) * 0.2 +
                (50 - abs(df['RSI'] - 50)) / 50 * 0.1
            )
            
            return df
            
        except Exception as e:
            logger.error(f"生成平衡信号失败: {e}")
            return df

    def simulate_balanced_strategy(self, data: pd.DataFrame) -> dict:
        """模拟平衡双向交易策略"""
        try:
            print("🔄 模拟P08平衡双向交易策略...")

            # 计算技术指标
            data = self.calculate_balanced_indicators(data)
            data = self.generate_balanced_signals(data)

            # 初始化
            initial_capital = 100000
            cash = initial_capital
            long_position = 0
            short_position = 0
            trades = []
            equity_curve = [initial_capital]

            # 交易控制变量
            last_trade_time = None
            long_entry_time = None
            short_entry_time = None
            long_entry_price = 0
            short_entry_price = 0
            daily_signals = {}

            for i in range(50, len(data)):  # 从第50个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']

                # 计算当前权益
                long_value = long_position * current_price if long_position > 0 else 0
                short_value = short_position * (2 * short_entry_price - current_price) if short_position > 0 else 0
                current_equity = cash + long_value + short_value
                equity_curve.append(current_equity)

                # 检查数据有效性
                if pd.isna(current_price):
                    continue

                # 每日信号限制
                current_date = current_time.date()
                if current_date not in daily_signals:
                    daily_signals[current_date] = 0

                if daily_signals[current_date] >= self.strategy_params['max_signals_per_day']:
                    continue

                # 检查信号间隔
                if last_trade_time:
                    time_diff = (current_time - last_trade_time).total_seconds() / 60
                    if time_diff < self.strategy_params['min_signal_gap']:
                        continue

                # 获取信号
                long_signal = current_data.get('Long_Signal', 0)
                short_signal = current_data.get('Short_Signal', 0)
                signal_quality = current_data.get('Signal_Quality', 0)

                # 信号质量过滤
                if signal_quality < self.strategy_params['min_signal_strength']:
                    continue

                # 检查做多信号
                if long_signal == 1:
                    # 平空头仓位
                    if short_position > 0:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': '反向信号平仓'
                        })

                        short_position = 0
                        short_entry_time = None

                    # 开多头仓位
                    if long_position == 0:
                        position_size = self.strategy_params['base_position']

                        # 根据信号质量调整仓位
                        if signal_quality > 1.5:
                            position_size = min(position_size * 1.5, self.strategy_params['max_position'])

                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            long_position = shares
                            cash -= shares * current_price * 1.0005
                            long_entry_time = current_time
                            long_entry_price = current_price

                            trades.append({
                                'time': current_time,
                                'action': 'buy_long',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'stop_loss': current_price * (1 - self.strategy_params['stop_loss_base']),
                                'take_profit': current_price * (1 + self.strategy_params['take_profit_base'])
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 检查做空信号
                elif short_signal == 1:
                    # 平多头仓位
                    if long_position > 0:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': '反向信号平仓'
                        })

                        long_position = 0
                        long_entry_time = None

                    # 开空头仓位
                    if short_position == 0:
                        position_size = self.strategy_params['base_position']

                        # 根据信号质量调整仓位
                        if signal_quality > 1.5:
                            position_size = min(position_size * 1.5, self.strategy_params['max_position'])

                        position_value = cash * position_size
                        shares = position_value / current_price

                        if shares > 0:
                            short_position = shares
                            cash -= shares * current_price * 1.0005
                            short_entry_time = current_time
                            short_entry_price = current_price

                            trades.append({
                                'time': current_time,
                                'action': 'sell_short',
                                'price': current_price,
                                'shares': shares,
                                'signal_quality': signal_quality,
                                'stop_loss': current_price * (1 + self.strategy_params['stop_loss_base']),
                                'take_profit': current_price * (1 - self.strategy_params['take_profit_base'])
                            })

                            last_trade_time = current_time
                            daily_signals[current_date] += 1

                # 多头出场逻辑
                if long_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'buy_long'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price > long_entry_price * 1.02:
                            new_stop = current_price * 0.985
                            stop_loss = max(stop_loss, new_stop)

                    holding_hours = (current_time - long_entry_time).total_seconds() / 3600

                    should_exit = False
                    exit_reason = ""

                    if current_price <= stop_loss:
                        should_exit = True
                        exit_reason = "多头止损"
                    elif current_price >= take_profit:
                        should_exit = True
                        exit_reason = "多头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "多头超时"

                    if should_exit:
                        long_pnl = (current_price - long_entry_price) * long_position
                        cash += long_position * current_price * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'sell_long',
                            'price': current_price,
                            'shares': long_position,
                            'pnl': long_pnl,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        long_position = 0
                        long_entry_time = None
                        last_trade_time = current_time

                # 空头出场逻辑
                if short_position > 0:
                    last_trade = [t for t in trades if t['action'] == 'sell_short'][-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']

                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price < short_entry_price * 0.98:
                            new_stop = current_price * 1.015
                            stop_loss = min(stop_loss, new_stop)

                    holding_hours = (current_time - short_entry_time).total_seconds() / 3600

                    should_exit = False
                    exit_reason = ""

                    if current_price >= stop_loss:
                        should_exit = True
                        exit_reason = "空头止损"
                    elif current_price <= take_profit:
                        should_exit = True
                        exit_reason = "空头止盈"
                    elif holding_hours > self.strategy_params['max_holding_hours']:
                        should_exit = True
                        exit_reason = "空头超时"

                    if should_exit:
                        short_pnl = short_position * (short_entry_price - current_price)
                        cash += short_position * short_entry_price + short_pnl * 0.9995

                        trades.append({
                            'time': current_time,
                            'action': 'cover_short',
                            'price': current_price,
                            'shares': short_position,
                            'pnl': short_pnl,
                            'reason': exit_reason,
                            'holding_hours': holding_hours
                        })

                        short_position = 0
                        short_entry_time = None
                        last_trade_time = current_time
