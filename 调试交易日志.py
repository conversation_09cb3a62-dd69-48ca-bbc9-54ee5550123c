# -*- coding: utf-8 -*-
"""
调试交易日志 - 检查交易记录的详细信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s]: %(message)s'
)

logger = logging.getLogger(__name__)

def debug_single_strategy():
    """调试单个策略的交易日志"""
    
    print("🔍 调试交易日志 - TrendFollowing策略")
    print("=" * 60)
    
    try:
        from 模拟回测引擎_分钟级 import MinuteEventBacktester
        from 配置.系统配置 import Config
        from 核心代码.交易策略.策略库 import STRATEGIES
        
        # 创建配置
        config = Config()
        config.start_date = '2025-04-01'
        config.end_date = '2025-04-03'  # 短期测试
        config.initial_cash = 100000
        config.cost = 0.0005
        config.crypto_pairs = ['BTCUSDT']
        config.benchmark_symbol = 'BTCUSDT'
        
        # 获取策略类
        strategy_name = 'TrendFollowing'
        strategy_class = STRATEGIES.get(strategy_name)
        
        if not strategy_class:
            print(f"❌ 策略 {strategy_name} 不可用")
            return
        
        # 宽松的策略参数
        strategy_params = {
            'sma_short': 10,
            'sma_long': 30,
            'rsi_threshold': 45,
            'risk_per_trade_pct': 0.02,
            'atr_sl_multiple': 1.5,
            'atr_tp_multiple': 3.0,
            'min_signal_interval_minutes': 30
        }
        
        print(f"测试策略: {strategy_name}")
        print(f"测试时间: {config.start_date} 到 {config.end_date}")
        print(f"策略参数: {strategy_params}")
        
        # 创建回测引擎
        backtester = MinuteEventBacktester(config, strategy_class, strategy_params)
        
        # 运行回测
        results = backtester.run_backtest(config.start_date, config.end_date)
        
        # 检查交易日志
        trade_log = backtester.portfolio.trade_log
        
        print(f"\n📊 交易日志分析:")
        print(f"总交易记录数: {len(trade_log)}")
        
        if trade_log:
            # 转换为DataFrame便于分析
            df = pd.DataFrame(trade_log)
            
            print(f"\n📋 交易记录详情:")
            print(df.to_string(index=False))
            
            # 分析交易类型
            print(f"\n📈 交易类型统计:")
            action_counts = df['action'].value_counts()
            for action, count in action_counts.items():
                print(f"  {action}: {count}次")
            
            # 分析盈亏
            print(f"\n💰 盈亏分析:")
            total_pnl = df['realized_pnl'].sum()
            print(f"总盈亏: {total_pnl:.2f}")
            
            # 分析平仓交易
            closing_trades = df[df['action'] == 'sell_to_close']
            print(f"\n🔚 平仓交易分析:")
            print(f"平仓交易数: {len(closing_trades)}")
            
            if not closing_trades.empty:
                profitable_trades = closing_trades[closing_trades['realized_pnl'] > 0]
                losing_trades = closing_trades[closing_trades['realized_pnl'] < 0]
                
                print(f"盈利交易: {len(profitable_trades)}次")
                print(f"亏损交易: {len(losing_trades)}次")
                print(f"胜率: {len(profitable_trades)/len(closing_trades)*100:.1f}%")
                
                if len(profitable_trades) > 0:
                    print(f"平均盈利: {profitable_trades['realized_pnl'].mean():.2f}")
                if len(losing_trades) > 0:
                    print(f"平均亏损: {losing_trades['realized_pnl'].mean():.2f}")
            else:
                print("❌ 没有平仓交易！这就是问题所在。")
        
        # 检查最终结果
        print(f"\n📊 回测结果:")
        if results:
            for key, value in results.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value}")
                else:
                    print(f"  {key}: {value}")
        else:
            print("❌ 没有返回结果")
        
        # 检查持仓状态
        print(f"\n📦 最终持仓状态:")
        positions = backtester.portfolio.positions
        print(f"持仓数量: {len(positions)}")
        for symbol, pos_info in positions.items():
            print(f"  {symbol}: {pos_info}")
        
        return trade_log, results
        
    except Exception as e:
        logger.error(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def analyze_trade_log_issue():
    """分析交易日志问题"""
    
    print("\n🔧 分析交易日志问题")
    print("=" * 60)
    
    trade_log, results = debug_single_strategy()
    
    if trade_log is None:
        print("❌ 无法获取交易日志")
        return
    
    if not trade_log:
        print("❌ 交易日志为空")
        return
    
    df = pd.DataFrame(trade_log)
    
    # 检查是否有开仓但没有平仓的情况
    buy_trades = df[df['action'] == 'buy_to_open']
    sell_trades = df[df['action'] == 'sell_to_close']
    
    print(f"\n🔍 问题诊断:")
    print(f"开仓交易数: {len(buy_trades)}")
    print(f"平仓交易数: {len(sell_trades)}")
    
    if len(buy_trades) > len(sell_trades):
        print(f"⚠️ 发现问题: 有 {len(buy_trades) - len(sell_trades)} 个开仓交易没有对应的平仓交易")
        print("这可能是因为:")
        print("1. 回测结束时仍有持仓未平仓")
        print("2. 止损/止盈机制没有正常工作")
        print("3. 策略没有生成卖出信号")
        
        # 检查最后的开仓交易
        if not buy_trades.empty:
            last_buy = buy_trades.iloc[-1]
            print(f"\n最后一次开仓:")
            print(f"  时间: {last_buy['timestamp']}")
            print(f"  价格: {last_buy['price']}")
            print(f"  数量: {last_buy['size']}")
    
    elif len(buy_trades) == len(sell_trades):
        print("✅ 开仓和平仓交易数量匹配")
    
    else:
        print("⚠️ 异常: 平仓交易数量超过开仓交易数量")

if __name__ == "__main__":
    try:
        analyze_trade_log_issue()
        
    except Exception as e:
        logger.error(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
