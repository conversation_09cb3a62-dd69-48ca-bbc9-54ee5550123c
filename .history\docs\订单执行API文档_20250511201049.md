# 订单执行模块API文档

## 1. OrderExecutor类

### 初始化
```python
def __init__(self, api_key: str = "demo_api_key", 
             base_url: str = "http://localhost:8000/trade",
             initial_capital: float = 100000.0)
```
参数说明：
- `api_key`: 交易API密钥
- `base_url`: 交易服务基础URL  
- `initial_capital`: 初始资金(元)

### 主要接口

#### 执行订单
```python
def execute_order(symbol: str, quantity: float, 
                order_type: str = 'market',
                limit_price: Optional[float] = None) -> Dict
```
参数说明：
- `symbol`: 交易标的代码 (如"AAPL")
- `quantity`: 数量(正数为买入，负数为卖出)
- `order_type`: 订单类型("market"或"limit")
- `limit_price`: 限价单价格(仅限价单需要)

返回值：
```json
{
  "order_id": "订单ID",
  "status": "订单状态(filled/rejected/new)",
  "executed_qty": "成交数量",
  "avg_price": "成交均价",
  "reason": "拒绝原因(可选)"
}
```

#### 带止损订单
```python 
def execute_with_stop_loss(symbol: str, quantity: float,
                          stop_loss_pct: float) -> Dict
```
参数说明：
- `stop_loss_pct`: 止损百分比(如0.01表示1%)

返回值：
```json
{
  "entry_order_id": "入场订单ID",
  "status": "订单状态",
  "stop_loss_price": "止损价格"
}
```

#### 订单状态查询
```python
def get_order_status(order_id: str) -> Dict
```

#### 取消订单
```python 
def cancel_order(order_id: str) -> Dict
```

## 2. 错误码

| 错误码 | 说明 |
|--------|------|
| 4001 | 无效的订单类型 |
| 4002 | 资金不足 |
| 4003 | 无效的数量 |
| 4004 | 流动性不足 |
| 4005 | 订单不存在 |
