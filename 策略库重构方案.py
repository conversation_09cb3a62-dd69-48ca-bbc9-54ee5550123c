# -*- coding: utf-8 -*-
"""
策略库重构方案
解决当前策略库命名混乱、分类不清、使用不便的问题
"""

import pandas as pd
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

# ==============================================================================
# 当前策略库问题分析
# ==============================================================================

CURRENT_STRATEGY_ISSUES = {
    "命名问题": [
        "MeanReversionStrategy - 太长",
        "TrendFollowingStrategy - 太长", 
        "OptimizedTrendStrategyAI - 太长",
        "AlphaXInspiredStrategy - 太长",
        "OptimizedAlphaXStrategy - 太长",
        "EnhancedAlphaXStrategyV2 - 太长",
        "AIOptimizedAlphaXStrategy - 太长",
        "SimplifiedAIAlphaXStrategy - 太长",
        "OptimizedMeanReversionStrategy - 太长",
        "BalancedMeanReversionStrategy - 太长",
        "AggressiveMeanReversionStrategy - 太长",
        "PracticalMeanReversionStrategy - 太长",
        "FinalProfitableStrategy - 太长",
        "ImprovedTrendFollowingStrategy - 太长",
        "MultiTimeframeTrendFollowingStrategy - 太长",
        "MLEnhancedTrendFollowingStrategy - 太长",
        "MonthlyProfitableStrategy - 太长",
        "UltraConservativeStrategy - 太长",
        "FixedMeanReversionStrategy - 太长"
    ],
    
    "分类问题": [
        "没有按策略类型分类",
        "没有按风险等级分类", 
        "没有按复杂度分类",
        "相似策略混在一起"
    ],
    
    "使用问题": [
        "用户难以快速找到合适策略",
        "不知道策略的特点和适用场景",
        "参数配置复杂",
        "缺乏推荐机制"
    ]
}

# ==============================================================================
# 新的策略分类体系
# ==============================================================================

STRATEGY_CATEGORIES = {
    # 基础策略 (Basic)
    "基础策略": {
        "代码": "B",
        "描述": "简单易懂的基础策略",
        "适用": "新手用户",
        "策略": [
            {"原名": "MeanReversionStrategy", "新名": "B01_均值回归", "简名": "均值回归"},
            {"原名": "TrendFollowingStrategy", "新名": "B02_趋势跟踪", "简名": "趋势跟踪"},
        ]
    },
    
    # 优化策略 (Optimized)  
    "优化策略": {
        "代码": "O",
        "描述": "经过参数优化的策略",
        "适用": "有经验用户",
        "策略": [
            {"原名": "OptimizedMeanReversionStrategy", "新名": "O01_优化均值回归", "简名": "优化均值回归"},
            {"原名": "BalancedMeanReversionStrategy", "新名": "O02_平衡均值回归", "简名": "平衡均值回归"},
            {"原名": "ImprovedTrendFollowingStrategy", "新名": "O03_改进趋势跟踪", "简名": "改进趋势跟踪"},
        ]
    },
    
    # AI策略 (AI-Enhanced)
    "AI策略": {
        "代码": "A", 
        "描述": "使用机器学习的智能策略",
        "适用": "高级用户",
        "策略": [
            {"原名": "OptimizedTrendStrategyAI", "新名": "A01_AI趋势预测", "简名": "AI趋势预测"},
            {"原名": "AIOptimizedAlphaXStrategy", "新名": "A02_AI优化阿尔法", "简名": "AI优化阿尔法"},
            {"原名": "MLEnhancedTrendFollowingStrategy", "新名": "A03_ML增强趋势", "简名": "ML增强趋势"},
        ]
    },
    
    # 高级策略 (Advanced)
    "高级策略": {
        "代码": "H",
        "描述": "复杂的高级策略",
        "适用": "专业用户", 
        "策略": [
            {"原名": "AlphaXInspiredStrategy", "新名": "H01_阿尔法X", "简名": "阿尔法X"},
            {"原名": "MultiTimeframeTrendFollowingStrategy", "新名": "H02_多时间框架", "简名": "多时间框架"},
            {"原名": "EnhancedAlphaXStrategyV2", "新名": "H03_增强阿尔法V2", "简名": "增强阿尔法V2"},
        ]
    },
    
    # 保守策略 (Conservative)
    "保守策略": {
        "代码": "C",
        "描述": "低风险保守策略",
        "适用": "风险厌恶用户",
        "策略": [
            {"原名": "UltraConservativeStrategy", "新名": "C01_超保守", "简名": "超保守"},
            {"原名": "PracticalMeanReversionStrategy", "新名": "C02_实用均值回归", "简名": "实用均值回归"},
        ]
    },
    
    # 激进策略 (Aggressive)
    "激进策略": {
        "代码": "G",
        "描述": "高风险高收益策略", 
        "适用": "风险偏好用户",
        "策略": [
            {"原名": "AggressiveMeanReversionStrategy", "新名": "G01_激进均值回归", "简名": "激进均值回归"},
        ]
    },
    
    # 特殊策略 (Special)
    "特殊策略": {
        "代码": "S",
        "描述": "特定目标的策略",
        "适用": "特定需求用户",
        "策略": [
            {"原名": "FinalProfitableStrategy", "新名": "S01_最终盈利", "简名": "最终盈利"},
            {"原名": "MonthlyProfitableStrategy", "新名": "S02_月月盈利", "简名": "月月盈利"},
            {"原名": "FixedMeanReversionStrategy", "新名": "S03_修复均值回归", "简名": "修复均值回归"},
        ]
    }
}

# ==============================================================================
# 策略推荐系统
# ==============================================================================

STRATEGY_RECOMMENDATIONS = {
    "新手推荐": {
        "策略": ["B01_均值回归", "B02_趋势跟踪"],
        "理由": "简单易懂，参数少，风险可控"
    },
    
    "稳健投资": {
        "策略": ["C01_超保守", "O02_平衡均值回归"],
        "理由": "风险较低，收益稳定"
    },
    
    "追求收益": {
        "策略": ["H01_阿尔法X", "A02_AI优化阿尔法"],
        "理由": "收益潜力大，但风险较高"
    },
    
    "技术流": {
        "策略": ["A01_AI趋势预测", "H02_多时间框架"],
        "理由": "使用先进技术，信号质量高"
    },
    
    "月度目标": {
        "策略": ["S02_月月盈利", "S01_最终盈利"],
        "理由": "专门针对月度盈利优化"
    }
}

# ==============================================================================
# 策略特征标签
# ==============================================================================

STRATEGY_TAGS = {
    "B01_均值回归": ["基础", "均值回归", "布林带", "RSI", "新手友好"],
    "B02_趋势跟踪": ["基础", "趋势跟踪", "移动平均", "ADX", "新手友好"],
    "O01_优化均值回归": ["优化", "均值回归", "参数优化", "风险控制"],
    "O02_平衡均值回归": ["优化", "均值回归", "平衡", "稳健"],
    "O03_改进趋势跟踪": ["优化", "趋势跟踪", "信号过滤", "动态止损"],
    "A01_AI趋势预测": ["AI", "机器学习", "趋势预测", "高级"],
    "A02_AI优化阿尔法": ["AI", "随机森林", "特征工程", "高级"],
    "A03_ML增强趋势": ["AI", "机器学习", "趋势跟踪", "信号质量"],
    "H01_阿尔法X": ["高级", "分批建仓", "动态止损", "趋势跟踪"],
    "H02_多时间框架": ["高级", "多时间框架", "趋势确认", "复杂"],
    "H03_增强阿尔法V2": ["高级", "增强版", "多功能", "复杂"],
    "C01_超保守": ["保守", "低风险", "稳健", "防守型"],
    "C02_实用均值回归": ["保守", "实用", "均值回归", "稳健"],
    "G01_激进均值回归": ["激进", "高风险", "高收益", "进攻型"],
    "S01_最终盈利": ["特殊", "盈利导向", "综合策略"],
    "S02_月月盈利": ["特殊", "月度目标", "稳定盈利"],
    "S03_修复均值回归": ["特殊", "修复版", "均值回归"]
}

# ==============================================================================
# 策略难度等级
# ==============================================================================

STRATEGY_DIFFICULTY = {
    "初级": ["B01_均值回归", "B02_趋势跟踪", "C01_超保守"],
    "中级": ["O01_优化均值回归", "O02_平衡均值回归", "O03_改进趋势跟踪", "C02_实用均值回归"],
    "高级": ["A01_AI趋势预测", "H01_阿尔法X", "G01_激进均值回归"],
    "专家": ["A02_AI优化阿尔法", "A03_ML增强趋势", "H02_多时间框架", "H03_增强阿尔法V2"],
    "特殊": ["S01_最终盈利", "S02_月月盈利", "S03_修复均值回归"]
}

# ==============================================================================
# 策略性能等级 (基于历史回测)
# ==============================================================================

STRATEGY_PERFORMANCE = {
    "收益潜力": {
        "高": ["H01_阿尔法X", "A02_AI优化阿尔法", "G01_激进均值回归"],
        "中": ["O03_改进趋势跟踪", "A01_AI趋势预测", "H02_多时间框架"],
        "低": ["B01_均值回归", "B02_趋势跟踪", "C01_超保守"]
    },
    
    "风险等级": {
        "高": ["G01_激进均值回归", "H03_增强阿尔法V2"],
        "中": ["H01_阿尔法X", "A02_AI优化阿尔法", "O03_改进趋势跟踪"],
        "低": ["C01_超保守", "C02_实用均值回归", "B01_均值回归"]
    },
    
    "稳定性": {
        "高": ["C01_超保守", "O02_平衡均值回归", "S02_月月盈利"],
        "中": ["B01_均值回归", "O01_优化均值回归", "H01_阿尔法X"],
        "低": ["G01_激进均值回归", "A03_ML增强趋势"]
    }
}

def generate_strategy_catalog():
    """生成策略目录"""
    
    print("📚 量化交易策略库目录")
    print("=" * 80)
    
    for category_name, category_info in STRATEGY_CATEGORIES.items():
        print(f"\n🔸 {category_name} ({category_info['代码']}类)")
        print(f"   描述: {category_info['描述']}")
        print(f"   适用: {category_info['适用']}")
        print("   " + "-" * 50)
        
        for i, strategy in enumerate(category_info['策略'], 1):
            new_name = strategy['新名']
            simple_name = strategy['简名']
            tags = STRATEGY_TAGS.get(new_name, [])
            
            print(f"   {i}. {simple_name} ({new_name})")
            print(f"      标签: {', '.join(tags)}")
    
    print(f"\n📊 策略推荐")
    print("=" * 50)
    
    for rec_name, rec_info in STRATEGY_RECOMMENDATIONS.items():
        print(f"\n🎯 {rec_name}")
        print(f"   推荐策略: {', '.join(rec_info['策略'])}")
        print(f"   推荐理由: {rec_info['理由']}")

def create_strategy_mapping():
    """创建策略映射表"""
    
    mapping = {}
    reverse_mapping = {}
    
    for category_info in STRATEGY_CATEGORIES.values():
        for strategy in category_info['策略']:
            original = strategy['原名']
            new_name = strategy['新名'] 
            simple_name = strategy['简名']
            
            # 原名 -> 新名
            mapping[original] = {
                'new_name': new_name,
                'simple_name': simple_name,
                'category': category_info['代码'],
                'tags': STRATEGY_TAGS.get(new_name, [])
            }
            
            # 新名 -> 原名 (用于向后兼容)
            reverse_mapping[new_name] = original
            reverse_mapping[simple_name] = original
    
    return mapping, reverse_mapping

def get_strategy_recommendation(user_profile: Dict[str, Any]) -> List[str]:
    """根据用户画像推荐策略"""
    
    experience = user_profile.get('experience', '新手')  # 新手/有经验/专家
    risk_tolerance = user_profile.get('risk_tolerance', '低')  # 低/中/高
    goal = user_profile.get('goal', '稳健')  # 稳健/收益/技术
    
    recommendations = []
    
    # 根据经验推荐
    if experience == '新手':
        recommendations.extend(STRATEGY_RECOMMENDATIONS['新手推荐']['策略'])
    
    # 根据风险偏好推荐
    if risk_tolerance == '低':
        recommendations.extend(STRATEGY_RECOMMENDATIONS['稳健投资']['策略'])
    elif risk_tolerance == '高':
        recommendations.extend(STRATEGY_RECOMMENDATIONS['追求收益']['策略'])
    
    # 根据目标推荐
    if goal == '技术':
        recommendations.extend(STRATEGY_RECOMMENDATIONS['技术流']['策略'])
    elif goal == '月度':
        recommendations.extend(STRATEGY_RECOMMENDATIONS['月度目标']['策略'])
    
    # 去重并返回前5个
    return list(dict.fromkeys(recommendations))[:5]

if __name__ == "__main__":
    # 生成策略目录
    generate_strategy_catalog()
    
    # 创建映射表
    mapping, reverse_mapping = create_strategy_mapping()
    
    print(f"\n🔄 策略映射示例:")
    print("=" * 50)
    for original, info in list(mapping.items())[:5]:
        print(f"{original} -> {info['simple_name']} ({info['new_name']})")
    
    # 推荐示例
    print(f"\n🎯 推荐示例:")
    print("=" * 50)
    
    user_profiles = [
        {'experience': '新手', 'risk_tolerance': '低', 'goal': '稳健'},
        {'experience': '专家', 'risk_tolerance': '高', 'goal': '收益'},
        {'experience': '有经验', 'risk_tolerance': '中', 'goal': '技术'}
    ]
    
    for i, profile in enumerate(user_profiles, 1):
        recs = get_strategy_recommendation(profile)
        print(f"{i}. 用户画像: {profile}")
        print(f"   推荐策略: {recs}")
        print()
