# -*- coding: utf-8 -*-
"""
P02简化均值回归策略 - 2023-2024年两年压力测试
使用本地BTCUSDT历史数据进行全面验证
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
import time
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class SimpleMeanReversionStressTest:
    """P02简化均值回归策略压力测试器"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        self.test_periods = [
            {
                'name': '2023年全年',
                'start': '2023-01-01',
                'end': '2023-12-31',
                'description': '2023年完整年度测试'
            },
            {
                'name': '2024年全年',
                'start': '2024-01-01', 
                'end': '2024-12-31',
                'description': '2024年完整年度测试'
            },
            {
                'name': '2023-2024两年',
                'start': '2023-01-01',
                'end': '2024-12-31', 
                'description': '完整两年压力测试'
            },
            {
                'name': '2023年熊市',
                'start': '2023-01-01',
                'end': '2023-06-30',
                'description': '2023年上半年熊市测试'
            },
            {
                'name': '2023年牛市',
                'start': '2023-10-01',
                'end': '2024-03-31',
                'description': '牛市上涨期测试'
            },
            {
                'name': '2024年4月验证',
                'start': '2024-04-01',
                'end': '2024-04-30',
                'description': '已知盈利期间验证'
            },
            {
                'name': '2024年上半年',
                'start': '2024-01-01',
                'end': '2024-06-30',
                'description': '2024年上半年测试'
            }
        ]
        
        # 简化均值回归策略参数
        self.strategy_params = {
            'bb_period': 20,           # 布林带周期
            'bb_std': 2.0,             # 布林带标准差
            'rsi_period': 14,          # RSI周期
            'rsi_oversold': 30,        # RSI超卖阈值
            'rsi_overbought': 70,      # RSI超买阈值
            'risk_per_trade': 0.02,    # 每笔交易风险2%
            'stop_loss_pct': 0.03,     # 止损3%
            'take_profit_pct': 0.06,   # 止盈6%
            'min_signal_interval': 240, # 最小信号间隔4小时
            'volume_threshold': 1.2    # 成交量阈值
        }
        
        self.results = {}
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            # 获取日期范围内的所有文件
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                # 构建文件路径
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    # 读取数据
                    df = pd.read_csv(filepath)
                    
                    # 标准化列名
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 重命名列以匹配策略需求
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    # 过滤日期范围
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            # 合并所有数据
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            
            # 去重
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            print(f"   时间范围: {combined_data.index[0]} 至 {combined_data.index[-1]}")
            
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()
            
            # 布林带
            df['BB_Middle'] = df['CLOSE'].rolling(self.strategy_params['bb_period']).mean()
            bb_std = df['CLOSE'].rolling(self.strategy_params['bb_period']).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * self.strategy_params['bb_std'])
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * self.strategy_params['bb_std'])
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # 成交量移动平均
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def simulate_mean_reversion_strategy(self, data: pd.DataFrame) -> dict:
        """模拟简化均值回归策略交易"""
        try:
            print("🔄 模拟简化均值回归策略交易...")
            
            # 计算技术指标
            data = self.calculate_technical_indicators(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            last_trade_time = None
            
            for i in range(50, len(data)):  # 从第50个数据点开始，确保技术指标有效
                current_time = data.index[i]
                current_data = data.iloc[i]
                
                close_price = current_data['CLOSE']
                bb_upper = current_data['BB_Upper']
                bb_lower = current_data['BB_Lower']
                bb_middle = current_data['BB_Middle']
                rsi = current_data['RSI']
                volume = current_data['VOLUME']
                volume_ma = current_data['Volume_MA']
                
                # 检查信号间隔
                if last_trade_time and (current_time - last_trade_time).total_seconds() < self.strategy_params['min_signal_interval'] * 60:
                    equity_curve.append(cash + position * close_price)
                    continue
                
                # 检查是否有有效的技术指标
                if pd.isna(bb_lower) or pd.isna(bb_upper) or pd.isna(rsi) or pd.isna(volume_ma):
                    equity_curve.append(cash + position * close_price)
                    continue
                
                # 生成交易信号
                if position == 0:  # 无持仓
                    # 买入条件：价格触及布林带下轨 + RSI超卖 + 成交量放大
                    if (close_price <= bb_lower and 
                        rsi <= self.strategy_params['rsi_oversold'] and 
                        volume > volume_ma * self.strategy_params['volume_threshold']):
                        
                        # 计算仓位大小
                        risk_amount = cash * self.strategy_params['risk_per_trade']
                        stop_loss_price = close_price * (1 - self.strategy_params['stop_loss_pct'])
                        risk_per_share = close_price - stop_loss_price
                        
                        if risk_per_share > 0:
                            shares = risk_amount / risk_per_share
                            shares = min(shares, cash * 0.95 / close_price)  # 最大95%仓位
                            
                            if shares > 0:
                                position = shares
                                cash -= shares * close_price * 1.0005  # 包含手续费
                                
                                trades.append({
                                    'time': current_time,
                                    'action': 'buy',
                                    'price': close_price,
                                    'shares': shares,
                                    'stop_loss': stop_loss_price,
                                    'take_profit': close_price * (1 + self.strategy_params['take_profit_pct'])
                                })
                                
                                last_trade_time = current_time
                
                elif position > 0:  # 有持仓
                    last_trade = trades[-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']
                    
                    # 卖出条件：止损、止盈或回归中轨
                    should_sell = False
                    sell_reason = ""
                    
                    if close_price <= stop_loss:
                        should_sell = True
                        sell_reason = "止损"
                    elif close_price >= take_profit:
                        should_sell = True
                        sell_reason = "止盈"
                    elif close_price >= bb_middle and rsi >= 50:
                        should_sell = True
                        sell_reason = "回归中轨"
                    elif close_price >= bb_upper or rsi >= self.strategy_params['rsi_overbought']:
                        should_sell = True
                        sell_reason = "超买退出"
                    
                    if should_sell:
                        cash += position * close_price * 0.9995  # 扣除手续费
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': close_price,
                            'shares': position,
                            'reason': sell_reason,
                            'pnl': (close_price - last_trade['price']) * position
                        })
                        
                        position = 0
                        last_trade_time = current_time
                
                # 更新权益曲线
                current_equity = cash + position * close_price
                equity_curve.append(current_equity)
            
            # 如果最后还有持仓，平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': (final_price - trades[-1]['price']) * position
                })
                
                position = 0
            
            final_equity = cash
            
            # 计算交易统计
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)  # 年化
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ 策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"策略模拟失败: {e}")
            return None
    
    def run_backtest_for_period(self, period_info: dict) -> dict:
        """对指定期间运行回测"""
        try:
            print(f"\n🚀 开始回测: {period_info['name']}")
            print(f"   期间: {period_info['start']} 至 {period_info['end']}")
            print(f"   描述: {period_info['description']}")
            print("-" * 60)
            
            start_time = time.time()
            
            # 加载数据
            data = self.load_historical_data(period_info['start'], period_info['end'])
            
            if data.empty:
                return {'error': '数据为空'}
            
            # 运行策略模拟
            strategy_result = self.simulate_mean_reversion_strategy(data)
            
            if not strategy_result:
                return {'error': '策略模拟失败'}
            
            execution_time = time.time() - start_time
            
            # 计算额外指标
            total_days = (pd.to_datetime(period_info['end']) - pd.to_datetime(period_info['start'])).days
            annual_return = (1 + strategy_result['total_return']) ** (365 / total_days) - 1
            
            # 计算市场基准收益
            market_return = ((data['CLOSE'].iloc[-1] - data['CLOSE'].iloc[0]) / data['CLOSE'].iloc[0])
            
            result = {
                'period_name': period_info['name'],
                'start_date': period_info['start'],
                'end_date': period_info['end'],
                'total_days': total_days,
                'execution_time': execution_time,
                
                # 基础指标
                'total_return_pct': strategy_result['total_return'] * 100,
                'annual_return_pct': annual_return * 100,
                'max_drawdown_pct': strategy_result['max_drawdown'] * 100,
                'sharpe_ratio': strategy_result['sharpe_ratio'],
                
                # 交易指标
                'total_trades': strategy_result['total_trades'],
                'winning_trades': strategy_result['winning_trades'],
                'losing_trades': strategy_result['losing_trades'],
                'win_rate': strategy_result['win_rate'] * 100,
                'profit_loss_ratio': strategy_result['profit_loss_ratio'],
                
                # 市场对比
                'market_return_pct': market_return * 100,
                'excess_return_pct': (strategy_result['total_return'] - market_return) * 100,
                
                # 权益曲线
                'equity_curve': strategy_result['equity_curve'],
                'trades': strategy_result['trades']
            }
            
            print(f"✅ 回测完成!")
            print(f"   总收益率: {result['total_return_pct']:+.2f}%")
            print(f"   年化收益率: {result['annual_return_pct']:+.2f}%")
            print(f"   最大回撤: {result['max_drawdown_pct']:.2f}%")
            print(f"   夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"   交易次数: {result['total_trades']}")
            print(f"   胜率: {result['win_rate']:.1f}%")
            print(f"   盈亏比: {result['profit_loss_ratio']:.2f}")
            print(f"   市场收益: {result['market_return_pct']:+.2f}%")
            print(f"   超额收益: {result['excess_return_pct']:+.2f}%")
            print(f"   执行时间: {execution_time:.1f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"回测期间 {period_info['name']} 失败: {e}")
            return {'error': str(e)}

def main():
    """主函数"""
    print("🚀 启动P02简化均值回归策略两年压力测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        print("请确保本地BTCUSDT历史数据已正确放置")
        return
    
    # 创建测试器
    stress_tester = SimpleMeanReversionStressTest()
    
    print("🔥 P02简化均值回归策略 - 两年压力测试")
    print("=" * 80)
    print("测试数据: 本地BTCUSDT分钟级数据")
    print("测试期间: 2023年1月 至 2024年12月")
    print("测试目的: 验证策略在不同市场环境下的表现")
    print("=" * 80)
    
    # 运行所有测试期间
    for period in stress_tester.test_periods:
        result = stress_tester.run_backtest_for_period(period)
        stress_tester.results[period['name']] = result
    
    # 生成综合报告
    print(f"\n📊 P02简化均值回归策略 - 两年压力测试报告")
    print("=" * 100)
    print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 过滤有效结果
    valid_results = {k: v for k, v in stress_tester.results.items() if 'error' not in v}
    
    if not valid_results:
        print("❌ 没有有效的测试结果")
        return
    
    # 创建结果表格
    print("📋 各期间详细表现:")
    print("-" * 100)
    
    header = f"{'期间':<15} {'总收益率':<10} {'年化收益率':<10} {'最大回撤':<8} {'夏普比率':<8} {'胜率':<8} {'交易次数':<8} {'超额收益':<10}"
    print(header)
    print("-" * 100)
    
    for period_name, result in valid_results.items():
        row = (f"{period_name:<15} "
              f"{result['total_return_pct']:>8.2f}% "
              f"{result['annual_return_pct']:>8.2f}% "
              f"{result['max_drawdown_pct']:>6.2f}% "
              f"{result['sharpe_ratio']:>6.2f} "
              f"{result['win_rate']:>6.1f}% "
              f"{result['total_trades']:>6d} "
              f"{result['excess_return_pct']:>8.2f}%")
        print(row)
    
    print()
    
    # 客户目标达成分析
    print(f"\n🎯 客户目标达成分析:")
    print("-" * 50)
    print("客户目标: 年化收益≥15%, 夏普比率≥2, 最大回撤≤15%")
    print()
    
    target_met_count = 0
    for period_name, result in valid_results.items():
        annual_ok = result['annual_return_pct'] >= 15
        sharpe_ok = result['sharpe_ratio'] >= 2 if not np.isnan(result['sharpe_ratio']) else False
        drawdown_ok = result['max_drawdown_pct'] <= 15
        
        all_targets_met = annual_ok and sharpe_ok and drawdown_ok
        if all_targets_met:
            target_met_count += 1
        
        status = "✅ 达标" if all_targets_met else "❌ 未达标"
        print(f"{period_name}: {status}")
        print(f"  年化收益: {result['annual_return_pct']:+.2f}% {'✅' if annual_ok else '❌'}")
        print(f"  夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'}")
        print(f"  最大回撤: {result['max_drawdown_pct']:.2f}% {'✅' if drawdown_ok else '❌'}")
        print()
    
    target_success_rate = target_met_count / len(valid_results) * 100
    print(f"🏆 目标达成率: {target_met_count}/{len(valid_results)} ({target_success_rate:.1f}%)")
    
    # 压力测试结论
    print(f"\n🔥 压力测试结论:")
    print("-" * 50)
    
    if target_success_rate >= 80:
        print("🏆 优秀: 策略在各种市场环境下表现稳定，强烈推荐使用")
    elif target_success_rate >= 60:
        print("✅ 良好: 策略表现总体良好，推荐使用")
    elif target_success_rate >= 40:
        print("⚠️ 一般: 策略表现一般，需要谨慎使用")
    else:
        print("❌ 较差: 策略表现不稳定，不推荐使用")
    
    print(f"\n🎉 压力测试完成!")
    print(f"📊 测试期间数: {len(valid_results)}")

if __name__ == "__main__":
    main()
