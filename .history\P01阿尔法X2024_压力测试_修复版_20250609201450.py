# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 2023-2024年两年压力测试 (修复版)
直接使用本地BTCUSDT历史数据，绕过回测引擎的数据加载问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
import time
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class AlphaX2024StressTestFixed:
    """P01阿尔法X2024策略压力测试器 - 修复版"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        self.test_periods = [
            {
                'name': '2024年4月',
                'start': '2024-04-01',
                'end': '2024-04-30',
                'description': '已验证的盈利期间'
            },
            {
                'name': '2024年1-3月',
                'start': '2024-01-01',
                'end': '2024-03-31',
                'description': '2024年第一季度'
            },
            {
                'name': '2024年5-6月',
                'start': '2024-05-01',
                'end': '2024-06-30',
                'description': '2024年第二季度前半段'
            },
            {
                'name': '2024年上半年',
                'start': '2024-01-01',
                'end': '2024-06-30',
                'description': '2024年上半年完整测试'
            }
        ]
        
        self.results = {}
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            # 获取日期范围内的所有文件
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                # 构建文件路径
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    # 读取数据
                    df = pd.read_csv(filepath)
                    
                    # 标准化列名
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    # 转换时间戳
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    # 重命名列以匹配策略需求
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    # 过滤日期范围
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                # 移动到下个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            # 合并所有数据
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            
            # 去重
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            print(f"   时间范围: {combined_data.index[0]} 至 {combined_data.index[-1]}")
            
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()
            
            # 移动平均线
            df['SMA_12'] = df['CLOSE'].rolling(12).mean()
            df['SMA_26'] = df['CLOSE'].rolling(26).mean()
            
            # RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI_14'] = 100 - (100 / (1 + rs))
            
            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR_14'] = true_range.rolling(14).mean()
            
            # ADX (简化版)
            df['ADX_14'] = df['CLOSE'].rolling(14).std() / df['CLOSE'].rolling(14).mean() * 100
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def simulate_alphax_strategy(self, data: pd.DataFrame) -> dict:
        """模拟阿尔法X策略交易"""
        try:
            print("🔄 模拟阿尔法X策略交易...")
            
            # 计算技术指标
            data = self.calculate_technical_indicators(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            # 策略参数
            risk_per_trade = 0.01
            atr_sl_multiple = 2.5
            atr_tp_multiple = 5.0
            min_signal_interval = 120  # 分钟
            
            last_trade_time = None
            
            for i in range(100, len(data)):  # 从第100个数据点开始，确保技术指标有效
                current_time = data.index[i]
                current_data = data.iloc[i]
                
                close_price = current_data['CLOSE']
                sma_12 = current_data['SMA_12']
                sma_26 = current_data['SMA_26']
                rsi = current_data['RSI_14']
                atr = current_data['ATR_14']
                adx = current_data['ADX_14']
                
                # 检查信号间隔
                if last_trade_time and (current_time - last_trade_time).total_seconds() < min_signal_interval * 60:
                    equity_curve.append(cash + position * close_price)
                    continue
                
                # 生成交易信号
                if position == 0:  # 无持仓
                    # 买入条件：趋势向上 + RSI不过热 + ADX显示趋势
                    if (sma_12 > sma_26 and 
                        close_price > sma_12 and 
                        rsi < 70 and 
                        adx > 25 and
                        not pd.isna(atr) and atr > 0):
                        
                        # 计算仓位大小
                        risk_amount = cash * risk_per_trade
                        stop_loss = close_price - (atr * atr_sl_multiple)
                        risk_per_share = close_price - stop_loss
                        
                        if risk_per_share > 0:
                            shares = risk_amount / risk_per_share
                            shares = min(shares, cash * 0.95 / close_price)  # 最大95%仓位
                            
                            if shares > 0:
                                position = shares
                                cash -= shares * close_price * 1.0005  # 包含手续费
                                
                                trades.append({
                                    'time': current_time,
                                    'action': 'buy',
                                    'price': close_price,
                                    'shares': shares,
                                    'stop_loss': stop_loss,
                                    'take_profit': close_price + (atr * atr_tp_multiple)
                                })
                                
                                last_trade_time = current_time
                
                elif position > 0:  # 有持仓
                    last_trade = trades[-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']
                    
                    # 卖出条件：止损、止盈或趋势转向
                    should_sell = False
                    sell_reason = ""
                    
                    if close_price <= stop_loss:
                        should_sell = True
                        sell_reason = "止损"
                    elif close_price >= take_profit:
                        should_sell = True
                        sell_reason = "止盈"
                    elif sma_12 < sma_26 or close_price < sma_12 or rsi > 80:
                        should_sell = True
                        sell_reason = "趋势转向"
                    
                    if should_sell:
                        cash += position * close_price * 0.9995  # 扣除手续费
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': close_price,
                            'shares': position,
                            'reason': sell_reason,
                            'pnl': (close_price - last_trade['price']) * position
                        })
                        
                        position = 0
                        last_trade_time = current_time
                
                # 更新权益曲线
                current_equity = cash + position * close_price
                equity_curve.append(current_equity)
            
            # 如果最后还有持仓，平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': (final_price - trades[-1]['price']) * position
                })
                
                position = 0
            
            final_equity = cash
            
            # 计算交易统计
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)  # 年化
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ 策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"策略模拟失败: {e}")
            return None
    
    def run_backtest_for_period(self, period_info: dict) -> dict:
        """对指定期间运行回测"""
        try:
            print(f"\n🚀 开始回测: {period_info['name']}")
            print(f"   期间: {period_info['start']} 至 {period_info['end']}")
            print(f"   描述: {period_info['description']}")
            print("-" * 60)
            
            start_time = time.time()
            
            # 加载数据
            data = self.load_historical_data(period_info['start'], period_info['end'])
            
            if data.empty:
                return {'error': '数据为空'}
            
            # 运行策略模拟
            strategy_result = self.simulate_alphax_strategy(data)
            
            if not strategy_result:
                return {'error': '策略模拟失败'}
            
            execution_time = time.time() - start_time
            
            # 计算额外指标
            total_days = (pd.to_datetime(period_info['end']) - pd.to_datetime(period_info['start'])).days
            annual_return = (1 + strategy_result['total_return']) ** (365 / total_days) - 1
            
            # 计算市场基准收益
            market_return = ((data['CLOSE'].iloc[-1] - data['CLOSE'].iloc[0]) / data['CLOSE'].iloc[0])
            
            result = {
                'period_name': period_info['name'],
                'start_date': period_info['start'],
                'end_date': period_info['end'],
                'total_days': total_days,
                'execution_time': execution_time,
                
                # 基础指标
                'total_return_pct': strategy_result['total_return'] * 100,
                'annual_return_pct': annual_return * 100,
                'max_drawdown_pct': strategy_result['max_drawdown'] * 100,
                'sharpe_ratio': strategy_result['sharpe_ratio'],
                
                # 交易指标
                'total_trades': strategy_result['total_trades'],
                'winning_trades': strategy_result['winning_trades'],
                'losing_trades': strategy_result['losing_trades'],
                'win_rate': strategy_result['win_rate'] * 100,
                
                # 市场对比
                'market_return_pct': market_return * 100,
                'excess_return_pct': (strategy_result['total_return'] - market_return) * 100,
                
                # 权益曲线
                'equity_curve': strategy_result['equity_curve'],
                'trades': strategy_result['trades']
            }
            
            print(f"✅ 回测完成!")
            print(f"   总收益率: {result['total_return_pct']:+.2f}%")
            print(f"   年化收益率: {result['annual_return_pct']:+.2f}%")
            print(f"   最大回撤: {result['max_drawdown_pct']:.2f}%")
            print(f"   夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"   交易次数: {result['total_trades']}")
            print(f"   胜率: {result['win_rate']:.1f}%")
            print(f"   市场收益: {result['market_return_pct']:+.2f}%")
            print(f"   超额收益: {result['excess_return_pct']:+.2f}%")
            print(f"   执行时间: {execution_time:.1f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"回测期间 {period_info['name']} 失败: {e}")
            return {'error': str(e)}
    
    def run_comprehensive_stress_test(self):
        """运行全面压力测试"""
        print("🔥 P01阿尔法X2024策略 - 压力测试 (修复版)")
        print("=" * 80)
        print("测试数据: 本地BTCUSDT分钟级数据")
        print("测试方法: 直接策略模拟")
        print("测试目的: 验证策略在不同市场环境下的表现")
        print("=" * 80)
        
        # 运行所有测试期间
        for period in self.test_periods:
            result = self.run_backtest_for_period(period)
            self.results[period['name']] = result
        
        # 生成综合报告
        self.generate_comprehensive_report()
        
        return self.results
    
    def generate_comprehensive_report(self):
        """生成综合压力测试报告"""
        print(f"\n📊 P01阿尔法X2024策略 - 压力测试报告")
        print("=" * 100)
        print(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 过滤有效结果
        valid_results = {k: v for k, v in self.results.items() if 'error' not in v}
        
        if not valid_results:
            print("❌ 没有有效的测试结果")
            return
        
        # 创建结果表格
        print("📋 各期间详细表现:")
        print("-" * 100)
        
        header = f"{'期间':<15} {'总收益率':<10} {'年化收益率':<10} {'最大回撤':<8} {'夏普比率':<8} {'胜率':<8} {'交易次数':<8} {'超额收益':<10}"
        print(header)
        print("-" * 100)
        
        for period_name, result in valid_results.items():
            row = (f"{period_name:<15} "
                  f"{result['total_return_pct']:>8.2f}% "
                  f"{result['annual_return_pct']:>8.2f}% "
                  f"{result['max_drawdown_pct']:>6.2f}% "
                  f"{result['sharpe_ratio']:>6.2f} "
                  f"{result['win_rate']:>6.1f}% "
                  f"{result['total_trades']:>6d} "
                  f"{result['excess_return_pct']:>8.2f}%")
            print(row)
        
        print()
        
        # 客户目标达成分析
        print(f"\n🎯 客户目标达成分析:")
        print("-" * 50)
        print("客户目标: 年化收益≥15%, 夏普比率≥2, 最大回撤≤15%")
        print()
        
        target_met_count = 0
        for period_name, result in valid_results.items():
            annual_ok = result['annual_return_pct'] >= 15
            sharpe_ok = result['sharpe_ratio'] >= 2 if not np.isnan(result['sharpe_ratio']) else False
            drawdown_ok = result['max_drawdown_pct'] <= 15
            
            all_targets_met = annual_ok and sharpe_ok and drawdown_ok
            if all_targets_met:
                target_met_count += 1
            
            status = "✅ 达标" if all_targets_met else "❌ 未达标"
            print(f"{period_name}: {status}")
            print(f"  年化收益: {result['annual_return_pct']:+.2f}% {'✅' if annual_ok else '❌'}")
            print(f"  夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'}")
            print(f"  最大回撤: {result['max_drawdown_pct']:.2f}% {'✅' if drawdown_ok else '❌'}")
            print()
        
        target_success_rate = target_met_count / len(valid_results) * 100
        print(f"🏆 目标达成率: {target_met_count}/{len(valid_results)} ({target_success_rate:.1f}%)")
        
        # 保存详细报告
        self.save_detailed_report(valid_results)

    def save_detailed_report(self, valid_results: dict):
        """保存详细报告到文件"""
        try:
            # 创建DataFrame
            report_data = []
            for period_name, result in valid_results.items():
                report_data.append({
                    '测试期间': period_name,
                    '开始日期': result['start_date'],
                    '结束日期': result['end_date'],
                    '测试天数': result['total_days'],
                    '总收益率(%)': result['total_return_pct'],
                    '年化收益率(%)': result['annual_return_pct'],
                    '最大回撤(%)': result['max_drawdown_pct'],
                    '夏普比率': result['sharpe_ratio'],
                    '总交易次数': result['total_trades'],
                    '盈利次数': result['winning_trades'],
                    '亏损次数': result['losing_trades'],
                    '胜率(%)': result['win_rate'],
                    '市场收益率(%)': result['market_return_pct'],
                    '超额收益(%)': result['excess_return_pct'],
                    '执行时间(秒)': result['execution_time']
                })

            df = pd.DataFrame(report_data)

            # 保存到CSV
            filename = f"P01阿尔法X2024_压力测试报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"📄 详细报告已保存: {filename}")

        except Exception as e:
            logger.error(f"保存报告失败: {e}")

def main():
    """主函数"""
    print("🚀 启动P01阿尔法X2024策略压力测试 (修复版)...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        print("请确保本地BTCUSDT历史数据已正确放置")
        return
    
    # 创建测试器
    stress_tester = AlphaX2024StressTestFixed()
    
    # 运行压力测试
    start_time = time.time()
    results = stress_tester.run_comprehensive_stress_test()
    end_time = time.time()
    
    print(f"\n🎉 压力测试完成!")
    print(f"⏱️ 总耗时: {(end_time - start_time)/60:.1f}分钟")
    print(f"📊 测试期间数: {len([r for r in results.values() if 'error' not in r])}")

if __name__ == "__main__":
    main()
