# -*- coding: utf-8 -*-
# 在 核心代码/交易策略/策略库.py

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass, field
import importlib
import os
import sys

# --- Setup Project Path ---
# Assuming this file is located at PROJECT_ROOT/核心代码/交易策略/
# We need to go up two levels to reach PROJECT_ROOT
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 检测backtesting库是否可用
try:
    import backtesting
    backtesting_available = True
except ImportError:
    backtesting_available = False

# --- 保持 BacktestResult 和 validate_input ---
@dataclass
class BacktestResult: # ... (保持不变) ...
    pass
def validate_input(data: pd.DataFrame, required_cols: List[str] = ['Open', 'High', 'Low', 'Close']) -> bool: # ... (保持不变) ...
    pass

# --- Logger Setup ---
log_format = "%(asctime)s [%(levelname)s] [%(name)s]: %(message)s"
os.makedirs(os.path.join(PROJECT_ROOT, 'logs'), exist_ok=True)
logging.basicConfig(level=logging.DEBUG, format=log_format, handlers=[logging.StreamHandler(), logging.FileHandler(os.path.join(PROJECT_ROOT,'logs','backtest.log'), encoding='utf-8')])
logger = logging.getLogger("模拟回测引擎")

# --- 修改后的策略基类 ---
class TradingStrategy:
    """
    交易策略基类 (适配模拟事件驱动回测和backtesting)
    """
    parameters: Dict[str, Any] = field(default_factory=dict)
    # 默认的止损止盈比例，子策略可以在其 on_init 中根据参数覆盖
    default_stop_loss_pct = 0.03
    default_take_profit_pct = 0.08
    # 默认的信号仓位百分比，如果策略不指定，则使用此值
    default_signal_size_pct = 0.05 

    def __init__(self, engine: Any, symbol_list: List[str], params: Optional[Dict] = None):
        """
        初始化策略实例 (主要为事件驱动模式设计)。

        Args:
            engine: 交易引擎实例 (通常是 Portfolio 实例或其适配器)，
                    用于访问组合信息 (如持仓、现金) 和市场数据。
            symbol_list: 此策略实例负责的标的列表。
            params: 策略特定参数。
        """
        # --- 参数处理逻辑 ---
        final_params = {}
        cls = self.__class__
        # 1. 类定义的属性作为基础默认值
        potential_class_params = [p for p in dir(cls) if not p.startswith('_') and not callable(getattr(cls, p)) and isinstance(getattr(cls, p), (int, float, str, bool, list, dict))]
        for param_name in potential_class_params:
            final_params[param_name] = getattr(cls, param_name)
            
        # 2. 构造函数传入的 params 覆盖/添加值
        if params:
            for key, value in params.items():
                final_params[key] = value
                
        self.parameters = final_params
        
        # 将最终参数设为实例属性 (方便访问)
        for key, value in self.parameters.items():
            setattr(self, key, value)

        # --- 事件驱动模式初始化 ---
        if engine is None or symbol_list is None:
            raise ValueError("事件驱动策略必须提供 engine 和 symbol_list。")
            
        self.engine = engine # Portfolio instance
        self.symbol_list = symbol_list
        self.strategy_name = self.__class__.__name__ # 用于信号标记

        logger.info(f"策略 {self.strategy_name} 以事件驱动模式初始化 for symbols: {self.symbol_list}, Params: {self.parameters}")
        
        # 调用子类的初始化逻辑
        self.on_init()

    def on_init(self):
        """子类可以覆盖此方法进行初始化，例如加载历史数据或设置内部状态。"""
        # 确保子类中也正确设置了 self.stop_loss_pct, self.take_profit_pct, self.signal_size_pct
        # 如果子类参数中没有定义，则使用基类的默认值
        self.stop_loss_pct = self.parameters.get('stop_loss_pct', self.default_stop_loss_pct)
        self.take_profit_pct = self.parameters.get('take_profit_pct', self.default_take_profit_pct)
        self.signal_size_pct = self.parameters.get('signal_size_pct', self.default_signal_size_pct)
        pass

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        """
        处理新的分钟线 Bar 数据，并返回交易信号列表。
        这是事件驱动策略的核心逻辑入口。

        Args:
            current_bar_data (pd.DataFrame): 当前时间点所有 relevant 标的的最新 Bar 数据
                                             (MultiIndex: Symbol, Datetime)。
                                             通常只包含最新的一个时间点。需要包含 OHLCV 和预计算的因子列。
        Returns:
            Optional[List[Dict]]: 交易信号字典的列表，每个字典代表一个交易指令。
                                  例如: [{'symbol': 'AAPL', 'action': 'buy', 'price': 150.0, 'size_pct': 0.05, 'strategy': 'MyStrategy'}, ...]
                                  如果没有信号，则返回 None 或空列表。
        """
        raise NotImplementedError("子类必须实现 on_bar 方法并返回信号列表或None。")

    def _create_signal(self, symbol: str, action: str, price: float, 
                       size_pct: Optional[float] = None, size_abs: Optional[float] = None,
                       sl_pct: Optional[float] = None, tp_pct: Optional[float] = None,
                       signal_type: Optional[str] = None) -> Optional[Dict]:
        """辅助函数，用于创建标准格式的交易信号字典。"""
        signal = {
            'symbol': symbol,
            'action': action.lower(),
            'price': price,
            'strategy': self.strategy_name, # 自动添加策略名
            'timestamp': self.engine.current_dt if hasattr(self.engine, 'current_dt') else pd.Timestamp.now() # Use engine's current_dt
        }
        if signal_type:
             signal['signal_type'] = signal_type

        # Determine the trade size. Prioritize size_abs if provided.
        trade_size = None
        current_engine_time = self.engine.current_dt if hasattr(self.engine, 'current_dt') else pd.Timestamp.now()

        if size_abs is not None and size_abs > 0:
            trade_size = size_abs
            logger.debug(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 创建信号使用 Size_Abs: {trade_size:.4f}")
        elif size_pct is not None and size_pct > 0 and self.engine.portfolio is not None:
            # Calculate absolute size from percentage
            try:
                # Ensure get_portfolio_value is called with current prices if needed by Portfolio
                current_portfolio_value_dict = self.engine.portfolio.get_portfolio_value(
                    current_prices_snapshot=self.engine.get_current_bar(symbol) # Pass current price for this symbol
                )
                current_portfolio_value = current_portfolio_value_dict['total']

                if price > 1e-9 and current_portfolio_value is not None and current_portfolio_value > 0: # Avoid division by zero or near zero
                    # Calculate approximate volume
                    notional_value = current_portfolio_value * size_pct
                    trade_size = notional_value / price
                    logger.debug(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 创建信号使用 Size_Pct ({size_pct:.2%}) -> Notional={notional_value:.2f}, Price={price:.4f} -> Calculated Size: {trade_size:.4f}")

                    # Note: This is a simplified calculation. Real-world trading might require
                    # rounding to lot sizes or handling minimum trade values.
                    # Add min trade value check
                    if notional_value < self.min_trade_value_abs:
                        logger.warning(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 计算出的交易名义金额 ({notional_value:.2f}) 小于最小交易金额限制 ({self.min_trade_value_abs:.2f})。信号无效。")
                        trade_size = None # Invalidate signal

                else:
                    logger.warning(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 无法计算交易大小：价格 ({price:.4f}) 或投资组合总价值 ({current_portfolio_value:.2f}) 无效。信号无效。")
                    trade_size = None # Invalidate signal
            except Exception as e:
                logger.error(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 计算交易大小出错: {e}", exc_info=True)
                trade_size = None # Invalidate signal

        # Add the calculated trade size to the signal if valid
        if trade_size is not None and trade_size > 1e-9: # Use a small tolerance
             signal['size'] = trade_size
             # Use strategy-level or base class default stop loss and take profit (if not specified in the call)
             signal['stop_loss_pct'] = sl_pct if sl_pct is not None else self.stop_loss_pct
             signal['take_profit_pct'] = tp_pct if tp_pct is not None else self.take_profit_pct
             return signal # Return the valid signal dictionary
        else:
             # If size could not be determined or is zero, log a warning.
             # The engine will likely mark this signal as invalid.
             # This warning is already in the calculation blocks above.
             # Just return None here.
             return None # Return None for invalid signals to prevent them from being processed

    def get_current_position(self, symbol: str) -> float:
        """获取当前持仓数量 (通过engine访问Portfolio)"""
        return self.engine.get_position_size(symbol) # 正确：使用引擎的接口

    def get_available_cash(self) -> float:
        """获取可用资金 (通过engine访问Portfolio)"""
        return self.engine.get_available_cash() # 正确：使用引擎的接口

    def _generate_results(self) -> Optional[Dict[str, Any]]:
        equity_df = pd.DataFrame(self.equity_curve, columns=['timestamp', 'equity'])
        equity_df = equity_df.set_index('timestamp')
        return equity_df

# --- 具体策略实现 (修改为使用 on_bar 并返回信号) ---

class MeanReversionStrategy(TradingStrategy):
    """均值回归策略 (适配 on_bar)"""
    # 参数定义因子列名 - 这些应该从 self.parameters 中获取，这里的定义是默认/备选值
    # bbands_prefix = 'Bollinger Bands' # 与 calculate_factors 输出匹配 - Moved to on_init
    # rsi_factor = 'RSI'              # 与 calculate_factors 输出匹配 - Moved to on_init
    # 策略逻辑参数
    rsi_ob = 75 # Default value, can be overridden by params
    rsi_os = 25 # Default value, can be overridden by params
    exit_on_middle = True # Default value, can be overridden by params
    min_trade_interval = 60  # 最小交易间隔时间（分钟） #TODO: 这个参数应该由RiskManager或System控制 - Moved to on_init
    min_trade_value_abs = 1000 # 最小名义交易额 (用于计算size_abs时的参考) - Moved to on_init

    def on_init(self):
        super().on_init() # 调用基类的 on_init 来设置默认的 SL/TP/SizePct (如果未在参数中指定)
        
        # 从 self.parameters 获取特定于此策略的参数，并使用更准确的键名
        self.bbands_prefix = self.parameters.get('bbands_prefix', 'BBands_10_1.5') # 使用配置中的 bbands_prefix 键
        self.rsi_key = self.parameters.get('rsi_key', 'RSI_7') # 使用配置中的 rsi_key 键
        
        # 获取策略逻辑参数，如果参数中没有指定，则使用类属性的默认值
        self.rsi_ob = self.parameters.get('rsi_ob', self.rsi_ob)
        self.rsi_os = self.parameters.get('rsi_os', self.rsi_os)
        self.exit_on_middle = self.parameters.get('exit_on_middle', self.exit_on_middle)
        self.min_trade_interval = self.parameters.get('min_trade_interval', self.min_trade_interval)
        self.min_trade_value_abs = self.parameters.get('min_trade_value_abs', self.min_trade_value_abs)

        # 策略特定的止损止盈和仓位百分比可以覆盖基类或参数传入的值
        # 基类的 on_init 已经处理了 'stop_loss_pct', 'take_profit_pct', 'signal_size_pct'
        # 但如果此策略需要自己的特定参数名称，可以在这里获取并覆盖
        self.stop_loss_pct = self.parameters.get('mean_reversion_sl_pct', self.stop_loss_pct)
        self.take_profit_pct = self.parameters.get('mean_reversion_tp_pct', self.take_profit_pct)
        self.signal_size_pct = self.parameters.get('mean_reversion_size_pct', self.signal_size_pct)

        # Bollinger Bands columns generated by calculate_factors include a '_BB_' prefix
        # Strategy requires these columns in self.required_cols for checking.
        bb_lower_col_required = f"{self.bbands_prefix}_BB_Lower"
        bb_upper_col_required = f"{self.bbands_prefix}_BB_Upper"
        bb_middle_col_required = f"{self.bbands_prefix}_BB_Middle"
        
        # Construct the list of required columns, including corrected BBands names and 'Close'
        self.required_cols = ['Close', bb_lower_col_required, bb_upper_col_required, self.rsi_key]
        if self.exit_on_middle:
            self.required_cols.append(bb_middle_col_required)
        self.required_cols = sorted(list(set(self.required_cols)))
            
        logger.info(f"{self.strategy_name}: 初始化完成。策略期望的必需列: {self.required_cols}. Params: {self.parameters}")
        logger.info(f"{self.strategy_name}: SL={self.stop_loss_pct:.2%}, TP={self.take_profit_pct:.2%}, SizePct={self.signal_size_pct:.2%}")
        logger.info(f"{self.strategy_name}: 查找必需列: {self.required_cols}")

    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        """处理新的分钟线 Bar，并返回交易信号列表。"""
        signals = []
        
        # 检查所需列 (一次性)
        if not hasattr(self, '_checked_cols_present'):
            self._checked_cols_present = all(c in current_bar_data.columns for c in self.required_cols)
            if not self._checked_cols_present:
                # Use WARNING instead of ERROR for missing columns to avoid stopping backtest immediately
                # Strategy should handle missing data gracefully if possible, or at least log it less aggressively
                logger.warning(f"{self.strategy_name} on_bar 缺少必需列: {self.required_cols}. 可用: {current_bar_data.columns.tolist()}")
                # Consider setting a flag here to prevent repeated warnings for the same issue
                # self._checked_cols_present = False # Keep checking if data structure might change (unlikely in backtest)
                # Retain the original check, returning None if cols are missing
                return None

        if not self._checked_cols_present: # This check is now redundant due to the previous block, but keeping for clarity
            return None # 如果列不全，则不产生信号

        for symbol in self.symbol_list:
            # The current_bar_data passed from MinuteEventBacktester has 'Symbol' as its index.
            # So, we should check if the symbol exists in this index.
            if symbol not in current_bar_data.index:
                # logger.debug(f"[{self.engine.current_dt}] {symbol}: No data in current_bar_data. Index: {current_bar_data.index}")
                continue

            try:
                # If current_bar_data.index is 'Symbol', then .loc[symbol] is appropriate.
                symbol_data_row = current_bar_data.loc[symbol]
                if not isinstance(symbol_data_row, pd.Series) or symbol_data_row.empty: # Ensure it's a Series and not empty
                    logger.warning(f"[{self.engine.current_dt}] {symbol}: Data for symbol is not a Series or is empty after .loc. Type: {type(symbol_data_row)}")
                    continue
                
                price = symbol_data_row['Close'] # Access directly if it's a Series
                rsi = symbol_data_row[self.rsi_key]
                # Access BBands columns using the corrected names
                bb_lower_col = f"{self.bbands_prefix}_BB_Lower"
                bb_upper_col = f"{self.bbands_prefix}_BB_Upper"
                bb_middle_col = f"{self.bbands_prefix}_BB_Middle"

                bb_low = symbol_data_row.get(bb_lower_col, np.nan) # Use .get for safer access
                bb_up = symbol_data_row.get(bb_upper_col, np.nan) # Use .get for safer access
                bb_mid = symbol_data_row.get(bb_middle_col, np.nan) if self.exit_on_middle else np.nan # Use .get and handle exit_on_middle

                # --- Added logging for key values ---
                current_engine_time = self.engine.current_dt
                current_pos_for_log = self.get_current_position(symbol) # Get position first
                # Ensure current_pos_for_log is a float for formatting, handle None
                current_pos_formatted = current_pos_for_log if pd.notna(current_pos_for_log) else 0.0
                logger.debug(f"[{current_engine_time}] {self.strategy_name} for {symbol}: Px={price:.4f}, BBup={bb_up:.4f}, BBlow={bb_low:.4f}, BBmid={bb_mid:.4f}, RSI={rsi:.4f}, Pos={current_pos_formatted:.4f}")
                # --- End added logging ---

                if not all(pd.notna(v) for v in [price, bb_low, bb_up, rsi]):
                     # Add logging if essential data is NaN
                    missing_nan_cols = [col for col, val in [(f'{symbol}.Close', price), (f'{symbol}.{bb_lower_col}', bb_low), (f'{symbol}.{bb_upper_col}', bb_up), (f'{symbol}.{self.rsi_key}', rsi)] if pd.notna(val) is False]
                    if missing_nan_cols:
                         logger.debug(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 关键因子或价格存在 NaN: {missing_nan_cols}. 跳过信号生成。")
                    continue
            except (IndexError, KeyError) as e:
                logger.warning(f"{self.strategy_name}: 提取 {symbol} 数据时出错: {e}", exc_info=False) # No full traceback needed for simple key errors
                continue
            except Exception as e:
                logger.error(f"{self.strategy_name}: 处理 {symbol} 数据时发生未知错误: {e}", exc_info=True)
                continue

            current_pos = self.get_current_position(symbol)
            # Explicitly handle case where get_current_position might return None
            if current_pos is None:
                current_pos = 0.0 # Treat None position as 0

            # current_engine_time = pd.Timestamp.now(tz='Asia/Shanghai') # 使用实际时间戳
            current_engine_time = self.engine.current_dt # Use current_dt from the engine


            # --- 交易逻辑：生成信号字典 ---
            # 做空信号 (价格高于上轨且RSI超买)
            # Check if the price *crossed* the upper band from below, and RSI is overbought
            # A simple check is price > upper band, but this might generate signals every bar above the band
            # A more common approach checks for a crossover or extreme reading
            # Let's stick to the provided logic: price > upper band AND RSI > ob
            if price > bb_up and rsi > self.rsi_ob:
                if current_pos > 0: # 持有多仓，则平多
                    signals.append(self._create_signal(symbol, 'sell', price, size_abs=abs(current_pos), signal_type='exit_long_mr_ob'))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 平多信号 (超买)， Px={price:.4f}, Size={abs(current_pos):.4f}")
                elif current_pos == 0 and hasattr(self.engine.config, 'allow_short_selling') and self.engine.config.allow_short_selling: # 无仓位且允许做空，则开空
                    # 使用默认的 signal_size_pct 开空
                    signals.append(self._create_signal(symbol, 'sell', price, size_pct=self.signal_size_pct, signal_type='entry_short_mr_ob'))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 开空信号 (超买)， Px={price:.4f}, SizePct={self.signal_size_pct:.2%}")
            
            # 做多信号 (价格低于下轨且RSI超卖)
            # Check if the price *crossed* the lower band from above, and RSI is oversold
            # Let's stick to the provided logic: price < lower band AND RSI < os
            elif price < bb_low and rsi < self.rsi_os:
                if current_pos < 0: # 持有空仓，则平空
                    signals.append(self._create_signal(symbol, 'buy', price, size_abs=abs(current_pos), signal_type='exit_short_mr_os'))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 平空信号 (超卖)， Px={price:.4f}, Size={abs(current_pos):.4f}")
                elif current_pos == 0: # 无仓位，则开多
                    signals.append(self._create_signal(symbol, 'buy', price, size_pct=self.signal_size_pct, signal_type='entry_long_mr_os'))
                    logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 开多信号 (超卖)， Px={price:.4f}, SizePct={self.signal_size_pct:.2%}")

            # 平仓信号 (回到中轨)
            # Check if exit_on_middle is True and bb_mid is valid
            elif self.exit_on_middle and pd.notna(bb_mid):
                 if current_pos > 0 and price >= bb_mid: # 多头回到中轨平仓
                      signals.append(self._create_signal(symbol, 'sell', price, size_abs=abs(current_pos), signal_type='exit_long_mr_mid'))
                      logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 多头中轨平仓， Px={price:.4f}, Size={abs(current_pos):.4f}")
                 elif current_pos < 0 and price <= bb_mid: # 空头回到中轨平仓
                      signals.append(self._create_signal(symbol, 'buy', price, size_abs=abs(current_pos), signal_type='exit_short_mr_mid'))
                      logger.info(f"[{current_engine_time}] {self.strategy_name} for {symbol}: 空头中轨平仓， Px={price:.4f}, Size={abs(current_pos):.4f}")

        # Filter out any None signals that might have been returned by _create_signal
        valid_signals = [sig for sig in signals if sig is not None]
        
        return valid_signals if valid_signals else None

# --- 可以添加其他策略，如 ORBStrategy, FactorRotationStrategy 等，实现 on_bar 并返回信号列表 ---
# class ORBStrategy(TradingStrategy): ...
# class FactorRotationStrategy(TradingStrategy): ...

class TrendFollowingStrategy(TradingStrategy):
    """趋势跟踪策略"""
    # 默认参数
    ma_fast = 10
    ma_slow = 30
    atr_window = 14
    atr_multiplier = 2.0
    
    def on_init(self):
        super().on_init()
        # 从参数中获取配置
        self.ma_fast = self.parameters.get('ma_fast', 10)
        self.ma_slow = self.parameters.get('ma_slow', 30) 
        self.atr_window = self.parameters.get('atr_window', 14)
        self.atr_multiplier = self.parameters.get('atr_multiplier', 2.0)
        
        # 设置列名
        self.ma_fast_col = f"MA_{self.ma_fast}"
        self.ma_slow_col = f"MA_{self.ma_slow}"
        self.atr_col = f"ATR_{self.atr_window}"
        
        self.required_cols = ['CLOSE', self.ma_fast_col, self.ma_slow_col, self.atr_col]
        
    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        
        # 检查所需列
        if not hasattr(self, '_checked_cols_present'):
            self._checked_cols_present = all(c in current_bar_data.columns for c in self.required_cols)
            if not self._checked_cols_present:
                logger.error(f"缺少必需列: {self.required_cols}")
                return None
                
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index.get_level_values('Symbol'):
                continue
                
            try:
                symbol_data = current_bar_data.xs(symbol, level='Symbol')
                close = symbol_data['CLOSE'].iloc[0]
                ma_fast = symbol_data[self.ma_fast_col]
                ma_slow = symbol_data[self.ma_slow_col]
                atr = symbol_data[self.atr_col]
                
                current_pos = self.get_current_position(symbol)
            except Exception as e:
                logger.warning(f"处理{symbol}数据出错: {e}")
                continue
                
            # 趋势信号
            if ma_fast > ma_slow and current_pos <= 0:  # 上升趋势
                entry_price = close
                stop_loss_price = entry_price - atr * self.atr_multiplier # Renamed for clarity
                signals.append(self._create_signal(
                    symbol, 'buy', entry_price,
                    sl_pct=(entry_price - stop_loss_price)/entry_price if entry_price > 0 else self.default_stop_loss_pct # Avoid division by zero
                ))
            elif ma_fast < ma_slow and current_pos >= 0:  # 下降趋势
                entry_price = close  
                stop_loss_price = entry_price + atr * self.atr_multiplier # Renamed for clarity
                signals.append(self._create_signal(
                    symbol, 'sell', entry_price,
                    sl_pct=(stop_loss_price - entry_price)/entry_price if entry_price > 0 else self.default_stop_loss_pct # Avoid division by zero
                ))
                
        return signals if signals else None

class OptimizedTrendStrategyAI(TradingStrategy):
    """基于人工智能优化的趋势跟踪策略"""
    
    # 默认参数
    ma_window = 20
    atr_window = 14
    atr_multiplier = 2.0
    rsi_window = 14
    rsi_ob = 70
    rsi_os = 30
    
    def on_init(self):
        super().on_init()
        
        # 从参数中获取配置
        self.ma_window = self.parameters.get('ma_window', 20)
        self.atr_window = self.parameters.get('atr_window', 14)
        self.atr_multiplier = self.parameters.get('atr_multiplier', 2.0)
        self.rsi_window = self.parameters.get('rsi_window', 14)
        self.rsi_ob = self.parameters.get('rsi_ob', 70)
        self.rsi_os = self.parameters.get('rsi_os', 30)
        
        # 设置列名
        self.ma_col = f"MA_{self.ma_window}"
        self.atr_col = f"ATR_{self.atr_window}"
        self.rsi_col = f"RSI_{self.rsi_window}"
        
        self.required_cols = ['Close', self.ma_col, self.atr_col, self.rsi_col]
        
    def on_bar(self, current_bar_data: pd.DataFrame) -> Optional[List[Dict]]:
        signals = []
        
        # 检查所需列
        if not hasattr(self, '_checked_cols_present'):
            self._checked_cols_present = all(c in current_bar_data.columns for c in self.required_cols)
            if not self._checked_cols_present:
                logger.error(f"缺少必需列: {self.required_cols}")
                return None
                
        for symbol in self.symbol_list:
            if symbol not in current_bar_data.index.get_level_values('Symbol'):
                continue
                
            try:
                symbol_data = current_bar_data.xs(symbol, level='Symbol')
                close = symbol_data['Close'].iloc[0]
                ma = symbol_data[self.ma_col]
                atr = symbol_data[self.atr_col]
                rsi = symbol_data[self.rsi_col]
                
                current_pos = self.get_current_position(symbol)
            except Exception as e:
                logger.warning(f"处理{symbol}数据出错: {e}")
                continue
                
            # 趋势信号
            if close > ma and rsi < self.rsi_ob and current_pos <= 0:  # 上升趋势
                entry_price = close
                stop_loss_price = entry_price - atr * self.atr_multiplier
                signals.append(self._create_signal(
                    symbol, 'buy', entry_price,
                    sl_pct=(entry_price - stop_loss_price)/entry_price if entry_price > 0 else self.default_stop_loss_pct
                ))
            elif close < ma and rsi > self.rsi_os and current_pos >= 0:  # 下降趋势
                entry_price = close  
                stop_loss_price = entry_price + atr * self.atr_multiplier
                signals.append(self._create_signal(
                    symbol, 'sell', entry_price,
                    sl_pct=(stop_loss_price - entry_price)/entry_price if entry_price > 0 else self.default_stop_loss_pct
                ))
                
        return signals if signals else None

# 更新 STRATEGIES 字典
STRATEGIES = {
    'MeanReversion': MeanReversionStrategy,
    'TrendFollowing': TrendFollowingStrategy,
    'OptimizedTrendStrategyAI': OptimizedTrendStrategyAI
}
