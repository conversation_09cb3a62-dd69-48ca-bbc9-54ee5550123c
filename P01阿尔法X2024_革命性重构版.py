# -*- coding: utf-8 -*-
"""
P01阿尔法X2024策略 - 革命性重构版
完全重新设计，专门针对达到客户目标：年化收益≥15%, 月度胜率≥60%
采用多策略融合 + 动态参数调整 + 激进风险管理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s]: %(message)s')
logger = logging.getLogger(__name__)

class RevolutionaryAlphaX2024Strategy:
    """P01阿尔法X2024策略 - 革命性重构版"""
    
    def __init__(self):
        self.data_path = "D:/市场数据/现金/BTCUSDT"
        
        # 革命性参数设计 - 专门为达到15%年化收益设计
        self.strategy_params = {
            # 激进的仓位管理 (提高收益)
            'base_position_size': 0.25,    # 基础仓位25% (提高)
            'max_position_size': 0.8,      # 最大仓位80% (激进)
            'leverage_multiplier': 1.5,    # 趋势确认时加仓倍数
            
            # 多策略融合
            'strategy_weights': {
                'trend_following': 0.4,    # 趋势跟踪40%
                'mean_reversion': 0.3,     # 均值回归30%
                'momentum_breakout': 0.3   # 动量突破30%
            },
            
            # 动态止盈止损 (优化盈亏比)
            'dynamic_stop_loss': True,
            'base_stop_loss': 0.02,        # 基础止损2%
            'base_take_profit': 0.12,      # 基础止盈12% (6:1盈亏比)
            'trailing_stop': True,         # 启用移动止损
            'profit_protection': 0.5,      # 盈利保护50%
            
            # 信号生成优化 (确保有交易)
            'min_signals_per_month': 5,    # 每月最少5次交易
            'max_signals_per_day': 3,      # 每日最多3次交易
            'signal_timeout_hours': 8,     # 信号超时8小时
            
            # 市场状态自适应
            'market_regime_detection': True,
            'bull_market_multiplier': 1.5,
            'bear_market_multiplier': 0.8,
            'sideways_multiplier': 1.2,
            
            # 技术指标参数 (更敏感)
            'fast_ma': 8,                  # 快速均线
            'slow_ma': 21,                 # 慢速均线
            'rsi_period': 10,              # RSI周期 (更敏感)
            'rsi_oversold': 40,            # RSI超卖 (放宽)
            'rsi_overbought': 60,          # RSI超买 (放宽)
            'atr_period': 10,              # ATR周期
            'volume_threshold': 1.0,       # 成交量阈值 (降低)
            
            # 激进交易控制
            'min_profit_target': 0.005,   # 最小盈利目标0.5%
            'max_holding_hours': 24,      # 最大持仓24小时
            'force_close_loss': 0.05,     # 强制平仓亏损5%
        }
        
    def load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载本地历史数据"""
        try:
            print(f"📊 加载历史数据: {start_date} 至 {end_date}")
            
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            all_data = []
            current_date = start_dt
            
            while current_date <= end_dt:
                year = current_date.year
                month = current_date.month
                
                filename = f"BTCUSDT-1m-{year}-{month:02d}.csv"
                filepath = os.path.join(self.data_path, filename)
                
                if os.path.exists(filepath):
                    print(f"  加载: {filename}")
                    
                    df = pd.read_csv(filepath)
                    
                    if len(df.columns) >= 6:
                        df.columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                    
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    
                    df.rename(columns={
                        'open': 'OPEN',
                        'high': 'HIGH', 
                        'low': 'LOW',
                        'close': 'CLOSE',
                        'volume': 'VOLUME'
                    }, inplace=True)
                    
                    df = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if not df.empty:
                        all_data.append(df)
                
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            
            if not all_data:
                raise ValueError(f"未找到 {start_date} 至 {end_date} 的数据文件")
            
            combined_data = pd.concat(all_data, axis=0)
            combined_data = combined_data.sort_index()
            combined_data = combined_data[~combined_data.index.duplicated(keep='first')]
            
            print(f"✅ 数据加载完成: {len(combined_data)} 条记录")
            return combined_data
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            raise
    
    def calculate_advanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算高级技术指标"""
        try:
            df = data.copy()
            
            # 多重移动平均线
            df['MA_Fast'] = df['CLOSE'].rolling(self.strategy_params['fast_ma']).mean()
            df['MA_Slow'] = df['CLOSE'].rolling(self.strategy_params['slow_ma']).mean()
            df['MA_Trend'] = df['CLOSE'].rolling(50).mean()
            
            # 敏感RSI
            delta = df['CLOSE'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.strategy_params['rsi_period']).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # ATR
            high_low = df['HIGH'] - df['LOW']
            high_close = np.abs(df['HIGH'] - df['CLOSE'].shift())
            low_close = np.abs(df['LOW'] - df['CLOSE'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            df['ATR'] = true_range.rolling(self.strategy_params['atr_period']).mean()
            
            # 价格动量指标
            df['Momentum_5'] = df['CLOSE'].pct_change(5)
            df['Momentum_15'] = df['CLOSE'].pct_change(15)
            df['Momentum_60'] = df['CLOSE'].pct_change(60)
            
            # 成交量指标
            df['Volume_MA'] = df['VOLUME'].rolling(20).mean()
            df['Volume_Ratio'] = df['VOLUME'] / df['Volume_MA']
            
            # 波动率
            df['Volatility'] = df['CLOSE'].rolling(20).std() / df['CLOSE'].rolling(20).mean()
            
            # 布林带
            df['BB_Middle'] = df['CLOSE'].rolling(20).mean()
            bb_std = df['CLOSE'].rolling(20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
            df['BB_Position'] = (df['CLOSE'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
            
            # 市场状态检测
            df['Market_Regime'] = 0  # 0=震荡, 1=牛市, -1=熊市
            
            # 牛市：快线>慢线，价格>趋势线，动量>0
            bull_condition = (
                (df['MA_Fast'] > df['MA_Slow']) & 
                (df['CLOSE'] > df['MA_Trend']) &
                (df['Momentum_60'] > 0.02)
            )
            df.loc[bull_condition, 'Market_Regime'] = 1
            
            # 熊市：快线<慢线，价格<趋势线，动量<0
            bear_condition = (
                (df['MA_Fast'] < df['MA_Slow']) & 
                (df['CLOSE'] < df['MA_Trend']) &
                (df['Momentum_60'] < -0.02)
            )
            df.loc[bear_condition, 'Market_Regime'] = -1
            
            # 多策略信号生成
            df['Trend_Signal'] = self.generate_trend_signals(df)
            df['Mean_Reversion_Signal'] = self.generate_mean_reversion_signals(df)
            df['Momentum_Signal'] = self.generate_momentum_signals(df)
            
            # 综合信号
            df['Combined_Signal'] = (
                df['Trend_Signal'] * self.strategy_params['strategy_weights']['trend_following'] +
                df['Mean_Reversion_Signal'] * self.strategy_params['strategy_weights']['mean_reversion'] +
                df['Momentum_Signal'] * self.strategy_params['strategy_weights']['momentum_breakout']
            )
            
            return df
            
        except Exception as e:
            logger.warning(f"计算技术指标失败: {e}")
            return data
    
    def generate_trend_signals(self, df: pd.DataFrame) -> pd.Series:
        """生成趋势跟踪信号"""
        signals = pd.Series(0, index=df.index)
        
        # 趋势跟踪条件
        trend_up = (
            (df['MA_Fast'] > df['MA_Slow']) &
            (df['CLOSE'] > df['MA_Fast']) &
            (df['RSI'] > 45) &
            (df['Volume_Ratio'] > self.strategy_params['volume_threshold'])
        )
        
        signals[trend_up] = 1
        return signals
    
    def generate_mean_reversion_signals(self, df: pd.DataFrame) -> pd.Series:
        """生成均值回归信号"""
        signals = pd.Series(0, index=df.index)
        
        # 均值回归条件
        oversold = (
            (df['RSI'] <= self.strategy_params['rsi_oversold']) &
            (df['BB_Position'] <= 0.2) &
            (df['Momentum_5'] > 0.002)  # 开始反弹
        )
        
        signals[oversold] = 1
        return signals
    
    def generate_momentum_signals(self, df: pd.DataFrame) -> pd.Series:
        """生成动量突破信号"""
        signals = pd.Series(0, index=df.index)
        
        # 动量突破条件
        breakout = (
            (df['Momentum_15'] > 0.01) &
            (df['Volume_Ratio'] > 1.5) &
            (df['RSI'] > 50) &
            (df['CLOSE'] > df['BB_Upper'] * 0.99)
        )
        
        signals[breakout] = 1
        return signals
    
    def simulate_revolutionary_strategy(self, data: pd.DataFrame) -> dict:
        """模拟革命性重构策略"""
        try:
            print("🔄 模拟革命性重构版阿尔法X策略...")
            
            # 计算高级技术指标
            data = self.calculate_advanced_indicators(data)
            
            # 初始化
            initial_capital = 100000
            cash = initial_capital
            position = 0
            trades = []
            equity_curve = [initial_capital]
            
            # 交易控制变量
            last_trade_time = None
            daily_trades = {}
            monthly_trades = 0
            entry_time = None
            
            for i in range(60, len(data)):  # 从第60个数据点开始
                current_time = data.index[i]
                current_data = data.iloc[i]
                current_price = current_data['CLOSE']
                
                # 更新权益曲线
                current_equity = cash + position * current_price
                equity_curve.append(current_equity)
                
                # 检查数据有效性
                if pd.isna(current_price) or pd.isna(current_data.get('Combined_Signal')):
                    continue
                
                # 检查每日交易限制
                current_date = current_time.date()
                if current_date not in daily_trades:
                    daily_trades[current_date] = 0
                
                if daily_trades[current_date] >= self.strategy_params['max_signals_per_day']:
                    continue
                
                # 无持仓时检查入场
                if position == 0:
                    combined_signal = current_data['Combined_Signal']
                    market_regime = current_data['Market_Regime']
                    
                    # 入场条件：综合信号>0.3 或 强信号>0.6
                    if combined_signal > 0.3 or combined_signal > 0.6:
                        
                        # 根据市场状态调整仓位
                        if market_regime == 1:  # 牛市
                            position_multiplier = self.strategy_params['bull_market_multiplier']
                        elif market_regime == -1:  # 熊市
                            position_multiplier = self.strategy_params['bear_market_multiplier']
                        else:  # 震荡市
                            position_multiplier = self.strategy_params['sideways_multiplier']
                        
                        # 计算仓位大小
                        base_size = self.strategy_params['base_position_size']
                        adjusted_size = base_size * position_multiplier
                        
                        # 强信号时加仓
                        if combined_signal > 0.6:
                            adjusted_size *= self.strategy_params['leverage_multiplier']
                        
                        # 限制最大仓位
                        position_size = min(adjusted_size, self.strategy_params['max_position_size'])
                        
                        # 计算动态止损止盈
                        atr = current_data.get('ATR', current_price * 0.02)
                        volatility = current_data.get('Volatility', 0.02)
                        
                        # 根据波动率调整止损止盈
                        stop_loss_pct = self.strategy_params['base_stop_loss'] * (1 + volatility * 10)
                        take_profit_pct = self.strategy_params['base_take_profit'] * (1 + volatility * 5)
                        
                        stop_loss = current_price * (1 - stop_loss_pct)
                        take_profit = current_price * (1 + take_profit_pct)
                        
                        # 计算股数
                        position_value = cash * position_size
                        shares = position_value / current_price
                        
                        if shares > 0:
                            position = shares
                            cash -= shares * current_price * 1.0005  # 手续费
                            entry_time = current_time
                            
                            trades.append({
                                'time': current_time,
                                'action': 'buy',
                                'price': current_price,
                                'shares': shares,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit,
                                'signal_strength': combined_signal,
                                'market_regime': market_regime,
                                'position_size': position_size,
                                'strategy': 'revolutionary'
                            })
                            
                            last_trade_time = current_time
                            daily_trades[current_date] += 1
                            monthly_trades += 1
                
                # 有持仓时检查出场
                elif position > 0:
                    last_trade = trades[-1]
                    stop_loss = last_trade['stop_loss']
                    take_profit = last_trade['take_profit']
                    
                    # 移动止损
                    if self.strategy_params['trailing_stop']:
                        if current_price > last_trade['price'] * 1.05:  # 盈利5%以上
                            # 移动止损到盈利保护位
                            profit_protection = last_trade['price'] * (1 + self.strategy_params['profit_protection'] * 0.05)
                            stop_loss = max(stop_loss, profit_protection)
                    
                    # 强制平仓条件
                    holding_hours = (current_time - entry_time).total_seconds() / 3600
                    force_close = holding_hours > self.strategy_params['max_holding_hours']
                    
                    # 出场条件
                    should_exit = False
                    exit_reason = ""
                    
                    if current_price <= stop_loss:
                        should_exit = True
                        exit_reason = "止损"
                    elif current_price >= take_profit:
                        should_exit = True
                        exit_reason = "止盈"
                    elif force_close:
                        should_exit = True
                        exit_reason = "强制平仓"
                    elif current_data.get('Combined_Signal', 0) < -0.3:
                        should_exit = True
                        exit_reason = "信号转向"
                    
                    if should_exit:
                        cash += position * current_price * 0.9995  # 扣除手续费
                        
                        pnl = (current_price - last_trade['price']) * position
                        pnl_pct = pnl / (last_trade['price'] * position) * 100
                        
                        trades.append({
                            'time': current_time,
                            'action': 'sell',
                            'price': current_price,
                            'shares': position,
                            'reason': exit_reason,
                            'pnl': pnl,
                            'pnl_pct': pnl_pct,
                            'holding_hours': holding_hours
                        })
                        
                        position = 0
                        entry_time = None
                        last_trade_time = current_time
                        daily_trades[current_date] += 1
            
            # 期末平仓
            if position > 0:
                final_price = data['CLOSE'].iloc[-1]
                cash += position * final_price * 0.9995
                
                pnl = (final_price - trades[-1]['price']) * position
                trades.append({
                    'time': data.index[-1],
                    'action': 'sell',
                    'price': final_price,
                    'shares': position,
                    'reason': '期末平仓',
                    'pnl': pnl
                })
                position = 0
            
            final_equity = cash
            
            # 计算统计指标
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell']
            
            total_trades = len(buy_trades)
            winning_trades = len([t for t in sell_trades if t.get('pnl', 0) > 0])
            losing_trades = len([t for t in sell_trades if t.get('pnl', 0) < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算盈亏比
            winning_pnls = [t.get('pnl', 0) for t in sell_trades if t.get('pnl', 0) > 0]
            losing_pnls = [abs(t.get('pnl', 0)) for t in sell_trades if t.get('pnl', 0) < 0]
            
            avg_win = np.mean(winning_pnls) if winning_pnls else 0
            avg_loss = np.mean(losing_pnls) if losing_pnls else 1
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0
            
            # 计算收益率
            total_return = (final_equity - initial_capital) / initial_capital
            
            # 计算最大回撤
            equity_series = pd.Series(equity_curve)
            rolling_max = equity_series.expanding().max()
            drawdown = (equity_series - rolling_max) / rolling_max
            max_drawdown = abs(drawdown.min())
            
            # 计算夏普比率
            if len(equity_curve) > 1:
                returns = pd.Series(equity_curve).pct_change().dropna()
                if returns.std() > 0:
                    sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)
                else:
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
            
            print(f"✅ 革命性重构版策略模拟完成!")
            print(f"   总收益率: {total_return*100:+.2f}%")
            print(f"   最大回撤: {max_drawdown*100:.2f}%")
            print(f"   交易次数: {total_trades}")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   盈亏比: {profit_loss_ratio:.2f}")
            print(f"   夏普比率: {sharpe_ratio:.2f}")
            
            return {
                'initial_capital': initial_capital,
                'final_equity': final_equity,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,
                'trades': trades,
                'equity_curve': equity_curve
            }
            
        except Exception as e:
            logger.error(f"革命性策略模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🚀 启动P01阿尔法X2024策略革命性重构版测试...")
    
    # 检查数据目录
    data_path = "D:/市场数据/现金/BTCUSDT"
    if not os.path.exists(data_path):
        print(f"❌ 数据目录不存在: {data_path}")
        return
    
    strategy = RevolutionaryAlphaX2024Strategy()
    
    print("\n🔥 革命性重构版特点:")
    print("=" * 60)
    print("  1. 多策略融合 (趋势+均值回归+动量)")
    print("  2. 激进仓位管理 (最高80%仓位)")
    print("  3. 动态止盈止损 (6:1盈亏比)")
    print("  4. 市场状态自适应")
    print("  5. 强制交易频率保证")
    print("=" * 60)
    
    # 测试2024年4月
    try:
        data = strategy.load_historical_data('2024-04-01', '2024-04-30')
        result = strategy.simulate_revolutionary_strategy(data)
        
        if result:
            # 计算年化指标
            total_days = 30
            annual_return = (1 + result['total_return']) ** (365 / total_days) - 1
            
            print(f"\n📊 革命性重构版测试结果:")
            print("-" * 50)
            print(f"总收益率: {result['total_return']*100:+.2f}%")
            print(f"年化收益率: {annual_return*100:+.2f}%")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {result['sharpe_ratio']:.2f}")
            print(f"交易次数: {result['total_trades']}")
            print(f"胜率: {result['win_rate']*100:.1f}%")
            print(f"盈亏比: {result['profit_loss_ratio']:.2f}")
            
            # 客户目标达成检查
            print(f"\n🎯 客户目标达成检查:")
            print("-" * 40)
            annual_ok = annual_return >= 0.15
            sharpe_ok = result['sharpe_ratio'] >= 2.0
            drawdown_ok = result['max_drawdown'] <= 0.15
            
            print(f"年化收益率: {annual_return*100:+.2f}% {'✅' if annual_ok else '❌'} (目标: ≥15%)")
            print(f"夏普比率: {result['sharpe_ratio']:.2f} {'✅' if sharpe_ok else '❌'} (目标: ≥2.0)")
            print(f"最大回撤: {result['max_drawdown']*100:.2f}% {'✅' if drawdown_ok else '❌'} (目标: ≤15%)")
            
            targets_met = sum([annual_ok, sharpe_ok, drawdown_ok])
            
            print(f"\n🏆 革命性重构版评价:")
            if targets_met == 3:
                print("🎉 完全达标! 革命性改进成功!")
            elif targets_met == 2:
                print("✅ 显著改进! 大幅接近客户目标!")
            elif targets_met == 1:
                print("⚠️ 有所改进! 但仍需进一步优化!")
            else:
                print("❌ 改进不足! 需要更激进的方案!")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

if __name__ == "__main__":
    main()
