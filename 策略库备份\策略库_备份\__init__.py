# -*- coding: utf-8 -*-
# 暴露此包内的主要类、常量和数据结构

# Import base strategy class and alias it as TradingStrategy
from .事件驱动策略基类 import EventDrivenStrategy as TradingStrategy

# Import specific strategies from the core strategies file
from .core_strategies import MeanReversionStrategy, TrendFollowingStrategy

# Import the STRATEGIES dictionary from 策略库.py
from .策略库 import STRATEGIES

__all__ = [
    'TradingStrategy',
    'MeanReversionStrategy',
    'TrendFollowingStrategy',
    'STRATEGIES',
    # BacktestResult            # 回测结果数据类 - assuming it's in 策略库.py or elsewhere if needed
]
